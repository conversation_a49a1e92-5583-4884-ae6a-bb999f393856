/**
 * Database Singleton Factory
 * 
 * Ensures only one CronJobPersistence instance exists across the entire application.
 * Handles async initialization and provides consistent database access.
 */

const CronJobPersistence = require('./cronJobPersistence');

class DatabaseSingleton {
  static instance = null;
  static initPromise = null;
  static isInitializing = false;
  
  /**
   * Get the singleton database instance
   * Creates and initializes the instance if it doesn't exist
   */
  static async getInstance() {
    if (DatabaseSingleton.instance) {
      return DatabaseSingleton.instance;
    }
    
    // If already initializing, wait for that process to complete
    if (DatabaseSingleton.initPromise) {
      return await DatabaseSingleton.initPromise;
    }
    
    // Start initialization
    DatabaseSingleton.initPromise = DatabaseSingleton.createAndInitialize();
    DatabaseSingleton.instance = await DatabaseSingleton.initPromise;
    
    return DatabaseSingleton.instance;
  }
  
  /**
   * Create and initialize the database instance
   */
  static async createAndInitialize() {
    try {
      DatabaseSingleton.isInitializing = true;
      console.log('🔄 Initializing database singleton...');
      
      const instance = new CronJobPersistence();
      await instance.waitForInitialization();
      
      console.log('✅ Database singleton initialized successfully');
      DatabaseSingleton.isInitializing = false;
      
      return instance;
    } catch (error) {
      DatabaseSingleton.isInitializing = false;
      DatabaseSingleton.initPromise = null; // Reset so we can try again
      console.error('❌ Database singleton initialization failed:', error.message);
      throw error;
    }
  }
  
  /**
   * Check if the database is available
   */
  static isAvailable() {
    return DatabaseSingleton.instance && DatabaseSingleton.instance.isDbAvailable;
  }
  
  /**
   * Check if initialization is in progress
   */
  static isInitializing() {
    return DatabaseSingleton.isInitializing;
  }
  
  /**
   * Reset the singleton (useful for testing)
   */
  static reset() {
    if (DatabaseSingleton.instance) {
      // Close existing connection if possible
      if (typeof DatabaseSingleton.instance.close === 'function') {
        DatabaseSingleton.instance.close().catch(console.error);
      }
    }
    
    DatabaseSingleton.instance = null;
    DatabaseSingleton.initPromise = null;
    DatabaseSingleton.isInitializing = false;
    console.log('🔄 Database singleton reset');
  }
  
  /**
   * Get instance synchronously (throws if not initialized)
   */
  static getInstanceSync() {
    if (!DatabaseSingleton.instance) {
      throw new Error('Database not initialized. Call getInstance() first.');
    }
    return DatabaseSingleton.instance;
  }
  
  /**
   * Initialize the database without returning the instance
   * Useful for startup sequences
   */
  static async initialize() {
    await DatabaseSingleton.getInstance();
  }
}

module.exports = DatabaseSingleton;
