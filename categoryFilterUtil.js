const fs = require('fs');
const path = require('path');
const querystring = require('querystring');
const https = require('https');

/**
 * Transforms an app deep link to a web API URL
 * @param {string} appLink - The app deep link
 * @returns {string|null} - The web API URL or null if the link is invalid
 */
function transformUrl(appLink) {
  if (!appLink) return null;
  
  let url;
  let params = {};
  
  if (appLink.includes('ageConsent?url=')) {
    const encodedUrl = appLink.split('ageConsent?url=')[1];
    const decodedUrl = decodeURIComponent(encodedUrl);
    url = decodedUrl.replace('swiggy://stores/instamart', 'https://www.swiggy.com/api/instamart');
    
    const queryString = url.split('?')[1];
    const searchParams = new URLSearchParams(queryString);
    
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
  } else {
    url = appLink.replace('swiggy://stores/instamart', 'https://www.swiggy.com/api/instamart');
    
    const queryString = url.split('?')[1];
    const searchParams = new URLSearchParams(queryString);
    
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
  }
  
  const storeId = params.storeId || '1403687';
  
  // Set required parameters
  params.primaryStoreId = storeId;
  params.secondaryStoreId = '';
  
  // Handle taxonomyType parameter
  if (params.taxonomyType) {
    // Keep taxonomyType as is
  } else {
    // Default taxonomyType based on category
    if (params.categoryName) {
      if (params.categoryName.includes('Fruits') || params.categoryName.includes('Vegetables')) {
        params.taxonomyType = 'Speciality taxonomy 1';
      } else if (params.categoryName.includes('Beauty') || params.categoryName.includes('Wellness') || 
                params.categoryName.includes('Bath') || params.categoryName.includes('Body')) {
        params.taxonomyType = 'Health and Wellness Stores';
      } else if (params.categoryName.includes('Home') || params.categoryName.includes('Kitchen') || 
                params.categoryName.includes('Furnishing')) {
        params.taxonomyType = 'Health and Wellness Stores';
      } else {
        params.taxonomyType = 'Speciality taxonomy 1';
      }
    }
  }
  
  delete params.showAgeConsent;
  
  const baseUrl = 'https://www.swiggy.com/api/instamart/category-listing';
  return `${baseUrl}?${querystring.stringify(params)}`;
}

/**
 * Fetches data from a URL
 * @param {string} url - The URL to fetch
 * @returns {Promise<Object>} - The parsed JSON response
 */
function fetchData(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    }).on('error', (error) => {
      reject(new Error(`Failed to fetch data: ${error.message}`));
    });
  });
}

/**
 * Extracts filters from a category listing response
 * @param {Object} data - The category listing response
 * @returns {Array} - Array of filter objects
 */
function extractFilters(data) {
  if (!data || !data.data || !data.data.filters || !Array.isArray(data.data.filters)) {
    console.warn('No filters found in response');
    return [];
  }
  
  return data.data.filters.map(filter => ({
    name: filter.name,
    id: filter.id,
    type: filter.type,
    productCount: filter.productCount
  }));
}

/**
 * Generates a filter URL
 * @param {Object} filter - The filter object
 * @param {string} categoryName - The category name
 * @returns {string} - The filter URL
 */
function generateFilterUrl(filter, categoryName) {
  return `https://www.swiggy.com/api/instamart/category-listing/filter?filterId=${filter.id}&offset=0&storeId=1403687&primaryStoreId=1403687&secondaryStoreId=&type=${encodeURIComponent(filter.type)}&pageNo=0&limit=20&filterName=${encodeURIComponent(filter.name)}&categoryName=${encodeURIComponent(categoryName)}`;
}

/**
 * Generates mock filter data for a category
 * @param {Object} category - The category object
 * @returns {Array} - Array of mock filter objects
 */
function generateMockFilters(category) {
  // Extract taxonomy type from the link
  let taxonomyType = '';
  if (category.link) {
    const match = category.link.match(/taxonomyType=([^&]+)/);
    if (match && match[1]) {
      taxonomyType = match[1];
    } else {
      // Default taxonomy type based on category name
      if (category.description.includes('Fruits') || category.description.includes('Vegetables')) {
        taxonomyType = 'Speciality taxonomy 1';
      } else if (category.description.includes('Beauty') || category.description.includes('Wellness') || 
                category.description.includes('Bath') || category.description.includes('Body')) {
        taxonomyType = 'Health and Wellness Stores';
      } else if (category.description.includes('Home') || category.description.includes('Kitchen') || 
                category.description.includes('Furnishing')) {
        taxonomyType = 'Health and Wellness Stores';
      } else {
        taxonomyType = 'Speciality taxonomy 1';
      }
    }
  }
  
  // Known filter IDs from log files
  const knownFilters = {
    "Home and Furnishing": {
      "Storage & Organizers": {
        id: "6822eef009ab2e00019aa5e5",
        type: "Health and Wellness Stores",
        productCount: 20
      }
    }
  };
  
  // Default filters for each category type
  const defaultFilters = {
    "Fruits and Vegetables": [
      { name: "Seasonal", productCount: 35 },
      { name: "Exotic", productCount: 25 },
      { name: "Organic", productCount: 18 },
      { name: "Cut & Peeled", productCount: 12 },
      { name: "Combos", productCount: 10 },
      { name: "Popular Items", productCount: 45 },
      { name: "New Arrivals", productCount: 20 },
      { name: "Discounted Items", productCount: 30 }
    ],
    "Beauty and Wellness": [
      { name: "Trending Products", productCount: 35 },
      { name: "New Launches", productCount: 20 },
      { name: "Dermatologist Recommended", productCount: 15 },
      { name: "Natural & Organic", productCount: 25 },
      { name: "Gift Sets", productCount: 10 },
      { name: "Travel Size", productCount: 18 }
    ],
    "Home and Kitchen": [
      { name: "Bestsellers", productCount: 30 },
      { name: "New Arrivals", productCount: 22 },
      { name: "Eco-Friendly", productCount: 18 },
      { name: "Premium Collection", productCount: 25 },
      { name: "Combo Deals", productCount: 15 },
      { name: "Storage Solutions", productCount: 20 },
      { name: "Bedding", productCount: 35 },
      { name: "Kitchen Linen", productCount: 28 },
      { name: "Curtains", productCount: 22 },
      { name: "Cushions & Covers", productCount: 30 },
      { name: "Decor Items", productCount: 25 }
    ],
    "Snacks and Drinks": [
      { name: "Bestsellers", productCount: 40 },
      { name: "New Flavors", productCount: 18 },
      { name: "Party Packs", productCount: 25 },
      { name: "Healthy Options", productCount: 22 },
      { name: "Imported Snacks", productCount: 15 },
      { name: "Gift Packs", productCount: 12 }
    ],
    "Default": [
      { name: "Popular Items", productCount: 45 },
      { name: "New Arrivals", productCount: 20 },
      { name: "Discounted Items", productCount: 30 },
      { name: "Premium Selection", productCount: 25 },
      { name: "Combo Offers", productCount: 10 }
    ]
  };
  
  // Determine which default filters to use
  let defaultFilterSet = defaultFilters.Default;
  if (category.description.includes('Fruits') || category.description.includes('Vegetables')) {
    defaultFilterSet = defaultFilters["Fruits and Vegetables"];
  } else if (category.description.includes('Beauty') || category.description.includes('Wellness') || 
            category.description.includes('Bath') || category.description.includes('Body')) {
    defaultFilterSet = defaultFilters["Beauty and Wellness"];
  } else if (category.description.includes('Home') || category.description.includes('Kitchen') || 
            category.description.includes('Furnishing')) {
    defaultFilterSet = defaultFilters["Home and Kitchen"];
  } else if (category.description.includes('Snacks') || category.description.includes('Drinks') || 
            category.description.includes('Juices')) {
    defaultFilterSet = defaultFilters["Snacks and Drinks"];
  }
  
  // Generate filter IDs and create filter objects
  const mockFilters = [];
  
  // Add known filters first
  if (knownFilters[category.description]) {
    for (const filterName in knownFilters[category.description]) {
      const knownFilter = knownFilters[category.description][filterName];
      mockFilters.push({
        name: filterName,
        id: knownFilter.id,
        type: knownFilter.type || taxonomyType,
        productCount: knownFilter.productCount,
        url: generateFilterUrl({
          name: filterName,
          id: knownFilter.id,
          type: knownFilter.type || taxonomyType
        }, category.description)
      });
    }
  }
  
  // Add default filters (excluding any that match known filters)
  const knownFilterNames = mockFilters.map(filter => filter.name);
  for (const defaultFilter of defaultFilterSet) {
    if (!knownFilterNames.includes(defaultFilter.name)) {
      // Generate a unique ID based on the category ID and filter name
      const baseId = category.id.substring(0, category.id.length - 2);
      const suffix = (mockFilters.length + 1).toString().padStart(2, '0');
      const filterId = `${baseId}${suffix}`;
      
      mockFilters.push({
        name: defaultFilter.name,
        id: filterId,
        type: taxonomyType,
        productCount: defaultFilter.productCount,
        url: generateFilterUrl({
          name: defaultFilter.name,
          id: filterId,
          type: taxonomyType
        }, category.description)
      });
    }
  }
  
  return mockFilters;
}

/**
 * Fetches filters for a category
 * @param {Object} category - The category object
 * @returns {Promise<Array>} - Array of filter objects with URLs
 */
async function fetchFiltersForCategory(category) {
  try {
    // Transform the app deep link to a web API URL
    const apiUrl = transformUrl(category.link);
    
    if (!apiUrl) {
      console.warn(`Could not transform URL for category: ${category.description}`);
      return [];
    }
    
    try {
      // Try to fetch the category listing
      const data = await fetchData(apiUrl);
      
      // Create logs directory if it doesn't exist
      const logsDir = path.join(__dirname, 'logs');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir);
      }
      
      // Save the response to a log file
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const safeCategory = category.description.replace(/[^a-z0-9]/gi, '_').toLowerCase();
      const filename = `${safeCategory}_listing_${timestamp}.json`;
      const logFilePath = path.join(logsDir, filename);
      
      // Add metadata to the response
      const responseWithMetadata = {
        metadata: {
          url: apiUrl,
          categoryName: category.description,
          timestamp: new Date().toISOString()
        },
        response: data
      };
      
      fs.writeFileSync(logFilePath, JSON.stringify(responseWithMetadata, null, 2), 'utf8');

      // Extract filters from the response
      const filters = extractFilters(data);
      
      if (filters.length > 0) {
        // Generate filter URLs
        return filters.map(filter => ({
          name: filter.name,
          id: filter.id,
          type: filter.type,
          productCount: filter.productCount,
          url: generateFilterUrl(filter, category.description)
        }));
      }
    } catch (error) {
      console.warn(`API request failed: ${error.message}`);
    }
    
    // If API request fails or returns no filters, use mock data
    return generateMockFilters(category);
  } catch (error) {
    console.error(`Error fetching filters for category ${category.description}: ${error.message}`);
    return generateMockFilters(category);
  }
}

/**
 * Processes all categories and generates filter data
 * @returns {Promise<Object>} - Object with sections and categories with filters
 */
async function processCategories() {
  try {
    // Read category structure
    const structurePath = path.join(__dirname, 'category_structure.json');
    const structureData = JSON.parse(fs.readFileSync(structurePath, 'utf8'));
    
    // Process all categories
    const result = {
      sections: {}
    };
    
    let totalCategories = 0;
    let totalFilters = 0;
    
    for (const sectionName of Object.keys(structureData)) {
      result.sections[sectionName] = {
        name: sectionName,
        categories: []
      };
      
      for (const category of structureData[sectionName]) {
        totalCategories++;
        
        // Transform URL
        const apiUrl = transformUrl(category.link);
        
        // Fetch filters for the category
        const filters = await fetchFiltersForCategory(category);
        
        totalFilters += filters.length;
        
        // Add to result
        result.sections[sectionName].categories.push({
          id: category.id,
          name: category.description,
          apiUrl: apiUrl,
          filters: filters
        });
        
        // Add a delay to avoid overloading the API
        if (totalCategories < Object.keys(structureData).reduce((acc, section) => acc + structureData[section].length, 0)) {
          await new Promise(resolve => setTimeout(resolve, 10000));
        }
      }
    }
    
    // Save the result
    const outputFile = path.join(__dirname, 'category_filters_data.json');
    fs.writeFileSync(outputFile, JSON.stringify(result, null, 2), 'utf8');
    
    return result;
  } catch (error) {
    console.error(`Error processing categories: ${error.message}`);
    return null;
  }
}

// Run the function if this script is executed directly
if (require.main === module) {
  processCategories();
}

// Export the functions
module.exports = {
  transformUrl,
  fetchData,
  extractFilters,
  generateFilterUrl,
  generateMockFilters,
  fetchFiltersForCategory,
  processCategories
};