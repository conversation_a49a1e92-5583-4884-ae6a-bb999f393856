/**
 * Telegram <PERSON><PERSON>
 * 
 * Ensures only one Telegram bot instance is running across the entire application.
 * Prevents multiple bot conflicts and 409 errors.
 */

const TelegramBot = require('node-telegram-bot-api');
const fs = require('fs');
const path = require('path');

class BotSingleton {
  static instance = null;
  static isStarted = false;
  static startPromise = null;
  
  /**
   * Get the singleton bot instance
   */
  static getInstance() {
    if (!BotSingleton.instance) {
      // Load configuration
      const config = JSON.parse(fs.readFileSync(path.join(__dirname, 'config.json'), 'utf8'));
      
      // Bot configuration
      const BOT_CONFIG = {
        token: process.env.TELEGRAM_BOT_TOKEN || '**********************************************',
        maxItemsPerMessage: 5,
        maxMessageLength: 4000
      };
      
      // Create bot instance (don't start polling immediately)
      BotSingleton.instance = new TelegramBot(BOT_CONFIG.token, { polling: false });
      
      // Handle polling errors
      BotSingleton.instance.on('polling_error', (error) => {
        // Only log non-409 errors to reduce noise
        if (!error.message.includes('409') && !error.message.includes('Conflict')) {
          console.error('Polling error:', error);
        } else {
          console.log('⚠️ Bot conflict detected - another instance may be running');
        }
      });
      
      console.log('🤖 Bot singleton instance created');
    }
    
    return BotSingleton.instance;
  }
  
  /**
   * Start the bot (only if not already started)
   */
  static async startBot() {
    if (BotSingleton.isStarted) {
      console.log('ℹ️ Bot is already running');
      return BotSingleton.instance;
    }
    
    if (BotSingleton.startPromise) {
      console.log('ℹ️ Bot is already starting, waiting...');
      return await BotSingleton.startPromise;
    }
    
    BotSingleton.startPromise = BotSingleton._doStartBot();
    return await BotSingleton.startPromise;
  }
  
  /**
   * Internal method to actually start the bot
   */
  static async _doStartBot() {
    try {
      const bot = BotSingleton.getInstance();
      
      // Stop any existing polling first
      await bot.stopPolling();
      
      // Wait a moment
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Start polling
      await bot.startPolling();
      
      BotSingleton.isStarted = true;
      console.log('🤖 Telegram bot started successfully via singleton!');
      
      return bot;
    } catch (error) {
      BotSingleton.startPromise = null;
      console.error('❌ Error starting Telegram bot:', error);
      throw error;
    }
  }
  
  /**
   * Stop the bot
   */
  static async stopBot() {
    if (BotSingleton.instance && BotSingleton.isStarted) {
      try {
        await BotSingleton.instance.stopPolling();
        BotSingleton.isStarted = false;
        console.log('🛑 Bot stopped successfully');
      } catch (error) {
        console.error('❌ Error stopping bot:', error);
      }
    }
  }
  
  /**
   * Check if bot is running
   */
  static isRunning() {
    return BotSingleton.isStarted;
  }
  
  /**
   * Reset the singleton (for testing)
   */
  static reset() {
    if (BotSingleton.instance && BotSingleton.isStarted) {
      BotSingleton.instance.stopPolling().catch(console.error);
    }
    
    BotSingleton.instance = null;
    BotSingleton.isStarted = false;
    BotSingleton.startPromise = null;
    console.log('🔄 Bot singleton reset');
  }
  
  /**
   * Get bot configuration
   */
  static getBotConfig() {
    return {
      token: process.env.TELEGRAM_BOT_TOKEN || '**********************************************',
      maxItemsPerMessage: 5,
      maxMessageLength: 4000
    };
  }
}

module.exports = BotSingleton;
