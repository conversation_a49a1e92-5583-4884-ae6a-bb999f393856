# Swiggy Deals Dashboard

A comprehensive React-based frontend dashboard for monitoring and managing your Swiggy deals cron jobs with detailed product tracking and analytics.

## 🌟 Features

### 📊 **Dashboard Overview**
- **Real-time statistics** - Total jobs, active jobs, executions, items found
- **Success rate monitoring** - Visual progress indicators
- **Recent activity feed** - Latest cron job executions
- **Quick action shortcuts** - Easy navigation to key features

### ⏰ **Cron Job Management**
- **Complete job listing** - All your automated deal tracking jobs
- **Real-time status** - Running, stopped, error states with visual indicators
- **Advanced filtering** - Search by code, filter by status
- **Detailed job cards** - Schedule type, success rate, next run time
- **Execution history** - Track performance over time

### 🛍️ **Product Tracking**
- **Comprehensive product cards** - Images, prices, discounts, stock status
- **Price history charts** - Visual price trend analysis using Recharts
- **Last 5 distinct prices** - Detailed pricing table with timestamps
- **Stock monitoring** - Real-time availability tracking
- **Direct Swiggy links** - One-click access to product pages

### 📈 **Analytics & Insights**
- **Interactive price charts** - Line graphs showing price trends over time
- **Discount analysis** - Color-coded discount percentages
- **Performance metrics** - Success rates, execution counts
- **Historical data** - Complete tracking history

## 🏗️ Architecture

### **Backend API** (`/api/server.js`)
- **Express.js server** - RESTful API endpoints
- **PostgreSQL integration** - Persistent data storage
- **Real-time data** - Live cron job status and results
- **CORS enabled** - Cross-origin resource sharing

### **Frontend** (`/frontend/`)
- **React 18** - Modern React with hooks
- **React Router** - Client-side routing
- **Tailwind CSS** - Utility-first styling
- **Recharts** - Interactive data visualization
- **Lucide React** - Beautiful icons
- **Axios** - HTTP client with interceptors

## 🚀 Quick Start

### **1. Setup**
```bash
# Make setup script executable
chmod +x setup-frontend.sh

# Run setup
./setup-frontend.sh
```

### **2. Development**
```bash
# Start both API and frontend
npm run dev

# Or start individually
npm run api      # API server (port 3001)
npm run frontend # React app (port 3000)
npm run bot      # Telegram bot
```

### **3. Production**
```bash
# Build frontend
npm run build-frontend

# Start API server
npm run api
```

## 📱 Pages & Components

### **Pages**
- **Dashboard** (`/`) - Overview and statistics
- **Cron Jobs** (`/cron-jobs`) - Job management and monitoring
- **Cron Job Details** (`/cron-jobs/:jobId`) - Detailed execution history
- **Product Details** (`/products/:productId`) - Price history and analytics

### **Components**
- **Layout** - Navigation and responsive design
- **ProductCard** - Reusable product display component
- **StatCard** - Dashboard statistics cards
- **LoadingSpinner** - Loading states

## 🎨 Design System

### **Colors**
- **Primary** - Orange theme (`#f97316`) matching Swiggy branding
- **Success** - Green for positive states
- **Warning** - Yellow for attention states
- **Danger** - Red for error states

### **Typography**
- **Inter font** - Clean, modern typography
- **Responsive text** - Scales across device sizes

### **Components**
- **Cards** - Consistent white backgrounds with subtle shadows
- **Buttons** - Primary and secondary variants
- **Badges** - Status indicators with color coding
- **Tables** - Clean data presentation

## 🔌 API Endpoints

### **Cron Jobs**
- `GET /api/cron-jobs` - List all cron jobs
- `GET /api/cron-jobs/:jobId/results` - Get job execution results

### **Products**
- `GET /api/products/:productId/history` - Get product price history

### **Dashboard**
- `GET /api/dashboard/stats` - Get dashboard statistics

## 📊 Data Flow

1. **Telegram Bot** creates and manages cron jobs
2. **PostgreSQL Database** stores job data and execution history
3. **API Server** serves data to frontend
4. **React Frontend** displays real-time information
5. **Log Files** store detailed execution results

## 🎯 Key Features Explained

### **Smart Product Tracking**
- **Duplicate Prevention** - Only shows deals once unless discount changes
- **Price History** - Tracks last 5 distinct prices automatically
- **Stock Monitoring** - Real-time availability status
- **Image Handling** - Graceful fallbacks for missing images

### **Real-time Updates**
- **Auto-refresh** - Dashboard updates every 30 seconds
- **Live status** - Cron job states update automatically
- **Responsive design** - Works on desktop, tablet, and mobile

### **Advanced Filtering**
- **Search functionality** - Find jobs by code or ID
- **Status filtering** - Filter by running, stopped, error states
- **Date-based sorting** - Most recent activity first

## 🔧 Configuration

### **Environment Variables**
```bash
# API Configuration
API_PORT=3001                    # API server port
REACT_APP_API_URL=/api          # Frontend API base URL

# Database (inherited from main app)
DATABASE_URL=postgresql://...    # PostgreSQL connection string
```

### **Customization**
- **Colors** - Modify `tailwind.config.js` for theme changes
- **API endpoints** - Update `services/api.js` for custom endpoints
- **Components** - Extend or modify existing components

## 🚀 Deployment

### **Railway Deployment (Recommended)**
The app is fully configured for Railway deployment with Docker:

1. **Push to Railway:**
   ```bash
   # Connect your repo to Railway
   # Railway will automatically detect Dockerfile and build
   ```

2. **Environment Variables:**
   ```bash
   NODE_ENV=production
   DATABASE_URL=postgresql://...
   TELEGRAM_BOT_TOKEN=your_bot_token
   PORT=3000  # Railway will set this automatically
   ```

3. **Automatic Features:**
   - ✅ **Docker build** - Uses optimized Dockerfile
   - ✅ **Frontend build** - React app built and served
   - ✅ **API server** - Express.js backend
   - ✅ **Telegram bot** - Runs concurrently
   - ✅ **Health checks** - Monitors `/api/dashboard/stats`
   - ✅ **Auto-restart** - On failure with retries

### **Manual Deployment**
1. Build the frontend: `npm run build-frontend`
2. Start production: `npm run railway`
3. Access at: `http://localhost:3001`

### **Local Development**
```bash
# Start both API and frontend
npm run dev

# Or individually
npm run api      # Backend only
npm run frontend # Frontend only
npm run bot      # Telegram bot only
```

## 🎉 Benefits

### **For Users**
✅ **Visual monitoring** - See all cron jobs at a glance  
✅ **Product insights** - Detailed price history and trends  
✅ **Easy management** - Intuitive interface for job monitoring  
✅ **Mobile friendly** - Responsive design works everywhere  

### **For Developers**
✅ **Modern stack** - React 18, Tailwind CSS, Express.js  
✅ **Scalable architecture** - Clean separation of concerns  
✅ **Extensible** - Easy to add new features and pages  
✅ **Well documented** - Clear code structure and comments  

## 📈 Future Enhancements

- **Real-time notifications** - WebSocket integration for live updates
- **Advanced analytics** - More detailed charts and insights
- **User management** - Multi-user support with authentication
- **Export functionality** - Download reports and data
- **Mobile app** - React Native version

Your Swiggy deals monitoring just got a major upgrade! 🎊
