# Use Node.js 18 LTS
FROM node:18-alpine

# Install git (needed for some npm packages)
RUN apk add --no-cache git

# Set working directory
WORKDIR /app

# Copy backend package files and install dependencies
COPY package*.json ./
RUN npm install --omit=dev

# Copy all source code
COPY . .

# Install frontend dependencies and build
WORKDIR /app/frontend

# Copy frontend package.json
COPY frontend/package.json ./

# Install frontend dependencies (use install instead of ci since we might not have lock file)
RUN npm install --omit=dev

# Copy frontend source
COPY frontend/ .

# Build frontend
RUN npm run build

# Go back to app root and clean up
WORKDIR /app
RUN rm -rf frontend/node_modules frontend/src frontend/public

# Expose port
EXPOSE $PORT

# Start the application
CMD ["npm", "run", "railway"]
