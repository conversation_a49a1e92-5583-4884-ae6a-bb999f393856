#!/usr/bin/env node

/**
 * Main Application Entry Point
 * 
 * Clean, modular startup with proper error handling
 */

const { getInstance } = require('./src/Application');

async function main() {
  console.log('🚀 Starting Grocery Deal Finder Application...');
  console.log('📅 Version: 2.0.0 (Modular Architecture)');
  console.log('🕐 Started at:', new Date().toISOString());
  console.log('');

  try {
    // Get application instance
    const app = getInstance();

    // Initialize application
    await app.initialize();

    console.log('');
    console.log('🎉 Application started successfully!');
    console.log('');
    console.log('📊 Available Services:');
    console.log('  🤖 Telegram Bot - Automated deal notifications');
    console.log('  🌐 API Server - REST API for frontend');
    console.log('  ⏰ Cron Jobs - Scheduled deal discovery');
    console.log('  🔍 Deal Discovery - Multi-API deal finding');
    console.log('  📊 Price Gap Finder - Max Saver analysis');
    console.log('  🧹 Cleanup Service - Automated maintenance');
    console.log('');

    // Log health status
    const health = await app.healthCheck();
    console.log('💚 Health Status:', health.status);
    
    if (health.status === 'healthy') {
      console.log('✅ All systems operational');
    } else {
      console.log('⚠️ Health issues detected:', health.reason);
    }

  } catch (error) {
    console.error('');
    console.error('💥 Failed to start application:');
    console.error('❌ Error:', error.message);
    console.error('📍 Stack:', error.stack);
    console.error('');
    
    process.exit(1);
  }
}

// Handle CLI arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Grocery Deal Finder - Modular Architecture

Usage: node main.js [options]

Options:
  --help, -h          Show this help message
  --version, -v       Show version information
  --health            Check application health
  --stats             Show application statistics
  --config            Show current configuration

Environment Variables:
  NODE_ENV            Environment (development, production, test)
  TELEGRAM_BOT_TOKEN  Telegram bot token
  DATABASE_URL        PostgreSQL connection string
  PORT                API server port (default: 3001)
  LOG_LEVEL           Logging level (debug, info, warn, error)

Examples:
  node main.js                    # Start application
  node main.js --health           # Check health status
  node main.js --stats            # Show statistics
  NODE_ENV=production node main.js # Start in production mode
  `);
  process.exit(0);
}

if (args.includes('--version') || args.includes('-v')) {
  console.log('Grocery Deal Finder v2.0.0 (Modular Architecture)');
  process.exit(0);
}

if (args.includes('--health')) {
  (async () => {
    try {
      const app = getInstance();
      await app.initialize();
      const health = await app.healthCheck();
      console.log(JSON.stringify(health, null, 2));
      process.exit(health.status === 'healthy' ? 0 : 1);
    } catch (error) {
      console.error('Health check failed:', error.message);
      process.exit(1);
    }
  })();
  return;
}

if (args.includes('--stats')) {
  (async () => {
    try {
      const app = getInstance();
      await app.initialize();
      const stats = await app.getStats();
      console.log(JSON.stringify(stats, null, 2));
      process.exit(0);
    } catch (error) {
      console.error('Stats retrieval failed:', error.message);
      process.exit(1);
    }
  })();
  return;
}

if (args.includes('--config')) {
  (async () => {
    try {
      const app = getInstance();
      const config = app.getService('config');
      console.log(JSON.stringify(config.getAll(), null, 2));
      process.exit(0);
    } catch (error) {
      console.error('Config retrieval failed:', error.message);
      process.exit(1);
    }
  })();
  return;
}

// Start the application
main().catch(error => {
  console.error('💥 Unhandled error in main:', error);
  process.exit(1);
});
