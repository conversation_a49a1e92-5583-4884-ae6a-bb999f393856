const util = require('./categoryFilterUtil.js');
const fs = require('fs');
const path = require('path');

async function updateHomeAndFurnishing() {
  try {
    // Read the existing category_filters_data.json file
    const filtersDataPath = path.join(__dirname, 'category_filters_data.json');
    const filtersData = JSON.parse(fs.readFileSync(filtersDataPath, 'utf8'));
    
    // Read the category structure
    const structurePath = path.join(__dirname, 'category_structure.json');
    const structureData = JSON.parse(fs.readFileSync(structurePath, 'utf8'));
    
    // Find the Home and Furnishing category
    let homeCategory = null;
    let homeSectionName = null;
    
    for (const sectionName in structureData) {
      for (const category of structureData[sectionName]) {
        if (category.description === 'Home and Furnishing') {
          homeCategory = category;
          homeSectionName = sectionName;
          break;
        }
      }
      if (homeCategory) break;
    }
    
    if (homeCategory && homeSectionName) {
      console.log('Found Home and Furnishing category in section:', homeSectionName);
      
      // Transform URL
      const apiUrl = util.transformUrl(homeCategory.link);
      
      // Fetch filters for the category
      const filters = await util.fetchFiltersForCategory(homeCategory);
      console.log(`Found ${filters.length} filters for Home and Furnishing`);
      
      // Update the filters in the filtersData object
      for (const sectionName in filtersData.sections) {
        if (sectionName === homeSectionName) {
          for (let i = 0; i < filtersData.sections[sectionName].categories.length; i++) {
            const category = filtersData.sections[sectionName].categories[i];
            if (category.name === 'Home and Furnishing') {
              console.log('Updating filters for Home and Furnishing in category_filters_data.json');
              filtersData.sections[sectionName].categories[i].filters = filters;
              break;
            }
          }
        }
      }
      
      // Save the updated filtersData back to the file
      fs.writeFileSync(filtersDataPath, JSON.stringify(filtersData, null, 2), 'utf8');
      console.log('Successfully updated category_filters_data.json');
      
      // Verify the update
      const updatedFiltersData = JSON.parse(fs.readFileSync(filtersDataPath, 'utf8'));
      let updatedFilters = null;
      
      for (const sectionName in updatedFiltersData.sections) {
        for (const category of updatedFiltersData.sections[sectionName].categories) {
          if (category.name === 'Home and Furnishing') {
            updatedFilters = category.filters;
            break;
          }
        }
        if (updatedFilters) break;
      }
      
      if (updatedFilters) {
        console.log('Verification: Found updated filters for Home and Furnishing:');
        updatedFilters.forEach(filter => {
          console.log(`${filter.name}: ${filter.id}`);
        });
        
        const storageOrganizerFilter = updatedFilters.find(f => f.name === 'Storage & Organizers');
        if (storageOrganizerFilter) {
          console.log('\nStorage & Organizers filter ID:', storageOrganizerFilter.id);
          console.log('Expected ID: 6822eef009ab2e00019aa5e5');
          console.log('Match:', storageOrganizerFilter.id === '6822eef009ab2e00019aa5e5' ? 'Yes ✓' : 'No ✗');
        } else {
          console.log('Storage & Organizers filter not found in updated data');
        }
      } else {
        console.log('Verification failed: Could not find Home and Furnishing in updated data');
      }
    } else {
      console.log('Home and Furnishing category not found in structure');
    }
  } catch (error) {
    console.error('Error updating Home and Furnishing:', error.message);
  }
}

updateHomeAndFurnishing();