const fs = require('fs');
const path = require('path');
const https = require('https');
const zlib = require('zlib');
const querystring = require('querystring');

// Load configuration
const config = JSON.parse(fs.readFileSync(path.join(__dirname, 'config.json'), 'utf8'));

/**
 * Transforms an app deep link to a web API URL
 * @param {string} appLink - The app deep link
 * @returns {string|null} - The web API URL or null if the link is invalid
 */
function transformUrl(appLink) {
  if (!appLink) return null;
  
  let url;
  let params = {};
  
  if (appLink.includes('ageConsent?url=')) {
    const encodedUrl = appLink.split('ageConsent?url=')[1];
    const decodedUrl = decodeURIComponent(encodedUrl);
    url = decodedUrl.replace('swiggy://stores/instamart', 'https://www.swiggy.com/api/instamart');
    
    const queryString = url.split('?')[1];
    const searchParams = new URLSearchParams(queryString);
    
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
  } else {
    url = appLink.replace('swiggy://stores/instamart', 'https://www.swiggy.com/api/instamart');
    
    const queryString = url.split('?')[1];
    const searchParams = new URLSearchParams(queryString);
    
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
  }
  
  const storeId = params.storeId || '1403687';
  
  // Set required parameters
  params.primaryStoreId = storeId;
  params.secondaryStoreId = '';
  
  // Handle taxonomyType parameter
  if (params.taxonomyType) {
    // Keep taxonomyType as is
  } else {
    // Default taxonomyType based on category
    if (params.categoryName) {
      if (params.categoryName.includes('Fruits') || params.categoryName.includes('Vegetables')) {
        params.taxonomyType = 'Speciality taxonomy 1';
      } else if (params.categoryName.includes('Beauty') || params.categoryName.includes('Wellness') || 
                params.categoryName.includes('Bath') || params.categoryName.includes('Body')) {
        params.taxonomyType = 'Health and Wellness Stores';
      } else if (params.categoryName.includes('Home') || params.categoryName.includes('Kitchen') || 
                params.categoryName.includes('Furnishing')) {
        params.taxonomyType = 'Health and Wellness Stores';
      } else {
        params.taxonomyType = 'Speciality taxonomy 1';
      }
    }
  }
  
  delete params.showAgeConsent;
  
  const baseUrl = 'https://www.swiggy.com/api/instamart/category-listing';
  return `${baseUrl}?${querystring.stringify(params)}`;
}

/**
 * Fetches data from a URL with proper headers and compression handling
 * @param {string} url - The URL to fetch
 * @returns {Promise<Object>} - The parsed JSON response
 */
function fetchData(url) {
  return new Promise((resolve, reject) => {
    console.log(`Fetching data from: ${url}`);

    const options = {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.swiggy.com/',
        'sec-ch-ua': '"Google Chrome";v="91", "Chromium";v="91", ";Not A Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin'
      }
    };

    https.get(url, options, (res) => {
      const chunks = [];

      res.on('data', (chunk) => {
        chunks.push(chunk);
      });

      res.on('end', () => {
        try {
          let buffer = Buffer.concat(chunks);

          // Handle compression
          if (res.headers['content-encoding'] === 'gzip') {
            buffer = zlib.gunzipSync(buffer);
          } else if (res.headers['content-encoding'] === 'deflate') {
            buffer = zlib.inflateSync(buffer);
          } else if (res.headers['content-encoding'] === 'br') {
            buffer = zlib.brotliDecompressSync(buffer);
          }

          const data = buffer.toString('utf8');
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          console.error(`Failed to parse JSON: ${error.message}`);
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    }).on('error', (error) => {
      reject(new Error(`Failed to fetch data: ${error.message}`));
    });
  });
}

/**
 * Extracts filters from a category listing response
 * @param {Object} data - The category listing response
 * @returns {Array} - Array of filter objects
 */
function extractFilters(data) {
  if (!data || !data.data || !data.data.filters || !Array.isArray(data.data.filters)) {
    console.warn('No filters found in response');
    return [];
  }

  return data.data.filters.map(filter => ({
    name: filter.name,
    id: filter.id,
    type: filter.type,
    productCount: filter.productCount,
    imageId: filter.imageId || null
  }));
}

/**
 * Generates a filter URL
 * @param {Object} filter - The filter object
 * @param {string} categoryName - The category name
 * @returns {string} - The filter URL
 */
function generateFilterUrl(filter, categoryName) {
  return `https://www.swiggy.com/api/instamart/category-listing/filter?filterId=${filter.id}&offset=0&storeId=1403687&primaryStoreId=1403687&secondaryStoreId=&type=${encodeURIComponent(filter.type)}&pageNo=0&limit=20&filterName=${encodeURIComponent(filter.name)}&categoryName=${encodeURIComponent(categoryName)}`;
}

/**
 * Generates fallback filter data for a category when API fails
 * @param {Object} category - The category object
 * @returns {Array} - Array of fallback filter objects
 */
function generateFallbackFilters(category) {
  console.log(`Generating fallback filters for category: ${category.description}`);

  // Extract taxonomy type from the link
  let taxonomyType = '';
  if (category.link) {
    const match = category.link.match(/taxonomyType=([^&]+)/);
    if (match && match[1]) {
      taxonomyType = decodeURIComponent(match[1]);
    }
  }

  // Return empty array as fallback - we should always try to get real data from API
  console.log(`No fallback filters available for category: ${category.description}`);
  return [];
}

/**
 * Fetches filters for a category
 * @param {Object} category - The category object
 * @returns {Promise<Array>} - Array of filter objects with URLs
 */
async function fetchFiltersForCategory(category) {
  try {
    // Transform the app deep link to a web API URL
    const apiUrl = transformUrl(category.link);

    if (!apiUrl) {
      console.warn(`Could not transform URL for category: ${category.description}`);
      return [];
    }

    console.log(`Fetching filters for category: ${category.description}`);

    try {
      // Try to fetch the category listing
      const data = await fetchData(apiUrl);

      // Create logs directory if it doesn't exist
      const logsDir = path.join(__dirname, 'logs');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir);
      }

      // Save the response to a log file
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const safeCategory = category.description.replace(/[^a-z0-9]/gi, '_').toLowerCase();
      const filename = `${safeCategory}_listing_${timestamp}.json`;
      const logFilePath = path.join(logsDir, filename);

      // Add metadata to the response
      const responseWithMetadata = {
        metadata: {
          url: apiUrl,
          categoryName: category.description,
          timestamp: new Date().toISOString()
        },
        response: data
      };

      fs.writeFileSync(logFilePath, JSON.stringify(responseWithMetadata, null, 2), 'utf8');
      console.log(`Saved response to: ${logFilePath}`);

      // Extract filters from the response
      const filters = extractFilters(data);
      console.log(`Found ${filters.length} filters for category: ${category.description}`);

      if (filters.length > 0) {
        // Generate filter URLs
        return filters.map(filter => ({
          name: filter.name,
          id: filter.id,
          type: filter.type,
          productCount: filter.productCount,
          imageId: filter.imageId,
          url: generateFilterUrl(filter, category.description)
        }));
      } else {
        console.warn(`No filters found for category: ${category.description}`);
        return [];
      }
    } catch (error) {
      console.error(`API request failed for ${category.description}: ${error.message}`);
      return generateFallbackFilters(category);
    }
  } catch (error) {
    console.error(`Error fetching filters for category ${category.description}: ${error.message}`);
    return generateFallbackFilters(category);
  }
}

/**
 * Fetches filters for all categories
 * @returns {Promise<Object>} - Object with sections and categories with filters
 */
async function fetchAllCategoryFilters() {
  try {
    // Read the category structure
    const structurePath = path.join(__dirname, 'category_structure.json');
    const structureData = JSON.parse(fs.readFileSync(structurePath, 'utf8'));
    
    // Process all categories
    const result = {
      sections: {}
    };
    
    let totalCategories = 0;
    let totalFilters = 0;
    
    for (const sectionName of Object.keys(structureData)) {
      result.sections[sectionName] = {
        name: sectionName,
        categories: []
      };
      
      for (const category of structureData[sectionName]) {
        totalCategories++;
        
        console.log(`\n[${totalCategories}] Processing category: ${category.description}`);
        
        // Transform URL
        const apiUrl = transformUrl(category.link);
        
        // Fetch filters for the category
        const filters = await fetchFiltersForCategory(category);
        
        totalFilters += filters.length;
        
        // Add to result
        result.sections[sectionName].categories.push({
          id: category.id,
          name: category.description,
          apiUrl: apiUrl,
          filters: filters
        });
        
        // Add a delay to avoid overloading the API
        if (totalCategories < Object.keys(structureData).reduce((acc, section) => acc + structureData[section].length, 0)) {
          console.log(`Waiting ${config.api.requestDelay / 1000} seconds before next request...`);
          await new Promise(resolve => setTimeout(resolve, config.api.requestDelay));
        }
      }
    }
    
    // Save the result
    const outputFile = path.join(__dirname, 'category_filters_data.json');
    fs.writeFileSync(outputFile, JSON.stringify(result, null, 2), 'utf8');
    
    console.log(`\nProcessed ${totalCategories} categories across ${Object.keys(result.sections).length} sections`);
    console.log(`Found ${totalFilters} filters`);
    console.log(`Saved results to ${outputFile}`);
    
    return result;
  } catch (error) {
    console.error(`Error fetching all category filters: ${error.message}`);
    return null;
  }
}

/**
 * Tests fetching filters for a specific category
 * @param {string} categoryName - The category name
 */
async function testFetchFilters(categoryName) {
  try {
    // Read the category structure
    const structurePath = path.join(__dirname, 'category_structure.json');
    const structureData = JSON.parse(fs.readFileSync(structurePath, 'utf8'));
    
    // Find the category
    let category = null;
    for (const sectionName of Object.keys(structureData)) {
      const found = structureData[sectionName].find(c => c.description === categoryName);
      if (found) {
        category = found;
        break;
      }
    }
    
    if (!category) {
      console.error(`Category not found: ${categoryName}`);
      return;
    }
    
    console.log(`Testing fetch filters for category: ${category.description}`);
    
    // Fetch filters for the category
    const filters = await fetchFiltersForCategory(category);
    
    console.log(`\nFound ${filters.length} filters for category: ${category.description}`);
    filters.forEach((filter, index) => {
      console.log(`\n${index + 1}. ${filter.name} (ID: ${filter.id}, Type: ${filter.type}, Products: ${filter.productCount})`);
      console.log(`   URL: ${filter.url}`);
    });
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error(`Test failed: ${error.message}`);
  }
}

// If this script is run directly, test with "Home and Furnishing" category
if (require.main === module) {
  testFetchFilters('Home and Furnishing');
}

module.exports = {
  transformUrl,
  fetchData,
  extractFilters,
  generateFilterUrl,
  generateFallbackFilters,
  fetchFiltersForCategory,
  fetchAllCategoryFilters,
  testFetchFilters
};