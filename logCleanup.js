/**
 * Log Cleanup Utility
 * 
 * Cleans up old JSON log files and database records to prevent storage bloat
 */

const fs = require('fs').promises;
const path = require('path');
const DatabaseSingleton = require('./dbSingleton');

/**
 * Clean up old JSON log files
 */
async function cleanupOldJsonLogs(daysOld = 7) {
  const logsDir = path.join(__dirname, 'logs');
  let deletedCount = 0;
  let totalSize = 0;

  try {
    // Check if logs directory exists
    try {
      await fs.access(logsDir);
    } catch (error) {
      console.log('📁 Logs directory does not exist, nothing to clean');
      return { deletedCount: 0, totalSize: 0 };
    }

    const files = await fs.readdir(logsDir);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    console.log(`🧹 Cleaning JSON logs older than ${daysOld} days (before ${cutoffDate.toISOString()})`);

    for (const file of files) {
      if (!file.endsWith('.json')) {
        continue; // Skip non-JSON files
      }

      const filePath = path.join(logsDir, file);
      
      try {
        const stats = await fs.stat(filePath);
        
        if (stats.mtime < cutoffDate) {
          const fileSize = stats.size;
          await fs.unlink(filePath);
          deletedCount++;
          totalSize += fileSize;
          console.log(`🗑️ Deleted: ${file} (${(fileSize / 1024).toFixed(2)} KB, modified: ${stats.mtime.toISOString()})`);
        }
      } catch (error) {
        console.error(`❌ Error processing file ${file}:`, error.message);
      }
    }

    console.log(`✅ Cleanup complete: ${deletedCount} files deleted, ${(totalSize / 1024 / 1024).toFixed(2)} MB freed`);
    
    return { deletedCount, totalSize };
  } catch (error) {
    console.error('❌ Error during log cleanup:', error.message);
    return { deletedCount: 0, totalSize: 0 };
  }
}

/**
 * Clean up old database records
 */
async function cleanupOldDatabaseRecords(daysOld = 30) {
  try {
    const dbPersistence = await DatabaseSingleton.getInstance();
    
    console.log(`🗄️ Cleaning database records older than ${daysOld} days`);
    
    // Clean old displayed items
    const displayedItemsDeleted = await dbPersistence.cleanOldDisplayedItems(daysOld);
    console.log(`🗑️ Deleted ${displayedItemsDeleted} old displayed items records`);
    
    // Clean old notified deals (if method exists)
    try {
      if (typeof dbPersistence.cleanOldNotifiedDeals === 'function') {
        const notifiedDealsDeleted = await dbPersistence.cleanOldNotifiedDeals(daysOld);
        console.log(`🗑️ Deleted ${notifiedDealsDeleted} old notified deals records`);
      }
    } catch (error) {
      console.log('ℹ️ cleanOldNotifiedDeals method not available, skipping');
    }
    
    console.log(`✅ Database cleanup complete`);
    
    return {
      displayedItemsDeleted,
      notifiedDealsDeleted: 0 // Will be updated if method exists
    };
  } catch (error) {
    console.error('❌ Error during database cleanup:', error.message);
    return { displayedItemsDeleted: 0, notifiedDealsDeleted: 0 };
  }
}

/**
 * Run comprehensive cleanup
 */
async function runCleanup(options = {}) {
  const {
    jsonLogsDays = 7,
    databaseDays = 30,
    verbose = true
  } = options;

  if (verbose) {
    console.log('🧹 Starting comprehensive cleanup...');
    console.log(`📄 JSON logs: ${jsonLogsDays} days`);
    console.log(`🗄️ Database records: ${databaseDays} days`);
  }

  const results = {
    jsonLogs: { deletedCount: 0, totalSize: 0 },
    database: { displayedItemsDeleted: 0, notifiedDealsDeleted: 0 },
    startTime: Date.now()
  };

  // Clean JSON logs
  results.jsonLogs = await cleanupOldJsonLogs(jsonLogsDays);

  // Clean database records
  results.database = await cleanupOldDatabaseRecords(databaseDays);

  results.endTime = Date.now();
  results.executionTime = results.endTime - results.startTime;

  if (verbose) {
    console.log('\n📊 Cleanup Summary:');
    console.log(`📄 JSON Files: ${results.jsonLogs.deletedCount} deleted, ${(results.jsonLogs.totalSize / 1024 / 1024).toFixed(2)} MB freed`);
    console.log(`🗄️ Database: ${results.database.displayedItemsDeleted} displayed items deleted`);
    console.log(`⏱️ Execution time: ${results.executionTime}ms`);
  }

  return results;
}

/**
 * Schedule automatic cleanup
 */
function scheduleCleanup(intervalHours = 24) {
  console.log(`⏰ Scheduling automatic cleanup every ${intervalHours} hours`);
  
  // Run initial cleanup
  runCleanup().catch(error => {
    console.error('❌ Error in scheduled cleanup:', error.message);
  });

  // Schedule recurring cleanup
  setInterval(async () => {
    try {
      console.log('⏰ Running scheduled cleanup...');
      await runCleanup();
    } catch (error) {
      console.error('❌ Error in scheduled cleanup:', error.message);
    }
  }, intervalHours * 60 * 60 * 1000);
}

// CLI support
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};
  
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--json-days':
        options.jsonLogsDays = parseInt(args[++i]) || 7;
        break;
      case '--db-days':
        options.databaseDays = parseInt(args[++i]) || 30;
        break;
      case '--quiet':
        options.verbose = false;
        break;
      case '--help':
        console.log(`
Log Cleanup Utility

Usage: node logCleanup.js [options]

Options:
  --json-days <days>    Days to keep JSON logs (default: 7)
  --db-days <days>      Days to keep database records (default: 30)
  --quiet               Suppress verbose output
  --help                Show this help message

Examples:
  node logCleanup.js                    # Use defaults (7 days JSON, 30 days DB)
  node logCleanup.js --json-days 3      # Keep only 3 days of JSON logs
  node logCleanup.js --db-days 14       # Keep only 14 days of DB records
  node logCleanup.js --quiet            # Run silently
        `);
        process.exit(0);
    }
  }
  
  runCleanup(options).then(results => {
    console.log('🎉 Cleanup completed successfully');
    process.exit(0);
  }).catch(error => {
    console.error('❌ Cleanup failed:', error.message);
    process.exit(1);
  });
}

module.exports = {
  cleanupOldJsonLogs,
  cleanupOldDatabaseRecords,
  runCleanup,
  scheduleCleanup
};
