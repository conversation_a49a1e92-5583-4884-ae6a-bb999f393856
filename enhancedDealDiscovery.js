/**
 * Enhanced Deal Discovery with API Type Support
 * 
 * Supports both existing API and new API with Max Saver pricing
 */

const discountedItemsUtil = require('./discountedItemsUtil');
const { makeHttp2Request, buildCategoryListingUrl } = require('./testNewApiApproach');
const { deriveInStockFromProduct } = require('./src/services/api/utils/stockUtils');

/**
 * Get highly discounted items using the specified API type
 */
async function getHighlyDiscountedItems(options, progressCallback) {
  const { apiType = 'existing', priceCalculation = 'offer', ...restOptions } = options;
  
  console.log(`🔍 Using ${apiType} API with ${priceCalculation} price calculation`);
  
  if (apiType === 'new') {
    return await getItemsWithNewAPI(restOptions, priceCalculation, progressCallback);
  } else {
    return await getItemsWithExistingAPI(restOptions, progressCallback);
  }
}

/**
 * Get items using the existing API approach
 */
async function getItemsWithExistingAPI(options, progressCallback) {
  console.log('📡 Using existing API approach');
  return await discountedItemsUtil.getHighlyDiscountedItems(options, progressCallback);
}

/**
 * Get items using the new API approach with Max Saver support
 */
async function getItemsWithNewAPI(options, priceCalculation = 'offer', progressCallback) {
  console.log(`📡 Using new API approach with ${priceCalculation} price calculation`);
  
  const { 
    code, 
    category, 
    filter, 
    discountThreshold = 60, 
    limit = 50,
    retryAttempts = 5,
    retryDelay = 3000 
  } = options;

  try {
    // Build the URL using the new API approach
    const url = buildCategoryListingUrl(category, filter);
    console.log(`🌐 Fetching from: ${url}`);

    // Make the HTTP/2 request with retry logic
    const response = await makeHttp2RequestWithRetry(url, retryAttempts, retryDelay);
    
    if (!response || !response.data) {
      console.log('❌ No data received from API');
      return [];
    }

    // Parse and process the items
    const items = parseItemsFromResponse(response.data, priceCalculation, discountThreshold);
    
    // Apply limit
    const limitedItems = items.slice(0, limit);
    
    console.log(`✅ Found ${limitedItems.length} items using new API (${priceCalculation} pricing)`);
    // Notify completion for this category/filter if callback provided
    if (typeof progressCallback === 'function') {
      try {
        progressCallback({ type: 'filterCompleted', categoryName: category, filterName: filter, found: limitedItems.length });
      } catch (_) {}
    }
    return limitedItems;

  } catch (error) {
    console.error(`❌ Error with new API approach:`, error.message);
    
    // Fallback to existing API if new API fails
    console.log('🔄 Falling back to existing API');
    return await getItemsWithExistingAPI(options);
  }
}

/**
 * Make HTTP/2 request with enhanced retry logic
 */
async function makeHttp2RequestWithRetry(url, maxAttempts = 5, baseDelay = 3000) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`🔄 Attempt ${attempt}/${maxAttempts} for new API request`);
      
      const response = await makeHttp2Request(url);
      
      if (response && response.data) {
        console.log(`✅ New API request successful on attempt ${attempt}`);
        return response;
      } else {
        throw new Error('No data in response');
      }
      
    } catch (error) {
      lastError = error;
      console.log(`❌ Attempt ${attempt} failed:`, error.message);
      
      if (attempt < maxAttempts) {
        // Calculate delay: first 3 attempts use baseDelay, later attempts use longer delay
        const delay = attempt <= 3 ? baseDelay : 60000; // 1 minute for attempts 4-5
        console.log(`⏳ Waiting ${delay/1000}s before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError || new Error('All retry attempts failed');
}

/**
 * Parse items from API response and calculate discounts based on price calculation method
 */
function parseItemsFromResponse(data, priceCalculation, discountThreshold) {
  const items = [];
  
  try {
    // Parse the response data (assuming it's similar to existing API structure)
    const products = data.products || data.items || [];
    
    for (const product of products) {
      try {
        const item = parseIndividualItem(product, priceCalculation, discountThreshold);
        if (item) {
          items.push(item);
        }
      } catch (error) {
        console.log(`⚠️ Error parsing item:`, error.message);
        continue;
      }
    }
    
  } catch (error) {
    console.error('❌ Error parsing response data:', error.message);
  }
  
  return items.sort((a, b) => b.discountPercentage - a.discountPercentage);
}

/**
 * Parse individual item and calculate discount based on price calculation method
 */
function parseIndividualItem(product, priceCalculation, discountThreshold) {
  const name = product.name || product.product_name || '';
  const id = product.id || product.product_id || '';
  
  // Get pricing information
  const storePrice = parseFloat(product.store_price || product.mrp || 0);
  const offerPrice = parseFloat(product.offer_price || product.price || 0);
  const maxSaverPrice = parseFloat(product.max_saver_price || product.member_price || offerPrice);
  
  if (!storePrice || !offerPrice) {
    return null;
  }
  
  // Calculate discount based on price calculation method
  let baselinePrice, finalPrice, discountPercentage;
  
  if (priceCalculation === 'maxsaver' && maxSaverPrice && maxSaverPrice < offerPrice) {
    // Use Max Saver pricing for discount calculation
    baselinePrice = storePrice;
    finalPrice = maxSaverPrice;
    discountPercentage = ((storePrice - maxSaverPrice) / storePrice) * 100;
  } else {
    // Use regular offer pricing for discount calculation
    baselinePrice = storePrice;
    finalPrice = offerPrice;
    discountPercentage = ((storePrice - offerPrice) / storePrice) * 100;
  }
  
  // Check if discount meets threshold
  if (discountPercentage < discountThreshold) {
    return null;
  }
  
  return {
    id,
    name,
    variation: product.variation || product.variant || '',
    storePrice,
    offerPrice,
    maxSaverPrice: maxSaverPrice || offerPrice,
    finalPrice, // The price used for discount calculation
    discountPercentage: Math.round(discountPercentage * 100) / 100,
    quantity: product.quantity || '',
    unit: product.unit || '',
    url: product.url || product.product_url || '',
    imageUrl: extractImageUrl(product),
    inStock: deriveInStockFromProduct(product),
    appliedThreshold: discountThreshold,
    priceCalculationMethod: priceCalculation,
    category: product.category || '',
    filter: product.filter || ''
  };
}

/**
 * Get available price calculation methods
 */
function getAvailablePriceCalculationMethods() {
  return [
    {
      value: 'offer',
      label: 'Offer Price',
      description: 'Calculate discounts from regular offer price'
    },
    {
      value: 'maxsaver',
      label: 'Max Saver Price',
      description: 'Calculate discounts from Max Saver membership price'
    }
  ];
}

/**
 * Extract image URL from product data
 */
function extractImageUrl(product) {
  const baseUrl = 'https://instamart-media-assets.swiggy.com/swiggy/image/upload/fl_lossy,f_auto,q_auto,h_600';

  // Try to get image from variations first (preferred method)
  if (product.variations && Array.isArray(product.variations)) {
    for (const variation of product.variations) {
      if (variation.images && Array.isArray(variation.images) && variation.images.length > 0) {
        const imageId = variation.images[0];
        return `${baseUrl}/${imageId}`;
      }
    }
  }

  // Fallback to direct image property
  if (product.image) {
    return `${baseUrl}/${product.image}`;
  }

  // Fallback to images array
  if (product.images && Array.isArray(product.images) && product.images.length > 0) {
    return `${baseUrl}/${product.images[0]}`;
  }

  // Fallback to image_url property
  if (product.image_url) {
    return `${baseUrl}/${product.image_url}`;
  }

  return null;
}

/**
 * Get available API types
 */
function getAvailableApiTypes() {
  return [
    {
      value: 'existing',
      label: 'Existing API',
      description: 'Use current deal discovery API'
    },
    {
      value: 'new',
      label: 'New API',
      description: 'Use enhanced API with Max Saver support'
    }
  ];
}

module.exports = {
  getHighlyDiscountedItems,
  getItemsWithExistingAPI,
  getItemsWithNewAPI,
  getAvailablePriceCalculationMethods,
  getAvailableApiTypes
};
