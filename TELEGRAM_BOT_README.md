# 🤖 <PERSON><PERSON><PERSON> Deals Telegram Bot

A powerful Telegram bot that helps you find the best discounted items from Swiggy Instamart with real-time data and smart filtering.

## ✨ Features

### 🔍 **Find Code by Category/Filter**
- Enter category name to get category-only codes
- Add filter name for specific filter codes
- Smart search with suggestions for partial matches
- Shows all available filters for each category

### 💰 **Find Deals by Code(s)**
- Support for single codes (`A1`) or multiple codes (`A1,D15,K5`)
- Category-only codes (`A`) fetch all filters automatically
- Customizable discount thresholds (10-90%)
- Real-time product data with images and direct links

### 📋 **Browse Categories**
- Complete list of all 44 available categories
- Shows corresponding mapping codes
- Easy navigation and selection

### ⏰ **Cron Jobs (NEW!)**
- Set up automated deal monitoring for any category/filter
- Default 10-minute intervals (customizable 5-1440 minutes)
- Get notifications only when deals are found
- Manage up to 5 active jobs simultaneously
- Job statistics and performance tracking

### ⏹️ **Manage Cron Jobs**
- View all your active monitoring jobs
- Stop individual jobs or all jobs at once
- Real-time job status and statistics
- Automatic error handling and recovery

### 📊 **System Statistics**
- Real-time mapping statistics
- Configuration details
- System health information
- Active cron job monitoring

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Create Telegram Bot
1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Send `/newbot` and follow instructions
3. Get your bot token (looks like `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

### 3. Set Environment Variable
```bash
# Windows
set TELEGRAM_BOT_TOKEN=your_bot_token_here

# Linux/Mac
export TELEGRAM_BOT_TOKEN=your_bot_token_here
```

### 4. Start the Bot
```bash
npm start
# or
node startBot.js
```

## 🎯 Usage Examples

### Finding Codes
1. Click "🔍 Find Code by Category/Filter"
2. Enter category name: `Appliances`
3. Get category code: `A` (all filters)
4. Or enter: `Appliances Air Fryers`
5. Get specific code: `A1`

### Getting Deals
1. Click "💰 Find Deals by Code(s)"
2. Enter codes: `A,D,K` (multiple categories)
3. Set threshold: `50` (50%+ discount)
4. Get results with product details and links

### Setting Up Cron Jobs
1. Click "⏰ Start Cron Job"
2. Enter code: `A` (monitor all Appliances deals)
3. Set interval: `10` (check every 10 minutes)
4. Set threshold: `60` (notify for 60%+ discounts)
5. Get automatic notifications when deals are found!

### Managing Cron Jobs
1. Click "⏹️ Manage Cron Jobs"
2. View all active jobs with statistics
3. Send job number to stop (e.g., "1", "2")
4. Send "stop all" to stop all jobs
5. Send "refresh" to update job status

## 📱 Bot Commands

- `/start` - Welcome message and main menu
- `/help` - Detailed help and instructions

## ⏰ Cron Job Features

### **Automated Monitoring**
- **Smart Scheduling**: Uses node-cron for reliable job execution
- **Flexible Intervals**: 5 minutes to 24 hours (default: 10 minutes)
- **Custom Thresholds**: Set different discount requirements per job
- **Notification Control**: Only get alerts when deals are actually found

### **Job Management**
- **Multi-Job Support**: Up to 5 active jobs per user
- **Real-time Status**: See job statistics and performance
- **Easy Control**: Start, stop, and manage jobs with simple commands
- **Error Recovery**: Automatic error handling with user notifications

### **Smart Notifications**
- **Deal Alerts**: Get notified immediately when deals are found
- **Batch Limiting**: Max 10 items per notification to prevent spam
- **Status Updates**: Periodic updates when no deals are found
- **Rich Formatting**: Product details with images and direct links

## 🎛️ Bot Features

### **Smart Filtering**
- **Blacklist Keywords**: Automatically filters out test/sample items
- **Custom Thresholds**: Different discount expectations for different product types
- **Deduplication**: Removes duplicate items across multiple codes

### **User-Friendly Interface**
- **Chunked Results**: Large result sets split into manageable chunks
- **Rich Formatting**: Product details with emojis and formatting
- **Direct Links**: Clickable links to product pages
- **Progress Updates**: Real-time processing status

### **Performance Optimized**
- **Caching System**: Optional caching for faster repeated queries
- **Rate Limiting**: Built-in delays to respect API limits
- **Error Handling**: Graceful error recovery and user feedback

## 🔧 Configuration

The bot uses the same `config.json` as the command-line runner:

```json
{
  "api": {
    "requestDelay": 3000,
    "useCache": false,
    "defaultDiscountThreshold": 75
  },
  "filtering": {
    "blacklistKeywords": ["test", "sample", "demo"],
    "customDiscountThresholds": {
      "premium": 50,
      "organic": 40,
      "electronics": 20
    }
  }
}
```

## 📊 Example Output

```
🔥 1. Lifelong LLIMR02 Immersion Rod 1500W
Black & Silver

💵 Original: ₹1500
🏷️ Offer: ₹249
📉 Discount: 83.40% (Threshold: 50%)
📦 Quantity: 1 unit
🔗 View Product

⚡ 2. Nova Hair Straightener NHS 860
Professional Temperature Control

💵 Original: ₹2395
🏷️ Offer: ₹489
📉 Discount: 79.58% (Threshold: 50%)
📦 Quantity: 1 Piece
🔗 View Product
```

## 🛠️ Development

### Project Structure
```
├── telegramBot.js          # Main bot logic
├── startBot.js            # Bot starter script
├── runner.js              # Command-line interface
├── discountedItemsUtil.js  # Core discount fetching
├── categoryFilterMappingUtil.js # Mapping utilities
├── config.json            # Configuration
└── package.json           # Dependencies
```

### Adding New Features
1. Add handlers in `telegramBot.js`
2. Update user session states
3. Add corresponding menu options
4. Test with multiple users

## 🔒 Security & Privacy

- **No Data Storage**: User sessions are temporary (in-memory only)
- **No Personal Data**: Bot doesn't collect or store personal information
- **API Rate Limiting**: Respects Swiggy's API limits
- **Error Handling**: Secure error messages without exposing internals

## 🐛 Troubleshooting

### Common Issues

**Bot not responding:**
- Check if `TELEGRAM_BOT_TOKEN` is set correctly
- Verify bot token with @BotFather
- Check console for error messages

**No deals found:**
- Try lower discount thresholds (20-30%)
- Verify mapping codes are valid
- Check if categories have available items

**Slow responses:**
- Normal for multiple codes (3s delay between API calls)
- Enable caching in config for faster repeated queries
- Use specific filter codes instead of category codes

## 📈 Statistics

- **612 Total Mappings**: 44 categories + 568 filters
- **Real-time Data**: Direct integration with Swiggy API
- **Smart Filtering**: Custom thresholds and blacklist keywords
- **Multi-code Support**: Process multiple categories simultaneously

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new features
4. Submit pull request

## 📄 License

MIT License - feel free to use and modify for your needs.

---

**Happy deal hunting! 🛒💰**
