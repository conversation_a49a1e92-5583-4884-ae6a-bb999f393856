const fs = require('fs');
const path = require('path');
const https = require('https');
const zlib = require('zlib');
const mappingUtil = require('./categoryFilterMappingUtil');
const { deriveInStockFromVariation } = require('./src/services/api/utils/stockUtils');

// Load configuration
const config = JSON.parse(fs.readFileSync(path.join(__dirname, 'config.json'), 'utf8'));

/**
 * Checks if an item name contains any blacklisted keywords
 * @param {string} itemName - The item name to check
 * @returns {boolean} - True if the item should be filtered out
 */
function isBlacklisted(itemName) {
  const lowerName = itemName.toLowerCase();
  return config.filtering.blacklistKeywords.some(keyword =>
    lowerName.includes(keyword.toLowerCase())
  );
}

/**
 * Gets the custom discount threshold for an item based on keywords in its name
 * @param {string} itemName - The item name to check
 * @param {number} defaultThreshold - The default threshold to use if no custom threshold is found
 * @returns {number} - The discount threshold to use for this item
 */
function getCustomDiscountThreshold(itemName, defaultThreshold) {
  const lowerName = itemName.toLowerCase();

  // Check for custom threshold keywords
  for (const [keyword, threshold] of Object.entries(config.filtering.customDiscountThresholds)) {
    if (lowerName.includes(keyword.toLowerCase())) {
      return threshold;
    }
  }

  return defaultThreshold;
}

/**
 * Generates the Swiggy Instamart item URL
 * @param {string} itemId - The item ID
 * @returns {string} - The complete item URL
 */
function generateItemUrl(itemId) {
  const baseUrl = `${config.urls.instamartItemBase}${itemId}${config.urls.shareParam}`;
  const storeId = config.urls.storeId;

  // Add storeId parameter
  const separator = baseUrl.includes('?') ? '&' : '?';
  return `${baseUrl}${separator}storeId=${storeId}`;
}

/**
 * Generates the Swiggy image URL from image array
 * @param {Array} images - Array of image IDs
 * @returns {string|null} - The complete image URL or null if no images
 */
function generateImageUrl(images) {
  const baseUrl = 'https://instamart-media-assets.swiggy.com/swiggy/image/upload/fl_lossy,f_auto,q_auto,h_600';

  if (images && Array.isArray(images) && images.length > 0) {
    const imageId = images[0]; // Use the first image
    return `${baseUrl}/${imageId}`;
  }

  return null;
}

/**
 * Fetches highly discounted items based on category and filter
 * @param {Object} options - The options object
 * @param {string} [options.categoryName] - The name of the category (e.g., "Fresh Fruits")
 * @param {string} [options.filterName] - The name of the filter (e.g., "Seasonal Fruits"), if not provided all filters will be analyzed
 * @param {string} [options.code] - The mapping code (e.g., "A1") as an alternative to categoryName and filterName
 * @param {number} [options.discountThreshold=75] - Minimum discount percentage to include items
 * @param {boolean} [options.useCache=true] - Whether to use cached responses from logs
 * @returns {Promise<Array>} - Array of highly discounted items
 */
async function getHighlyDiscountedItems(options = {}, progressCallback) {
  try {
    // Set default values from config
    const discountThreshold = options.discountThreshold ?? config.api.defaultDiscountThreshold;
    const useCache = options.useCache ?? config.api.useCache;
    
    let categoryName = options.categoryName;
    let filterName = options.filterName;
    
    // If code is provided, convert it to category and filter names
    if (options.code) {
      if (!mappingUtil.isValidCode(options.code)) {
        throw new Error(`Invalid mapping code: ${options.code}`);
      }

      const names = mappingUtil.codeToNames(options.code);
      if (!names) {
        throw new Error(`Could not find category and filter for code: ${options.code}`);
      }

      categoryName = names.category;
      filterName = names.filter;

      if (mappingUtil.isCategoryOnlyCode(options.code)) {
        console.log(`Using mapping code ${options.code} for category "${categoryName}" (all filters)`);
      } else {
        console.log(`Using mapping code ${options.code} for category "${categoryName}" and filter "${filterName}"`);
      }
    }
    
    if (!categoryName) {
      throw new Error('Category name or mapping code is required');
    }
    
    // If filterName is not provided, process all filters for the category
    if (!filterName) {
      console.log(`No filter name provided, processing all filters for category "${categoryName}"`);
      return await processAllFilters(categoryName, discountThreshold, useCache, progressCallback);
    }
    
    // Find the filter URL based on category and filter name
    const filterUrl = findFilterUrl(categoryName, filterName);
    
    if (!filterUrl) {
      throw new Error(`Could not find filter URL for category "${categoryName}" and filter "${filterName}"`);
    }

    console.log(`Found filter URL: ${filterUrl}`,useCache);
    // Make the API request (with cache if specified)
    const response = await makeApiRequest(filterUrl, categoryName, filterName, useCache);
    
    // Extract items from the last widget
    const items = extractItemsFromLastWidget(response);
    
    // Filter items based on discount threshold
    const discountedItems = filterDiscountedItems(items, discountThreshold);
    
    return discountedItems;
  } catch (error) {
    console.error(`Error fetching discounted items: ${error.message}`);
    return [];
  }
}

/**
 * Processes all filters for a given category
 * @param {string} categoryName - The name of the category
 * @param {number} discountThreshold - Minimum discount percentage to include items
 * @param {boolean} useCache - Whether to use cached responses from logs (default: true)
 * @returns {Promise<Array>} - Array of highly discounted items from all filters
 */
async function processAllFilters(categoryName, discountThreshold, useCache = true, progressCallback) {
  try {
    // Find all filter URLs for the category
    const filters = findAllFilterUrls(categoryName);
    
    if (!filters || filters.length === 0) {
      throw new Error(`No filters found for category "${categoryName}"`);
    }
    
    console.log(`Found ${filters.length} filters for category "${categoryName}"`);
    if (typeof progressCallback === 'function') {
      try {
        progressCallback({ type: 'filtersTotal', categoryName, total: filters.length });
      } catch (_) {}
    }
    
    // Process each filter with a 10-second delay between requests
    const allDiscountedItems = [];
    
    for (let i = 0; i < filters.length; i++) {
      const filter = filters[i];
      console.log(`Processing filter ${i+1}/${filters.length}: "${filter.name}"`);
      if (typeof progressCallback === 'function') {
        try {
          progressCallback({ type: 'filterProgress', categoryName, current: i + 1, total: filters.length, filterName: filter.name });
        } catch (_) {}
      }
      
      // Make the API request (with cache if specified)
      const response = await makeApiRequest(filter.url, categoryName, filter.name, useCache);
      
      // Extract items from the last widget
      const items = extractItemsFromLastWidget(response);
      
      // Filter items based on discount threshold
      const discountedItems = filterDiscountedItems(items, discountThreshold);
      
      // Add to the combined results
      allDiscountedItems.push(...discountedItems);
      
      // Add a 3-second delay between requests (except for the last one)
      if (i < filters.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }
    
    // Remove duplicates based on item ID
    const uniqueItems = [];
    const seenIds = new Set();
    
    for (const item of allDiscountedItems) {
      if (!seenIds.has(item.id)) {
        seenIds.add(item.id);
        uniqueItems.push(item);
      }
    }
    
    return uniqueItems;
  } catch (error) {
    console.error(`Error processing all filters: ${error.message}`);
    return [];
  }
}

/**
 * Finds the filter URL based on category and filter name
 * @param {string} categoryName - The name of the category
 * @param {string} filterName - The name of the filter
 * @returns {string|null} - The filter URL or null if not found
 */
function findFilterUrl(categoryName, filterName) {
  try {
    // Read the category filters data
    const filtersDataPath = path.join(__dirname, 'category_filters_data.json');
    const filtersData = JSON.parse(fs.readFileSync(filtersDataPath, 'utf8'));
    
    // Find the category and filter
    for (const sectionName in filtersData.sections) {
      const section = filtersData.sections[sectionName];
      
      for (const category of section.categories) {
        if (category.name.toLowerCase() === categoryName.toLowerCase()) {
          // Found the category, now find the filter
          for (const filter of category.filters) {
            if (filter.name.toLowerCase() === filterName.toLowerCase()) {
              // Found the filter, return its URL
              return filter.url;
            }
          }
          
          // If we get here, the filter wasn't found for this category
          console.warn(`Filter "${filterName}" not found for category "${categoryName}"`);
          return null;
        }
      }
    }
    
    // If we get here, the category wasn't found
    console.warn(`Category "${categoryName}" not found`);
    return null;
  } catch (error) {
    console.error(`Error finding filter URL: ${error.message}`);
    return null;
  }
}

/**
 * Finds all filter URLs for a given category
 * @param {string} categoryName - The name of the category
 * @returns {Array|null} - Array of filter objects with name and URL, or null if category not found
 */
function findAllFilterUrls(categoryName) {
  try {
    // Read the category filters data
    const filtersDataPath = path.join(__dirname, 'category_filters_data.json');
    const filtersData = JSON.parse(fs.readFileSync(filtersDataPath, 'utf8'));
    
    // Find the category
    for (const sectionName in filtersData.sections) {
      const section = filtersData.sections[sectionName];
      
      for (const category of section.categories) {
        if (category.name.toLowerCase() === categoryName.toLowerCase()) {
          // Found the category, return all its filters
          return category.filters.map(filter => ({
            name: filter.name,
            url: filter.url
          }));
        }
      }
    }
    
    // If we get here, the category wasn't found
    console.warn(`Category "${categoryName}" not found`);
    return null;
  } catch (error) {
    console.error(`Error finding filter URLs: ${error.message}`);
    return null;
  }
}

/**
 * Sleep function for retry delays
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} - Promise that resolves after the delay
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Makes a POST request to the filter URL with the required payload and retry logic
 * @param {string} url - The filter URL
 * @param {string} categoryName - The name of the category
 * @param {string} filterName - The name of the filter
 * @param {boolean} useCache - Whether to use cached responses from logs (default: false)
 * @returns {Promise<Object>} - The parsed response
 */
async function makeApiRequest(url, categoryName, filterName, useCache = false) {
  // Extended retry configuration
  const INITIAL_RETRIES = 3;
  const EXTENDED_RETRIES = 2;
  const INITIAL_RETRY_DELAY = 10000; // 10 seconds
  const EXTENDED_RETRY_DELAY = 60000; // 1 minute

  // Create logs folder if it doesn't exist
  const logsDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir);
  }

  console.log(`Making API request to: ${url}`, useCache);
  // Check if we should use cache and if a cached file exists
  if (useCache) {
    const cachedFilePath = findMostRecentLogFile(categoryName, filterName);
    if (cachedFilePath) {
      console.log(`Using cached data from: ${cachedFilePath}`);

      try {
        // Read the cached file
        const cachedData = fs.readFileSync(cachedFilePath, 'utf8');

        // Parse the JSON data
        const parsedData = JSON.parse(cachedData);

        // Check if the cached data has the expected structure
        if (parsedData && parsedData.response) {
          console.log(`Successfully loaded cached data for ${categoryName}/${filterName}`);
          return parsedData.response;
        } else {
          console.warn(`Cached data has unexpected structure, falling back to new request`);
        }
      } catch (error) {
        console.error(`Error reading cached file: ${error.message}`);
      }
    } else {
      console.log(`No cache found for ${categoryName}/${filterName}, making new request`);
    }
  }

  // Attempt API request with initial retry logic (3 attempts with 10s delay)
  for (let attempt = 1; attempt <= INITIAL_RETRIES; attempt++) {
    try {
      console.log(`API request attempt ${attempt}/${INITIAL_RETRIES} for ${categoryName}/${filterName}`);
      const result = await makeApiRequestAttempt(url, categoryName, filterName, logsDir);
      console.log(`✅ API request successful on attempt ${attempt}`);
      return result;
    } catch (error) {
      console.error(`❌ API request attempt ${attempt}/${INITIAL_RETRIES} failed: ${error.message}`);

      if (attempt === INITIAL_RETRIES) {
        console.log(`⏳ Initial attempts failed, starting extended retry with 1-minute intervals...`);
        break; // Exit initial retry loop to start extended retry
      }

      console.log(`⏳ Waiting ${INITIAL_RETRY_DELAY/1000} seconds before retry...`);
      await sleep(INITIAL_RETRY_DELAY);
    }
  }

  // Extended retry logic (2 more attempts with 1-minute delay)
  for (let attempt = 1; attempt <= EXTENDED_RETRIES; attempt++) {
    try {
      console.log(`⏳ Extended retry - waiting 1 minute before attempt ${attempt}/${EXTENDED_RETRIES}...`);
      await sleep(EXTENDED_RETRY_DELAY);
      
      console.log(`API request extended attempt ${attempt}/${EXTENDED_RETRIES} for ${categoryName}/${filterName}`);
      const result = await makeApiRequestAttempt(url, categoryName, filterName, logsDir);
      console.log(`✅ API request successful on extended attempt ${attempt}`);
      return result;
    } catch (error) {
      console.error(`❌ API request extended attempt ${attempt}/${EXTENDED_RETRIES} failed: ${error.message}`);

      if (attempt === EXTENDED_RETRIES) {
        console.error(`🚫 All ${INITIAL_RETRIES + EXTENDED_RETRIES} attempts failed for ${categoryName}/${filterName}`);
        throw new Error(`API request failed after ${INITIAL_RETRIES + EXTENDED_RETRIES} attempts: ${error.message}`);
      }
    }
  }
}

/**
 * Makes a single API request attempt
 * @param {string} url - The filter URL
 * @param {string} categoryName - The name of the category
 * @param {string} filterName - The name of the filter
 * @param {string} logsDir - The logs directory path
 * @returns {Promise<Object>} - The parsed response
 */
function makeApiRequestAttempt(url, categoryName, filterName, logsDir) {
  // Generate a unique filename for the response
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const safeCategory = categoryName ? categoryName.replace(/[^a-z0-9]/gi, '_').toLowerCase() : 'unknown';
  const safeFilter = filterName ? filterName.replace(/[^a-z0-9]/gi, '_').toLowerCase() : 'all';
  const filename = `${safeCategory}_${safeFilter}_${timestamp}.json`;
  const logFilePath = path.join(logsDir, filename);

  return new Promise((resolve, reject) => {
    // Parse the URL to get hostname and path
    const urlObj = new URL(url);
    
    // Set up the request options
    const options = {
      hostname: urlObj.hostname,
      path: urlObj.pathname + urlObj.search,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://www.swiggy.com/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'sec-ch-ua': '"Google Chrome";v="91", "Chromium";v="91", ";Not A Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin'
      }
    };
    
    // Set up the payload
    const payload = JSON.stringify({
      facets: {},
      sortAttribute: "priceLowToHigh"
    });
    
    // Make the request
    const req = https.request(options, (res) => {
      const chunks = [];

      res.on('data', (chunk) => {
        chunks.push(chunk);
      });

      res.on('end', () => {
        try {
          let buffer = Buffer.concat(chunks);

          // Handle compression
          if (res.headers['content-encoding'] === 'gzip') {
            buffer = zlib.gunzipSync(buffer);
          } else if (res.headers['content-encoding'] === 'deflate') {
            buffer = zlib.inflateSync(buffer);
          } else if (res.headers['content-encoding'] === 'br') {
            buffer = zlib.brotliDecompressSync(buffer);
          }

          const data = buffer.toString('utf8');
          const parsedData = JSON.parse(data);
          
          // Add metadata to the response
          const responseWithMetadata = {
            metadata: {
              url: url,
              categoryName: categoryName,
              filterName: filterName,
              timestamp: new Date().toISOString(),
              isMockData: false
            },
            response: parsedData
          };
          
          // Save the response to a file
          fs.writeFileSync(logFilePath, JSON.stringify(responseWithMetadata, null, 2), 'utf8');

          resolve(parsedData);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(new Error(`Request failed: ${error.message}`));
    });
    
    // Write the payload and end the request
    req.write(payload);
    req.end();
  });

}

/**
 * Extracts items from the last widget in the response
 * @param {Object} response - The API response
 * @returns {Array} - Array of items
 */
function extractItemsFromLastWidget(response) {
  try {
    // Check if the response has the expected structure
    if (!response || !response.data || !response.data.widgets || !Array.isArray(response.data.widgets) || response.data.widgets.length === 0) {
      console.warn('Response does not have the expected structure');
      
      // Check if the response has a collectionData structure with entityData
      if (response && response.data && response.data.collectionData && response.data.collectionData.entityData) {
        console.log('Found collectionData structure, trying to extract items');
        return extractItemsFromCollectionData(response.data.collectionData);
      }
      
      return [];
    }
    
    // First try to find a PRODUCT_LIST widget
    const productListWidget = response.data.widgets.find(widget => 
      widget.widgetInfo && widget.widgetInfo.widgetType === 'PRODUCT_LIST'
    );
    
    if (productListWidget) {
      console.log('Found PRODUCT_LIST widget');
      return extractItemsFromWidget(productListWidget);
    }
    
    // If no PRODUCT_LIST widget is found, look for any widget with a data array containing items with variations
    for (const widget of response.data.widgets) {
      if (widget.data && Array.isArray(widget.data)) {
        // Check if any item in the data array has variations
        const hasItemsWithVariations = widget.data.some(item => 
          item && item.variations && Array.isArray(item.variations)
        );
        
        if (hasItemsWithVariations) {
          console.log('Found widget with items that have variations');
          return widget.data;
        }
      }
    }
    
    // If no suitable widget is found, try the last widget as a fallback
    const lastWidget = response.data.widgets[response.data.widgets.length - 1];
    console.warn('No widget with variations found, trying last widget as fallback');
    return extractItemsFromWidget(lastWidget);
  } catch (error) {
    console.error(`Error extracting items from last widget: ${error.message}`);
    return [];
  }
}

/**
 * Extracts items from collection data
 * @param {Object} collectionData - The collection data object
 * @returns {Array} - Array of items
 */
function extractItemsFromCollectionData(collectionData) {
  try {
    if (!collectionData.entityData || !Array.isArray(collectionData.entityData)) {
      console.warn('Collection data does not have the expected structure');
      return [];
    }
    
    // Transform entity data into the format expected by filterDiscountedItems
    return collectionData.entityData.map(entity => ({
      display_name: entity.name || 'Unknown Item',
      variations: [{
        id: entity.id,
        display_name: entity.name || 'Unknown Item',
        price: {
          store_price: entity.price || 0,
          offer_price: entity.offerPrice || 0
        },
        quantity: entity.quantity || '0',
        unit_of_measure: entity.unit || ''
      }]
    }));
  } catch (error) {
    console.error(`Error extracting items from collection data: ${error.message}`);
    return [];
  }
}

/**
 * Extracts items from a widget
 * @param {Object} widget - The widget object
 * @returns {Array} - Array of items
 */
function extractItemsFromWidget(widget) {
  try {
    // Check if the widget has the expected structure
    if (!widget.data || !Array.isArray(widget.data)) {
      console.warn('Widget does not have the expected structure');
      return [];
    }
    
    // Return the items
    return widget.data;
  } catch (error) {
    console.error(`Error extracting items from widget: ${error.message}`);
    return [];
  }
}

/**
 * Filters items based on discount threshold
 * @param {Array} items - Array of items
 * @param {number} discountThreshold - Minimum discount percentage
 * @returns {Array} - Array of highly discounted items
 */
function filterDiscountedItems(items, discountThreshold) {
  try {
    const discountedItems = [];
    
    // Process each item
    for (const item of items) {
      // Check if the item has variations
      if (!item.variations || !Array.isArray(item.variations)) {
        continue;
      }
      
      // Process each variation
      for (const variation of item.variations) {
        // Check if the variation has price information
        if (!variation.price || !variation.price.store_price || !variation.price.offer_price) {
          continue;
        }
        
        // Check if item is blacklisted
        const itemName = item.display_name;
        const variationName = variation.display_name;
        const fullItemName = `${itemName} ${variationName}`;

        if (isBlacklisted(fullItemName)) {
          continue; // Skip blacklisted items
        }

        // Calculate discount percentage
        const storePrice = variation.price.store_price;
        const offerPrice = variation.price.offer_price;
        const discountPercentage = ((storePrice - offerPrice) / storePrice) * 100;

        // Get custom discount threshold for this item
        const itemDiscountThreshold = getCustomDiscountThreshold(fullItemName, discountThreshold);

        // Check if the discount percentage meets the threshold and item is in stock
        const isInStock = deriveInStockFromVariation(variation);

        if (discountPercentage >= itemDiscountThreshold && isInStock) {
          // Add the item to the discounted items list
          discountedItems.push({
            name: itemName,
            variation: variationName,
            storePrice: storePrice,
            offerPrice: offerPrice,
            discountPercentage: discountPercentage.toFixed(2),
            quantity: variation.quantity,
            unit: variation.unit_of_measure,
            id: variation.id,
            url: generateItemUrl(item.product_id),
            images: variation.images,
            imageUrl: generateImageUrl(variation.images),
            inStock: isInStock,
            appliedThreshold: itemDiscountThreshold
          });
        }
      }
    }
    
    return discountedItems;
  } catch (error) {
    console.error(`Error filtering discounted items: ${error.message}`);
    return [];
  }
}

/**
 * Finds the most recent log file for a given category and filter
 * @param {string} categoryName - The name of the category
 * @param {string} filterName - The name of the filter
 * @returns {string|null} - The path to the most recent log file, or null if none found
 */
function findMostRecentLogFile(categoryName, filterName) {
  try {
    const logsDir = path.join(__dirname, 'logs');
    
    // If logs directory doesn't exist, return null
    if (!fs.existsSync(logsDir)) {
      return null;
    }
    
    // Generate safe category and filter names for filename matching
    const safeCategory = categoryName ? categoryName.replace(/[^a-z0-9]/gi, '_').toLowerCase() : 'unknown';
    const safeFilter = filterName ? filterName.replace(/[^a-z0-9]/gi, '_').toLowerCase() : 'all';
    
    // Get all files in the logs directory
    const files = fs.readdirSync(logsDir);
    
    // Filter files that match the pattern {category}_{filter}_*.json
    const matchingFiles = files.filter(file => 
      file.startsWith(`${safeCategory}_${safeFilter}_`) && file.endsWith('.json')
    );
    
    if (matchingFiles.length === 0) {
      return null;
    }
    
    // Sort files by timestamp (newest first)
    matchingFiles.sort((a, b) => {
      // Extract timestamps from filenames
      const timestampA = a.substring(a.lastIndexOf('_') + 1, a.lastIndexOf('.'));
      const timestampB = b.substring(b.lastIndexOf('_') + 1, b.lastIndexOf('.'));
      
      // Compare timestamps (reverse order for newest first)
      return timestampB.localeCompare(timestampA);
    });
    
    // Return the path to the most recent file
    const mostRecentFile = matchingFiles[0];
    console.log(`Found cached log file: ${mostRecentFile}`);
    return path.join(logsDir, mostRecentFile);
  } catch (error) {
    console.error(`Error finding most recent log file: ${error.message}`);
    return null;
  }
}

// Export the functions
module.exports = {
  getHighlyDiscountedItems,
  findAllFilterUrls,
  findFilterUrl,
  findMostRecentLogFile
};

