const fs = require('fs');
const path = require('path');

/**
 * Generates a mapping file where categories are represented by alphabets (A, B, C, ..., Z, AA, AB)
 * and filters are represented by numbers (1, 2, ...)
 * The mapping is in the format "A1": { category: "Category Name", filter: "Filter Name" }
 */
function generateCategoryFilterMapping() {
  try {
    // Read the category filters data
    const filtersDataPath = path.join(__dirname, 'category_filters_data.json');
    const filtersData = JSON.parse(fs.readFileSync(filtersDataPath, 'utf8'));
    
    // Initialize arrays to store all categories and their filters
    const allCategories = [];
    const categoryToFiltersMap = {};
    
    // Extract all categories and their filters
    Object.keys(filtersData.sections).forEach(sectionName => {
      const section = filtersData.sections[sectionName];
      
      section.categories.forEach(category => {
        allCategories.push(category);
        categoryToFiltersMap[category.name] = category.filters;
      });
    });
    
    // Sort categories alphabetically by name
    allCategories.sort((a, b) => a.name.localeCompare(b.name));
    
    // Generate mapping
    const mapping = {};
    const reverseMapping = {};
    
    // Function to generate alphabetic codes (A, B, C, ..., Z, AA, AB, ...)
    function generateAlphabeticCode(index) {
      const baseCharCode = 'A'.charCodeAt(0);
      if (index < 26) {
        return String.fromCharCode(baseCharCode + index);
      } else {
        const firstChar = String.fromCharCode(baseCharCode + Math.floor(index / 26) - 1);
        const secondChar = String.fromCharCode(baseCharCode + (index % 26));
        return firstChar + secondChar;
      }
    }
    
    // Assign alphabetic codes to categories
    allCategories.forEach((category, categoryIndex) => {
      const categoryCode = generateAlphabeticCode(categoryIndex);

      // Add category-only mapping (e.g., "A": { category: "Category Name" })
      mapping[categoryCode] = {
        category: category.name
      };

      // Add to reverse mapping for categories
      if (!reverseMapping[category.name]) {
        reverseMapping[category.name] = {
          categoryCode: categoryCode,
          filters: {}
        };
      }
      
      // Sort filters alphabetically by name
      const filters = [...categoryToFiltersMap[category.name]];
      filters.sort((a, b) => a.name.localeCompare(b.name));
      
      // Assign numeric codes to filters
      filters.forEach((filter, filterIndex) => {
        const filterCode = (filterIndex + 1).toString();
        const mappingCode = categoryCode + filterCode;
        
        // Add to mapping
        mapping[mappingCode] = {
          category: category.name,
          filter: filter.name
        };
        
        // Add to reverse mapping for filters
        reverseMapping[category.name].filters[filter.name] = mappingCode;
      });
    });
    
    // Create the final mapping object
    const finalMapping = {
      mapping: mapping,
      reverseMapping: reverseMapping,
      metadata: {
        generatedAt: new Date().toISOString(),
        totalCategories: allCategories.length,
        totalCategoryOnlyMappings: allCategories.length,
        totalFilterMappings: Object.keys(mapping).length - allCategories.length,
        totalMappings: Object.keys(mapping).length
      }
    };
    
    // Save the mapping to a file
    const outputPath = path.join(__dirname, 'category_filter_mapping.json');
    fs.writeFileSync(outputPath, JSON.stringify(finalMapping, null, 2), 'utf8');
    
    console.log(`Generated mapping with ${allCategories.length} categories and ${Object.keys(mapping).length} total mappings`);
    console.log(`Saved mapping to: ${outputPath}`);
    
    return finalMapping;
  } catch (error) {
    console.error(`Error generating category filter mapping: ${error.message}`);
    return null;
  }
}

// Run the function if this script is executed directly
if (require.main === module) {
  generateCategoryFilterMapping();
}

// Export the function
module.exports = generateCategoryFilterMapping;