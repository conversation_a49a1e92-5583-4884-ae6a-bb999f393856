#!/usr/bin/env node

/**
 * Telegram <PERSON><PERSON> Starter <PERSON>t
 * 
 * This script starts the Swiggy Deals Telegram Bot
 * Make sure to set your bot token before running
 */

const { startBot, stopBot, isBotRunning } = require('./telegramBot');
const BotSingleton = require('./botSingleton');

// Process-level singleton check using a lock file
const fs = require('fs');
const path = require('path');

const LOCK_FILE = path.join(__dirname, '.bot.lock');

function createLockFile() {
  try {
    if (fs.existsSync(LOCK_FILE)) {
      const lockData = fs.readFileSync(LOCK_FILE, 'utf8');
      const { pid, timestamp } = JSON.parse(lockData);

      // Check if the process is still running
      try {
        process.kill(pid, 0); // This doesn't kill, just checks if process exists
        console.log(`⚠️ Another bot instance is already running (PID: ${pid})`);
        console.log('If you believe this is an error, delete the .bot.lock file and try again.');
        process.exit(1);
      } catch (error) {
        // Process doesn't exist, remove stale lock file
        console.log('🧹 Removing stale lock file...');
        fs.unlinkSync(LOCK_FILE);
      }
    }

    // Create new lock file
    const lockData = {
      pid: process.pid,
      timestamp: new Date().toISOString(),
      command: process.argv.join(' ')
    };

    fs.writeFileSync(LOCK_FILE, JSON.stringify(lockData, null, 2));
    console.log(`🔒 Created lock file (PID: ${process.pid})`);
  } catch (error) {
    console.error('❌ Error managing lock file:', error.message);
  }
}

function removeLockFile() {
  try {
    if (fs.existsSync(LOCK_FILE)) {
      fs.unlinkSync(LOCK_FILE);
      console.log('🔓 Removed lock file');
    }
  } catch (error) {
    console.error('❌ Error removing lock file:', error.message);
  }
}

// Start the bot
async function main() {
  try {
    // Create process lock
    createLockFile();

    console.log('🚀 Starting Swiggy Deals Telegram Bot...');
    console.log('');

    // Start bot via singleton
    await startBot();

    console.log('✅ Bot started successfully!');

  } catch (error) {
    console.error('❌ Failed to start bot:', error.message);
    removeLockFile();
    process.exit(1);
  }
}

// Handle graceful shutdown
async function shutdown(signal) {
  console.log(`\n🛑 Received ${signal}, shutting down bot...`);

  try {
    if (isBotRunning()) {
      await stopBot();
    }
    BotSingleton.reset();
    removeLockFile();
    console.log('✅ Bot shutdown complete');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error.message);
    removeLockFile();
    process.exit(1);
  }
}

process.on('SIGINT', () => shutdown('SIGINT'));
process.on('SIGTERM', () => shutdown('SIGTERM'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  removeLockFile();
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  removeLockFile();
  process.exit(1);
});

// Start the application
main();
