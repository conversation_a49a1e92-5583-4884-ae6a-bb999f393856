{"data": {"statusMessage": "", "pageOffset": {"nextOffset": "3", "widgetOffset": {}, "nextEndPoint": ""}, "cards": [{"card": {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": {"title": "SNACKS & DRINKS", "subtitle": "", "action": null, "imageId": "", "headerStyling": {"padding": {"left": 16, "top": 0, "right": 16, "bottom": 20}, "titleSize": 0, "subtitleSize": 0, "titleColor": "text_high_emphasis", "subtitleColor": "", "titleFontName": "FONT_NAME_V2_OVERLINE_NEUTRAL_BOLD_SPACED", "subtitleTopPadding": 0, "imageStyle": null, "showSeparator": true, "subtitleFontName": "FONT_NAME_INVALID", "colorMap": {}, "gradientStyle": null, "alignment": "ALIGNMENT_INVALID"}, "leadingText": "", "visibility": false, "tooltip": "", "analytics": null, "collapseConfig": null, "secondaryAction": null, "shareCta": null, "isBackNavigation": false, "chevronHidden": false, "subtitlePrefixIcon": ""}, "layout": {"rows": 2, "columns": 4, "horizontalScrollEnabled": false, "shouldSnap": false, "itemSpacing": 8, "lineSpacing": 20, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 16, "top": 0, "right": 16, "bottom": 32}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": null, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_DARK"}}, "ordering": "ORDERING_ROW_WISE", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "114602", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.ImageInfoLayoutCard", "info": [{"id": "6822eeebed32000001e25a46", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/ba386d44-a570-47cb-9b7d-9c02554b06ef_3977c023-7f0f-45f0-aea5-11bc873a5546", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Cold Drinks and Juices&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Cold Drinks and Juices", "widgetId": "114602"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Cold Drinks and Juices", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeebed32000001e25a57", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/4fe55f10-32b9-4762-8d41-be76ea7f67a8_cc703675-e18b-4331-9617-db8a881f0ab2", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Ice Creams and Frozen Desserts&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Ice Creams and Frozen Desserts", "widgetId": "114602"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Ice Creams and Frozen Desserts", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeebed32000001e25a62", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/70be19a4-dc51-4ab3-969b-65736cd2cdf6_c0031c9c-5f6e-422c-a84b-1428e1787d28", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Chips and Namkeens&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Chips and Namkeens", "widgetId": "114602"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Chips and Namkeens", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeebed32000001e25a70", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/a5c36315-21b0-4dd9-8afc-80210e95df0c_77305056-9bb7-47a4-822d-6ee188bd00f6", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Chocolates&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Chocolates", "widgetId": "114602"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Chocolates", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeebed32000001e25a7c", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/da9542e9-0c6b-4fc3-bd65-422ab537a4aa_ac5adc39-bf55-492f-94d4-1c0ac65dea46", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Noodles, Pasta, Vermicelli&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Noodles, Pasta, <PERSON><PERSON><PERSON><PERSON>", "widgetId": "114602"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Noodles, Pasta, <PERSON><PERSON><PERSON><PERSON>", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeebed32000001e25a87", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/7e355480-0793-4ac0-a0d7-ca7236109bf7_6bf77c55-7d76-4a96-ab88-9d0690dbd822", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Frozen Food&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Frozen Food", "widgetId": "114602"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Frozen Food", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeebed32000001e25a90", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/f028cb2e-9593-48c0-95c5-86af909d7198_5ef3e945-a61d-4487-a50f-f5e341b1eee4", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Sweets&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Sweets", "widgetId": "114602"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Sweets", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeebed32000001e25a98", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/9cd93ba2-f843-4b57-9c08-56b9a78bcd50_b45772ad-2905-4b44-9c02-fbcbe43bc67a", "action": {"link": "swiggy://ageConsent?url=swiggy%3A%2F%2Fstores%2Finstamart%2Fcategory-listing%3FcategoryName%3DPaan+Corner%26storeId%3D1403687%26offset%3D0%26filterName%3D%26taxonomyType%3DSpeciality+taxonomy+3%26showAgeConsent%3Dtrue", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "<PERSON><PERSON>", "widgetId": "114602"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "<PERSON><PERSON>", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}], "style": {"width": {"type": "TYPE_RELATIVE", "value": 0.25, "reference": "RELATIVE_DIMENSION_REFERENCE_CONTAINER_WIDTH"}, "height": {"type": "TYPE_RELATIVE", "value": 1.1315789, "reference": "RELATIVE_DIMENSION_REFERENCE_WIDTH"}}, "widgetType": "WIDGET_TYPE_IMAGE_WITH_TEXT"}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "114602"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "114602", "entityType": "IM_WIDGET"}, "relevance": null}}, {"card": {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": {"title": " BEAUTY & WELLNESS", "subtitle": "", "action": null, "imageId": "", "headerStyling": {"padding": {"left": 16, "top": 0, "right": 16, "bottom": 20}, "titleSize": 0, "subtitleSize": 0, "titleColor": "text_high_emphasis", "subtitleColor": "", "titleFontName": "FONT_NAME_V2_OVERLINE_NEUTRAL_BOLD_SPACED", "subtitleTopPadding": 0, "imageStyle": null, "showSeparator": true, "subtitleFontName": "FONT_NAME_INVALID", "colorMap": {}, "gradientStyle": null, "alignment": "ALIGNMENT_INVALID"}, "leadingText": "", "visibility": false, "tooltip": "", "analytics": null, "collapseConfig": null, "secondaryAction": null, "shareCta": null, "isBackNavigation": false, "chevronHidden": false, "subtitlePrefixIcon": ""}, "layout": {"rows": 3, "columns": 4, "horizontalScrollEnabled": false, "shouldSnap": false, "itemSpacing": 8, "lineSpacing": 20, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 16, "top": 0, "right": 16, "bottom": 32}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": null, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_DARK"}}, "ordering": "ORDERING_ROW_WISE", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "114603", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.ImageInfoLayoutCard", "info": [{"id": "6822eeefed32000001e25b4b", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/062ce6fe-c90e-4017-831b-c931f11b2c24_c5520d03-1113-4832-a1ed-a119cdfa2902", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Bath and Body&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Bath and Body", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Bath and Body", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25b59", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/7dd2200a-cb1c-41d9-b1e8-46f38473553c_159a7f53-ba98-4d60-850a-4c6190902c57", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Hair Care&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Hair Care", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Hair Care", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25b62", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/742eda61-6595-416f-9053-a22333a0ff94_2bbb3337-9f69-4f9d-ae1b-b306e3e53e86", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Skincare&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Skincare", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Skincare", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25b6a", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/693e91dc-ce1a-4dee-9646-ee3b90eabff7_5faab24b-46ab-41f7-ae12-4d29c6dedb76", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Makeup&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Makeup", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Makeup", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25b71", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/9fcdb58a-f84e-4a63-94dd-2d85dc0288c2_a326cf61-577b-44c5-a296-1c9f4898df40", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Oral Care&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Oral Care", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Oral Care", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25b79", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/ac9c2e1b-1791-40ec-bd2f-b0036a737f34_9a79e7e7-799e-4d9b-b043-c6a2e8fe76cb", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Grooming&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Grooming", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Grooming", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25b89", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/ed3380cf-9912-4eb9-9127-3745d77973eb_a6944c2b-75b9-44f2-9697-517615d7af62", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Baby Care&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Baby Care", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Baby Care", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25b9a", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/2840ac14-2e8d-4424-841b-0a3625359b29_ce126c72-564e-40b2-9ee3-925f7ad0d037", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Fragrances&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Fragrances", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Fragrances", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25ba2", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/b86a895c-e4e8-4653-bda9-633e953e64fc_34e3a030-9ae0-4be4-bf29-664dbe9416a7", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Protein and Supplements&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Protein and Supplements", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Protein and Supplements", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25baf", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/408f229e-01c6-4afd-929b-0ace3ccd8bc4_70ce3354-bfd7-4b03-9972-238d77503f24", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Feminine Hygiene&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Feminine Hygiene", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Feminine Hygiene", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25bb7", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/dd818ead-6ea9-4c81-a133-2e1a446a98a3_9c80e128-d615-42a0-b7d7-70ac0687408e", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Sexual Wellness&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Sexual Wellness", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Sexual Wellness", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeefed32000001e25bbf", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/a234c550-ec96-4130-a7ed-ae067ac82e33_d7161c27-69fd-405a-a691-8d01d9ddb375", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Health and Pharma&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Health and Pharma", "widgetId": "114603"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Health and Pharma", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}], "style": {"width": {"type": "TYPE_RELATIVE", "value": 0.25, "reference": "RELATIVE_DIMENSION_REFERENCE_CONTAINER_WIDTH"}, "height": {"type": "TYPE_RELATIVE", "value": 1.1315789, "reference": "RELATIVE_DIMENSION_REFERENCE_WIDTH"}}, "widgetType": "WIDGET_TYPE_IMAGE_WITH_TEXT"}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "114603"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "114603", "entityType": "IM_WIDGET"}, "relevance": null}}, {"card": {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": {"title": " HOUSEHOLD & LIFESTYLE", "subtitle": "", "action": null, "imageId": "", "headerStyling": {"padding": {"left": 16, "top": 0, "right": 16, "bottom": 20}, "titleSize": 0, "subtitleSize": 0, "titleColor": "text_high_emphasis", "subtitleColor": "", "titleFontName": "FONT_NAME_V2_OVERLINE_NEUTRAL_BOLD_SPACED", "subtitleTopPadding": 0, "imageStyle": null, "showSeparator": true, "subtitleFontName": "FONT_NAME_INVALID", "colorMap": {}, "gradientStyle": null, "alignment": "ALIGNMENT_INVALID"}, "leadingText": "", "visibility": false, "tooltip": "", "analytics": null, "collapseConfig": null, "secondaryAction": null, "shareCta": null, "isBackNavigation": false, "chevronHidden": false, "subtitlePrefixIcon": ""}, "layout": {"rows": 3, "columns": 4, "horizontalScrollEnabled": false, "shouldSnap": false, "itemSpacing": 8, "lineSpacing": 20, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 16, "top": 0, "right": 16, "bottom": 32}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": null, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_DARK"}}, "ordering": "ORDERING_ROW_WISE", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "114604", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.ImageInfoLayoutCard", "info": [{"id": "6822eef009ab2e00019aa5e2", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/c34ad44b-5b83-4b51-a254-f472c175fd4e_4a8b97f3-6a60-4404-a0ec-93eb72db73e1", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Home and Furnishing&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Home and Furnishing", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Home and Furnishing", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa5f3", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/c416dbf8-210d-480e-94ca-805ddb720a77_925991fe-2da3-49b0-b0d1-3d29a07174d2", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Kitchen and Dining&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Kitchen and Dining", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Kitchen and Dining", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa601", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/6976d426-ca5f-4071-8ae2-ab684888645a_e83f0668-cdd1-40f7-950c-11f51b43f351", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Cleaning Essentials&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Cleaning Essentials", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Cleaning Essentials", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa614", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/11bdd409-0389-4572-9d4b-06d3235c7aaf_7434539d-4963-49d6-8234-3a85d1d7ec76", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Clothing&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Clothing", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Clothing", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa621", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/dd815b3d-ce43-454a-aef0-846a8fca399c_4ce8b093-a81e-484b-a35c-6ca079145ac8", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Mobiles and Electronics &storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Mobiles and Electronics ", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Mobiles and Electronics ", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa630", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/ae055b85-221c-42dc-87cb-efae5e048e9a_8f020cc6-fea1-4a9c-9235-7cec4b0206b2", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Appliances&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Appliances", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Appliances", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa63f", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/fa971c1e-4344-41a4-85e5-10d921df0818_13beb56f-0d66-403d-839f-eb63fdf25a83", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Books and Stationery&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Books and Stationery", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Books and Stationery", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa649", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/1e2fd208-7180-46bc-954f-db70777eba12_556f0a21-db31-41f3-bcc0-c48a33e1afd6", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Jewellery and Accessories&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Jewellery and Accessories", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Jewellery and Accessories", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa65d", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/3accc5d4-38b2-4487-b3b5-5891bf4806ea_a9c87ee0-3dbb-4cb3-9ee2-a4b421823f45", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Puja&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "<PERSON><PERSON><PERSON>", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "<PERSON><PERSON><PERSON>", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa670", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/26e9ad5c-4299-42a1-8536-c72dfdf419c0_be8dbebf-8cff-4118-9e81-6085959c39b0", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Toys and Games&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Toys and Games", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Toys and Games", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa67f", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/65126402-7605-4caf-974e-4acc8514fb41_6660a2f1-7912-4432-b9dd-90a23fa99b6b", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Sports and Fitness&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Sports and Fitness", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Sports and Fitness", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eef009ab2e00019aa68e", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/f00fffd7-11da-402f-bf95-17b07b8c0fda_5745df60-e848-427e-af64-abb9a91d6949", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Pet Supplies&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Pet Supplies", "widgetId": "114604"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Pet Supplies", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}], "style": {"width": {"type": "TYPE_RELATIVE", "value": 0.25, "reference": "RELATIVE_DIMENSION_REFERENCE_CONTAINER_WIDTH"}, "height": {"type": "TYPE_RELATIVE", "value": 1.1315789, "reference": "RELATIVE_DIMENSION_REFERENCE_WIDTH"}}, "widgetType": "WIDGET_TYPE_IMAGE_WITH_TEXT"}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "114604"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "114604", "entityType": "IM_WIDGET"}, "relevance": null}}, {"card": {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": {"title": "SHOP BY STORE", "subtitle": "", "action": null, "imageId": "", "headerStyling": {"padding": {"left": 16, "top": 0, "right": 16, "bottom": 20}, "titleSize": 0, "subtitleSize": 0, "titleColor": "text_high_emphasis", "subtitleColor": "", "titleFontName": "FONT_NAME_V2_OVERLINE_NEUTRAL_BOLD_SPACED", "subtitleTopPadding": 0, "imageStyle": null, "showSeparator": true, "subtitleFontName": "FONT_NAME_INVALID", "colorMap": {}, "gradientStyle": null, "alignment": "ALIGNMENT_INVALID"}, "leadingText": "", "visibility": false, "tooltip": "", "analytics": null, "collapseConfig": null, "secondaryAction": null, "shareCta": null, "isBackNavigation": false, "chevronHidden": false, "subtitlePrefixIcon": ""}, "layout": {"rows": 2, "columns": 4, "horizontalScrollEnabled": true, "shouldSnap": false, "itemSpacing": 8, "lineSpacing": 16, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 16, "top": 0, "right": 16, "bottom": 16}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": {"isScrollEnabled": false, "autoScrollDuration": 4, "autoScrollResetDuration": 0, "autoScrollType": "AUTO_SCROLL_TYPE_INVALID"}, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#00FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": null}, "ordering": "ORDERING_INVALID", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "112840", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.ImageInfoLayoutCard", "info": [{"id": "4972878", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/4/8/70670590-644d-4a3d-b5c3-1a0608a5dc97_PartyStore120X160.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=11539&customerPage=STORES_MxN_42", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "4972878"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=11539&customerPage=STORES_MxN_42", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "3039280", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2024/11/22/ed460c31-111c-4717-971d-c2811926a2df_GiftStore120x1604.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=5274&customerPage=STORES_MxN_46", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "3039280"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=5274&customerPage=STORES_MxN_46", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "3518968", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2024/10/9/23e58e2a-322c-4790-90e4-c792a136ed73_GourmetStore1.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=5249&customerPage=STORES_MxN_39", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "3518968"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=5249&customerPage=STORES_MxN_39", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "5508621", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/6/19/a4456f30-db3f-4407-8e79-b5cfd99f9de1_petStore120X160.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=14898&customerPage=STORES_MxN_60", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "5508621"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=14898&customerPage=STORES_MxN_60", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "2477308", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2024/5/30/0295d265-d28f-41ae-a359-219970b8777a_Pooja.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=5025&customerPage=STORES_MxN_31", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "2477308"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=5025&customerPage=STORES_MxN_31", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "3039336", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2024/8/3/0d69458a-b254-429d-910d-681e16d63b77_Healthwellness1.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=5522&customerPage=STORES_MxN_50", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "3039336"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=5522&customerPage=STORES_MxN_50", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "4387512", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/2/3/68ee8d74-aceb-4e1d-8304-e9ec2273276b_Flavourofindia120X1602.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=9115&customerPage=STORES_MxN_90", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "4387512"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=9115&customerPage=STORES_MxN_90", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "5178598", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/5/14/d9ac67b7-10e3-4e5c-9d98-ad4f9f18f566_travelStore120X160.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=12115&customerPage=STORES_MxN_18", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "5178598"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=12115&customerPage=STORES_MxN_18", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "2477309", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/6/19/b7e08d6d-b699-4641-8c77-45eb462161c1_toyStore120X160.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=12947&customerPage=STORES_MxN_50", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "2477309"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=12947&customerPage=STORES_MxN_50", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}], "style": {"width": {"type": "TYPE_ABSOLUTE", "value": 102, "reference": "RELATIVE_DIMENSION_REFERENCE_INVALID"}, "height": {"type": "TYPE_ABSOLUTE", "value": 137, "reference": "RELATIVE_DIMENSION_REFERENCE_INVALID"}}, "widgetType": "WIDGET_TYPE_IMAGE_WITH_TEXT"}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "112840"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "", "entityType": ""}, "relevance": null}}], "communication": null, "firstOffsetRequest": false, "ribbons": [], "cacheExpiryTime": 300, "nextFetch": 0, "configs": {}, "hasFooter": false, "encodedIrctcInfo": "", "searchResultsOffset": "0", "requestId": "5c33d0bc-f178-44f9-ac5c-e49fb21f35d7", "statusCode": 0}, "statusCode": 0}