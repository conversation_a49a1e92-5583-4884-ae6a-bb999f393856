{"data": {"statusMessage": "", "pageOffset": {"nextOffset": "2", "widgetOffset": {}, "nextEndPoint": ""}, "cards": [{"card": {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": {"title": "Brand of the Hour- Portronics", "subtitle": "Only till 2 PM", "action": null, "imageId": "", "headerStyling": {"padding": {"left": 16, "top": 16, "right": 16, "bottom": 16}, "titleSize": 0, "subtitleSize": 0, "titleColor": "", "subtitleColor": "text_color_low_emphasis", "titleFontName": "FONT_NAME_HEADER_H5", "subtitleTopPadding": 0, "imageStyle": null, "showSeparator": false, "subtitleFontName": "FONT_NAME_BODY_B3_REG", "colorMap": {}, "gradientStyle": null, "alignment": "ALIGNMENT_INVALID"}, "leadingText": "", "visibility": false, "tooltip": "", "analytics": null, "collapseConfig": null, "secondaryAction": null, "shareCta": null, "isBackNavigation": false, "chevronHidden": false, "subtitlePrefixIcon": ""}, "layout": {"rows": 1, "columns": 16, "horizontalScrollEnabled": true, "shouldSnap": false, "itemSpacing": 4, "lineSpacing": 16, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 12, "top": 0, "right": 12, "bottom": 32}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": null, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_DARK"}}, "ordering": "ORDERING_INVALID", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "166428", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.im.v1.ItemCollectionCard", "viewType": "VIEW_TYPE_YGTI", "items": [{"displayName": "Portronics Conch Theta C In-Ear Wired Earphone With Mic, Type C (White)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "CXQVJRAAHA", "spinId": "NUCDCJBKHH", "quantityDescription": "1 unit", "displayName": "Portronics Conch Theta C In-Ear Wired Earphone With Mic, Type C (White)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2025/7/28/021220db-3a91-4a6a-a461-43ba1938b6ae_NUCDCJBKHH.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/16c6651b-17e7-4664-ab68-12e2547c6783_690476_2.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/3da03e15-e896-4d9e-8833-9b6565f3a8cb_101472_3.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/0a443b8e-4924-4b71-aca0-84ab07eac25c_690476_4.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/92fd96aa-250c-47be-b6c7-9ec72a479130_101472_5.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/8325e11e-ae93-42f5-94ee-aa9960aae30a_101472_6.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/8eaa771a-8fa4-46b2-81bb-076d07fff667_690476_7.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "799", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "249", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "550", "nanos": 0}, "offerApplied": {"listingDescription": "68% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Earbuds and Headsets", "dimensions": {"lengthInCm": 94, "widthInCm": 1, "heightInCm": 1, "volumeInCc": 94}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 3, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 47, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2025/7/28/021220db-3a91-4a6a-a461-43ba1938b6ae_NUCDCJBKHH.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/16c6651b-17e7-4664-ab68-12e2547c6783_690476_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/3da03e15-e896-4d9e-8833-9b6565f3a8cb_101472_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/0a443b8e-4924-4b71-aca0-84ab07eac25c_690476_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/92fd96aa-250c-47be-b6c7-9ec72a479130_101472_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/8325e11e-ae93-42f5-94ee-aa9960aae30a_101472_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/8eaa771a-8fa4-46b2-81bb-076d07fff667_690476_7.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.3", "valueColor": "positive", "count": "863", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"CXQVJRAAHA\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "HNQMOU0OEO", "showQuantity": false, "adTrackingContext": "", "parentProductId": "9J8OJ3C7TK"}, {"displayName": "Portronics Harmonics Z7 In-Ear Bluetooth Neckband With Mic, 40 Hrs Playtime, IPX4, Bluetooth v5.3 (Black)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "LYWFY31NFX", "spinId": "4I2XYLGZXD", "quantityDescription": "1 unit", "displayName": "Portronics Harmonics Z7 In-Ear Bluetooth Neckband With Mic, 40 Hrs Playtime, IPX4, Bluetooth v5.3 (Black)", "brandName": "Portronics", "imageIds": ["37fd91bd7b94730d175a27e12ec386c7", "7883c663983db87373770f9c9f3e602f", "932667287765de58b741b30f6d9ffddc", "63285203430d260deed880d09e24bc8a", "2a3833b04b5302b6e90f2158e3ad9be4", "30a8241e575e42fb3f273a9957dd1daf", "NI_CATALOG/IMAGES/CIW/2024/7/26/533e921c-0d28-47e3-a548-4f0364727067_HomeElectricalAppliances_2YUVAQ6AFS_AL6.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "1999", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "549", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "1450", "nanos": 0}, "offerApplied": {"listingDescription": "72% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Earbuds and Headsets", "dimensions": {"lengthInCm": 32, "widthInCm": 18, "heightInCm": 3, "volumeInCc": 1728}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 150, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "37fd91bd7b94730d175a27e12ec386c7", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "7883c663983db87373770f9c9f3e602f", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "932667287765de58b741b30f6d9ffddc", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "63285203430d260deed880d09e24bc8a", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "2a3833b04b5302b6e90f2158e3ad9be4", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "30a8241e575e42fb3f273a9957dd1daf", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/26/533e921c-0d28-47e3-a548-4f0364727067_HomeElectricalAppliances_2YUVAQ6AFS_AL6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.3", "valueColor": "positive", "count": "575", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"LYWFY31NFX\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "43H6H4FFCM", "showQuantity": false, "adTrackingContext": "", "parentProductId": "TAW8C58CFI"}, {"displayName": "Portronics Adapto 20 20W Single Port Adapter (White)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "BQBU5P4WYF", "spinId": "RFJTW7TADI", "quantityDescription": "1 unit", "displayName": "Portronics Adapto 20 20W Single Port Adapter (White)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/7/4/3ea1f8b5-f10d-41a6-aae2-f921a81ecbf2_ElectronicsandWearables_RFJTW7TADI_MN.png", "NI_CATALOG/IMAGES/CIW/2024/7/4/0f16950c-08ae-4103-a60a-89d80821a12b_ElectronicsandWearables_RFJTW7TADI_AL1.png", "NI_CATALOG/IMAGES/CIW/2024/7/4/cc447272-9969-4b9a-8cfc-86c821b5649a_ElectronicsandWearables_RFJTW7TADI_AL2.png", "NI_CATALOG/IMAGES/CIW/2024/7/4/1db27969-6474-4efd-b7ad-10260f3e3a92_ElectronicsandWearables_RFJTW7TADI_AL3.png", "NI_CATALOG/IMAGES/CIW/2024/7/4/f1fe05db-ae9e-4b3f-b0a8-900063387a8a_ElectronicsandWearables_RFJTW7TADI_AL4.png", "NI_CATALOG/IMAGES/CIW/2024/7/4/9efe9bce-f0a6-43c6-8c76-8b958f8da8a2_ElectronicsandWearables_RFJTW7TADI_AL5.png", "NI_CATALOG/IMAGES/CIW/2024/7/4/c726bbba-c4a7-4fd8-906f-d674f416fce4_ElectronicsandWearables_RFJTW7TADI_AL6.png", "NI_CATALOG/IMAGES/CIW/2024/7/25/4a1ad443-a6e8-4809-8ba9-bd1bbd254bd3_ElectronicsandWearables_R5J2LQUKCF_AL6.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "1499", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "299", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "1200", "nanos": 0}, "offerApplied": {"listingDescription": "80% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Powerbanks and Chargers", "dimensions": {"lengthInCm": 11, "widthInCm": 7, "heightInCm": 3, "volumeInCc": 231}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 5, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 10, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/7/4/3ea1f8b5-f10d-41a6-aae2-f921a81ecbf2_ElectronicsandWearables_RFJTW7TADI_MN.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/4/0f16950c-08ae-4103-a60a-89d80821a12b_ElectronicsandWearables_RFJTW7TADI_AL1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/4/cc447272-9969-4b9a-8cfc-86c821b5649a_ElectronicsandWearables_RFJTW7TADI_AL2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/4/1db27969-6474-4efd-b7ad-10260f3e3a92_ElectronicsandWearables_RFJTW7TADI_AL3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/4/f1fe05db-ae9e-4b3f-b0a8-900063387a8a_ElectronicsandWearables_RFJTW7TADI_AL4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/4/9efe9bce-f0a6-43c6-8c76-8b958f8da8a2_ElectronicsandWearables_RFJTW7TADI_AL5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/4/c726bbba-c4a7-4fd8-906f-d674f416fce4_ElectronicsandWearables_RFJTW7TADI_AL6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/25/4a1ad443-a6e8-4809-8ba9-bd1bbd254bd3_ElectronicsandWearables_R5J2LQUKCF_AL6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.2", "valueColor": "positive", "count": "355", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"BQBU5P4WYF\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "HNL3INV951", "showQuantity": false, "adTrackingContext": "", "parentProductId": "N6753MLDD3"}, {"displayName": "Portronics POR 1926 Mini Pocket Fan - White", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "7JOH3PU107", "spinId": "4R059OGQH8", "quantityDescription": "1 unit", "displayName": "Portronics POR 1926 Mini Pocket Fan - White", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/7/1/1873d179-cb57-49cb-80c7-a302eb458763_HomeElectricalAppliances_4R059OGQH8_AL1.png", "NI_CATALOG/IMAGES/CIW/2024/7/1/aafe9c23-6e0b-4151-9417-c0d550dff006_HomeElectricalAppliances_4R059OGQH8_MN.png", "NI_CATALOG/IMAGES/CIW/2024/7/1/f51b2583-2032-4f11-adf6-adcffc91bf89_HomeElectricalAppliances_4R059OGQH8_AL2.png", "NI_CATALOG/IMAGES/CIW/2024/7/1/02d051c0-02fa-4f63-b480-93f5adfce576_HomeElectricalAppliances_4R059OGQH8_AL3.png", "NI_CATALOG/IMAGES/CIW/2024/7/1/95633d4c-ee50-48fe-afa0-e7b18f90863b_HomeElectricalAppliances_4R059OGQH8_AL4.png", "NI_CATALOG/IMAGES/CIW/2024/7/1/8ad87d02-15dc-47ec-a1c3-772852e36cb3_HomeElectricalAppliances_4R059OGQH8_AL5.png", "NI_CATALOG/IMAGES/CIW/2024/7/1/0f72bb9c-5348-46d5-90ec-3b0ada24f289_HomeElectricalAppliances_4R059OGQH8_AL6.png", "NI_CATALOG/IMAGES/CIW/2024/7/26/533e921c-0d28-47e3-a548-4f0364727067_HomeElectricalAppliances_2YUVAQ6AFS_AL6.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "1499", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "699", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "800", "nanos": 0}, "offerApplied": {"listingDescription": "53% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Home Appliances", "dimensions": {"lengthInCm": 300, "widthInCm": 1, "heightInCm": 1, "volumeInCc": 300}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 150, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/7/1/1873d179-cb57-49cb-80c7-a302eb458763_HomeElectricalAppliances_4R059OGQH8_AL1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/1/aafe9c23-6e0b-4151-9417-c0d550dff006_HomeElectricalAppliances_4R059OGQH8_MN.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/1/f51b2583-2032-4f11-adf6-adcffc91bf89_HomeElectricalAppliances_4R059OGQH8_AL2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/1/02d051c0-02fa-4f63-b480-93f5adfce576_HomeElectricalAppliances_4R059OGQH8_AL3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/1/95633d4c-ee50-48fe-afa0-e7b18f90863b_HomeElectricalAppliances_4R059OGQH8_AL4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/1/8ad87d02-15dc-47ec-a1c3-772852e36cb3_HomeElectricalAppliances_4R059OGQH8_AL5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/1/0f72bb9c-5348-46d5-90ec-3b0ada24f289_HomeElectricalAppliances_4R059OGQH8_AL6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/7/26/533e921c-0d28-47e3-a548-4f0364727067_HomeElectricalAppliances_2YUVAQ6AFS_AL6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.5", "valueColor": "positive", "count": "288", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"7JOH3PU107\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "BXMUMIBFH6", "showQuantity": false, "adTrackingContext": "", "parentProductId": "AIDO02CZOU"}, {"displayName": "Portronics Aero Breeze Portable Table Fan 178mm,USB Rechargeable Fan,3 Speed Airflow,Battery Powered", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "0Q19XXCQJF", "spinId": "Q3SZNQ05P3", "quantityDescription": "1 Piece", "displayName": "Portronics Aero Breeze Portable Table Fan 178mm,USB Rechargeable Fan,3 Speed Airflow,Battery Powered", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2025/2/1/513b0429-276a-449b-8a01-0925b3f674bd_689432_1.png", "NI_CATALOG/IMAGES/CIW/2025/2/1/7bc84e14-e167-428a-b65d-e91ac68a2f59_689432_2.png", "NI_CATALOG/IMAGES/CIW/2025/2/1/6edb5d40-33e7-40cc-808d-af1c4b7cf69a_689432_3.png", "NI_CATALOG/IMAGES/CIW/2025/2/1/d62a7a77-c5e7-4107-bda8-3a22e7ba88f4_689432_4.png", "NI_CATALOG/IMAGES/CIW/2025/2/1/c07b9226-c8db-426e-99c1-53cea3153d2d_689432_5.png", "NI_CATALOG/IMAGES/CIW/2025/2/1/66a832ec-c1d1-413d-ada1-d1fbdc59bd94_689432_6.png", "NI_CATALOG/IMAGES/CIW/2025/2/1/d3f0551a-b987-48ca-9c3f-ad4b94729656_689432_7.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "3999", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "1099", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "2900", "nanos": 0}, "offerApplied": {"listingDescription": "72% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Home Appliances", "dimensions": {"lengthInCm": 24, "widthInCm": 19, "heightInCm": 10, "volumeInCc": 4560}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 510, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2025/2/1/513b0429-276a-449b-8a01-0925b3f674bd_689432_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/1/7bc84e14-e167-428a-b65d-e91ac68a2f59_689432_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/1/6edb5d40-33e7-40cc-808d-af1c4b7cf69a_689432_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/1/d62a7a77-c5e7-4107-bda8-3a22e7ba88f4_689432_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/1/c07b9226-c8db-426e-99c1-53cea3153d2d_689432_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/1/66a832ec-c1d1-413d-ada1-d1fbdc59bd94_689432_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/1/d3f0551a-b987-48ca-9c3f-ad4b94729656_689432_7.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.0", "valueColor": "positive", "count": "168", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"0Q19XXCQJF\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "XYRM2HFNV3", "showQuantity": false, "adTrackingContext": "", "parentProductId": "W8R4JAOQVC"}, {"displayName": "Portronics Toad 35 Wireless Mouse with Silent Clicks & Adjustable Optical Sensitivity (Black)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "Y2H6D8L71Y", "spinId": "OVVY9FPBMA", "quantityDescription": "1 unit", "displayName": "Portronics Toad 35 Wireless Mouse with Silent Clicks & Adjustable Optical Sensitivity (Black)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2025/2/11/7de457be-74df-4ff5-b7b6-66c72bc6d484_616098_1.png", "NI_CATALOG/IMAGES/CIW/2025/2/11/7dfbd146-c091-4048-9a5d-ff038dbeb8ec_616098_2.png", "NI_CATALOG/IMAGES/CIW/2025/2/11/c0aa3ef3-3c22-4ab9-ab27-bf2d739a566f_616098_3.png", "NI_CATALOG/IMAGES/CIW/2025/2/11/c6cd0486-9501-4599-9d2c-c7125ad5e038_616098_4.png", "NI_CATALOG/IMAGES/CIW/2025/2/11/c401e9e7-**************-a4c24a4f720b_616098_5.png", "NI_CATALOG/IMAGES/CIW/2025/2/11/3f592148-65b5-4c07-945c-ed161f45363d_616098_6.png", "NI_CATALOG/IMAGES/CIW/2025/2/11/648996d5-48df-4128-9036-f1c228cbe240_616098_7.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "699", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "259", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "440", "nanos": 0}, "offerApplied": {"listingDescription": "62% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Computer Accessories", "dimensions": {"lengthInCm": 172, "widthInCm": 1, "heightInCm": 1, "volumeInCc": 172}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 86, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2025/2/11/7de457be-74df-4ff5-b7b6-66c72bc6d484_616098_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/11/7dfbd146-c091-4048-9a5d-ff038dbeb8ec_616098_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/11/c0aa3ef3-3c22-4ab9-ab27-bf2d739a566f_616098_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/11/c6cd0486-9501-4599-9d2c-c7125ad5e038_616098_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/11/c401e9e7-**************-a4c24a4f720b_616098_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/11/3f592148-65b5-4c07-945c-ed161f45363d_616098_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/2/11/648996d5-48df-4128-9036-f1c228cbe240_616098_7.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.3", "valueColor": "positive", "count": "121", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"Y2H6D8L71Y\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "H521XGHBL2", "showQuantity": false, "adTrackingContext": "", "parentProductId": "POIIU63F72"}, {"displayName": "Portronics Luxcell Bind Mini 10k Powerbank, 10000mAh, 22.5W, Dual Output Ports (Black)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "5DSTQLH0W9", "spinId": "8Q837X69FL", "quantityDescription": "1 unit", "displayName": "Portronics Luxcell Bind Mini 10k Powerbank, 10000mAh, 22.5W, Dual Output Ports (Black)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/9/7/35d3ec4b-0118-48b9-aaa2-07b69c5a6869_242108_1.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/dbbaf672-47db-4180-a1a2-218d83fd129c_242108_2.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/b1be15af-ed02-4f3b-995e-23ef6b2680fd_242108_3.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/4188e57e-34c1-47c2-a58f-e1315f093420_242108_4.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/1503c79b-6ecb-4f15-95f2-34cd13431cf3_242108_5.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/19e62ab9-93c6-4a53-af68-ab9fb3f28954_242108_6.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/7b122129-8e15-469b-8d16-f7d33ab647fa_242108_7.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "2499", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "719", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "1780", "nanos": 0}, "offerApplied": {"listingDescription": "71% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Powerbanks and Chargers", "dimensions": {"lengthInCm": 400, "widthInCm": 1, "heightInCm": 1, "volumeInCc": 400}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 200, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/35d3ec4b-0118-48b9-aaa2-07b69c5a6869_242108_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/dbbaf672-47db-4180-a1a2-218d83fd129c_242108_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/b1be15af-ed02-4f3b-995e-23ef6b2680fd_242108_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/4188e57e-34c1-47c2-a58f-e1315f093420_242108_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/1503c79b-6ecb-4f15-95f2-34cd13431cf3_242108_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/19e62ab9-93c6-4a53-af68-ab9fb3f28954_242108_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/7b122129-8e15-469b-8d16-f7d33ab647fa_242108_7.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.4", "valueColor": "positive", "count": "264", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"5DSTQLH0W9\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "3FGZUJ781E", "showQuantity": false, "adTrackingContext": "", "parentProductId": "AGNO6769R4"}, {"displayName": "Portronics Car Power Rapid Car Charger with Dual Port and (18 W) for Fast Charging (Black)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "H4OKSA4CYT", "spinId": "3FICT7VQPJ", "quantityDescription": "1 unit", "displayName": "Portronics Car Power Rapid Car Charger with Dual Port and (18 W) for Fast Charging (Black)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/12/1/1f45f786-8e4f-474c-924c-43ab62f285f3_23913_1.png", "NI_CATALOG/IMAGES/CIW/2024/12/1/1ba0e053-da3c-44f8-b1cf-50bc0f54989d_23913_2.png", "NI_CATALOG/IMAGES/CIW/2024/12/1/c7578f1a-1e80-43ed-a4f0-5198fc567611_23913_3.jpg", "NI_CATALOG/IMAGES/CIW/2024/12/1/ed409849-a6ab-41a7-9144-e890548c4e71_23913_4.jpg", "NI_CATALOG/IMAGES/CIW/2024/12/1/72d271a5-ebbc-4706-8391-3b079a72510b_23913_5.jpg", "NI_CATALOG/IMAGES/CIW/2024/12/1/8665b6bf-4351-4b1a-9f22-c2126b20f926_23913_6.jpg", "NI_CATALOG/IMAGES/CIW/2024/12/1/77a24821-d69b-4a70-9124-d1b804269ac1_23913_7.jpg", "NI_CATALOG/IMAGES/CIW/2024/12/1/6a52d7c4-6f2b-42d4-ac4d-ced5576a53ff_23913_8.jpg"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "699", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "269", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "430", "nanos": 0}, "offerApplied": {"listingDescription": "61% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Powerbanks and Chargers", "dimensions": {"lengthInCm": 2, "widthInCm": 1, "heightInCm": 1, "volumeInCc": 2}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 1, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/12/1/1f45f786-8e4f-474c-924c-43ab62f285f3_23913_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/1/1ba0e053-da3c-44f8-b1cf-50bc0f54989d_23913_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/1/c7578f1a-1e80-43ed-a4f0-5198fc567611_23913_3.jpg", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/1/ed409849-a6ab-41a7-9144-e890548c4e71_23913_4.jpg", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/1/72d271a5-ebbc-4706-8391-3b079a72510b_23913_5.jpg", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/1/8665b6bf-4351-4b1a-9f22-c2126b20f926_23913_6.jpg", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/1/77a24821-d69b-4a70-9124-d1b804269ac1_23913_7.jpg", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/1/6a52d7c4-6f2b-42d4-ac4d-ced5576a53ff_23913_8.jpg", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.5", "valueColor": "positive", "count": "141", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"H4OKSA4CYT\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "2KLYI4W3AX", "showQuantity": false, "adTrackingContext": "", "parentProductId": "5EKP4NXTF2"}, {"displayName": "Portronics Mport 52 (7-in-1) USB-C Hub with 4K HDMI, Type-C PD & Data, USB 3.0 & 2.0", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "2TQBWQLX82", "spinId": "MBR5M4V1Z5", "quantityDescription": "1 Piece", "displayName": "Portronics Mport 52 (7-in-1) USB-C Hub with 4K HDMI, Type-C PD & Data, USB 3.0 & 2.0", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/12/5/d0f3d33e-05f1-4db8-aebe-0c303847eed0_850215_1.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/134b1a93-0b79-460d-bd4c-07f75e5c1ee9_850215_2.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/18705d40-f45f-4596-b35f-b48d90f9eeda_850215_3.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/1a298c77-64dc-47b1-a01d-6789cc2d79ca_850215_4.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/7e246d39-b178-4075-b87d-c8d77639178a_850215_5.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/6b65b418-b1b2-4fa7-ba2d-9c06eda36591_850215_6.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "2999", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "849", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "2150", "nanos": 0}, "offerApplied": {"listingDescription": "71% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Computer Accessories", "dimensions": {"lengthInCm": 17, "widthInCm": 8, "heightInCm": 2, "volumeInCc": 272}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 62, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/d0f3d33e-05f1-4db8-aebe-0c303847eed0_850215_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/134b1a93-0b79-460d-bd4c-07f75e5c1ee9_850215_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/18705d40-f45f-4596-b35f-b48d90f9eeda_850215_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/1a298c77-64dc-47b1-a01d-6789cc2d79ca_850215_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/7e246d39-b178-4075-b87d-c8d77639178a_850215_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/6b65b418-b1b2-4fa7-ba2d-9c06eda36591_850215_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.5", "valueColor": "positive", "count": "52", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"2TQBWQLX82\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "NDDQXD9G1X", "showQuantity": false, "adTrackingContext": "", "parentProductId": "YHEQITDPFM"}, {"displayName": "Portronics Luxcell B12 Powerbank, 10000mAh, 12W, Dual Output Ports (Black)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "AL4IG8FJYX", "spinId": "Z9OD0R45T1", "quantityDescription": "1 unit", "displayName": "Portronics Luxcell B12 Powerbank, 10000mAh, 12W, Dual Output Ports (Black)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/10/15/b7da9a3a-4ca2-4519-ba50-ad52023cdd17_836464_1.jpg", "NI_CATALOG/IMAGES/CIW/2024/9/7/c3be3916-91f7-4c78-bbd3-35ec4b80fd7e_836464_2.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/9cf7727a-39fc-4351-927d-1867063ab912_836464_3.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/a759b83c-7de0-4bc2-8250-1a51eb3d76d8_836464_4.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/25ef774c-4480-48ab-8f03-a301ec90818c_836464_5.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/924397f2-eee4-44c9-b09b-10562d8b67a0_836464_6.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/4f0d8953-fb05-4569-ba46-0e801395d6dd_836464_7.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/cfcb3e63-a2cf-4f2d-a52e-542e7a600c45_836464_1.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "1499", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "499", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "1000", "nanos": 0}, "offerApplied": {"listingDescription": "66% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Powerbanks and Chargers", "dimensions": {"lengthInCm": 500, "widthInCm": 1, "heightInCm": 1, "volumeInCc": 500}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 250, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/10/15/b7da9a3a-4ca2-4519-ba50-ad52023cdd17_836464_1.jpg", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/c3be3916-91f7-4c78-bbd3-35ec4b80fd7e_836464_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/9cf7727a-39fc-4351-927d-1867063ab912_836464_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/a759b83c-7de0-4bc2-8250-1a51eb3d76d8_836464_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/25ef774c-4480-48ab-8f03-a301ec90818c_836464_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/924397f2-eee4-44c9-b09b-10562d8b67a0_836464_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/4f0d8953-fb05-4569-ba46-0e801395d6dd_836464_7.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/cfcb3e63-a2cf-4f2d-a52e-542e7a600c45_836464_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "3.8", "valueColor": "positive", "count": "117", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"AL4IG8FJYX\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "16NYAK0E3E", "showQuantity": false, "adTrackingContext": "", "parentProductId": "S3V9SB9R57"}, {"displayName": "Portronics Luxcell MagClick Powerbank, 10000 mAh, 22.5W, Dual Output Ports (Black)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "YQO2733NZF", "spinId": "1BQ4MK7S4R", "quantityDescription": "1 unit", "displayName": "Portronics Luxcell MagClick Powerbank, 10000 mAh, 22.5W, Dual Output Ports (Black)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/9/7/7128d1b6-87c1-46d0-9058-287a8c137186_484472_1.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/1c483fa1-83fd-46c2-9e93-c9a0d54291c9_484472_2.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/117d8138-b7be-4d34-a164-8b0e7825c6be_484472_3.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/79ee731c-9d5b-4208-8e20-ec4dfb75a55b_484472_4.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/9c049229-6dc2-4fa0-8280-af0e9fa4e24a_484472_5.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/c18710fb-fbb8-419b-bbbb-9ebe33b6a41c_484472_6.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/abfba978-ba49-423a-a791-89a9eb802ba1_484472_7.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "2999", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "1229", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "1770", "nanos": 0}, "offerApplied": {"listingDescription": "59% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Powerbanks and Chargers", "dimensions": {"lengthInCm": 16, "widthInCm": 9, "heightInCm": 2, "volumeInCc": 288}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 3, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 230, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/7128d1b6-87c1-46d0-9058-287a8c137186_484472_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/1c483fa1-83fd-46c2-9e93-c9a0d54291c9_484472_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/117d8138-b7be-4d34-a164-8b0e7825c6be_484472_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/79ee731c-9d5b-4208-8e20-ec4dfb75a55b_484472_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/9c049229-6dc2-4fa0-8280-af0e9fa4e24a_484472_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/c18710fb-fbb8-419b-bbbb-9ebe33b6a41c_484472_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/abfba978-ba49-423a-a791-89a9eb802ba1_484472_7.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.3", "valueColor": "positive", "count": "227", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"YQO2733NZF\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "DECHYE48XO", "showQuantity": false, "adTrackingContext": "", "parentProductId": "33545WSSBZ"}, {"displayName": "Portronics My Buddy D Wood Multipurpose Movable & Adjustable Table for Computer & Laptop (Black)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "Y03JL7HJJR", "spinId": "CJ3XTL59WN", "quantityDescription": "1 Piece", "displayName": "Portronics My Buddy D Wood Multipurpose Movable & Adjustable Table for Computer & Laptop (Black)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2025/4/20/7736735e-5432-49ea-8593-0810fddf2e7d_544870_1.png", "NI_CATALOG/IMAGES/CIW/2025/4/20/d35ad511-090e-40ac-b8b0-8e6a90d9d4fd_544870_2.png", "NI_CATALOG/IMAGES/CIW/2025/4/20/c51ba7cc-7e39-4bf3-9719-c39a359dd349_544870_3.png", "NI_CATALOG/IMAGES/CIW/2025/4/20/3cba5911-3302-4f1f-bfa9-89a70418fbd6_544870_4.png", "NI_CATALOG/IMAGES/CIW/2025/4/20/f3b4cf01-5b5d-4559-a0d1-7c73be0ba314_544870_5.png", "NI_CATALOG/IMAGES/CIW/2025/4/20/a99e8e52-0200-41e5-a845-d7cc33705384_544870_6.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "2999", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "1099", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "1900", "nanos": 0}, "offerApplied": {"listingDescription": "63% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Computer Accessories", "dimensions": {"lengthInCm": 61, "widthInCm": 44, "heightInCm": 7, "volumeInCc": 0}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 5000, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2025/4/20/7736735e-5432-49ea-8593-0810fddf2e7d_544870_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/20/d35ad511-090e-40ac-b8b0-8e6a90d9d4fd_544870_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/20/c51ba7cc-7e39-4bf3-9719-c39a359dd349_544870_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/20/3cba5911-3302-4f1f-bfa9-89a70418fbd6_544870_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/20/f3b4cf01-5b5d-4559-a0d1-7c73be0ba314_544870_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/20/a99e8e52-0200-41e5-a845-d7cc33705384_544870_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.1", "valueColor": "positive", "count": "57", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"Y03JL7HJJR\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "ATXTK5LPO9", "showQuantity": false, "adTrackingContext": "", "parentProductId": "HLIFRCECG2"}, {"displayName": "Portronics Clamp M4 Car Phone Holder Stand with 360 Degree Rotation, Strong Grip, Suction Cup Mount, Single Hand Use, Shockproof Build (Black)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "I64DTNYN0S", "spinId": "97BLMN4LUJ", "quantityDescription": "1 Piece", "displayName": "Portronics Clamp M4 Car Phone Holder Stand with 360 Degree Rotation, Strong Grip, Suction Cup Mount, Single Hand Use, Shockproof Build (Black)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2025/4/21/caeab9ec-9b49-4e8f-ab2b-9f7ddf75236f_685148_1.png", "NI_CATALOG/IMAGES/CIW/2025/4/21/413f9e39-641a-418e-a015-fff36fc77e7a_685148_2.png", "NI_CATALOG/IMAGES/CIW/2025/4/21/2a54a674-c3d5-4548-8ed8-756e55e173c2_685148_3.png", "NI_CATALOG/IMAGES/CIW/2025/4/21/d37c5a8a-c0de-4282-a68d-f924f88e40bf_685148_4.png", "NI_CATALOG/IMAGES/CIW/2025/4/21/1974b6f5-3cb1-481a-9f4f-05ea63d815bf_685148_5.png", "NI_CATALOG/IMAGES/CIW/2025/4/21/6ca5e58e-f504-48b7-bbde-36dd0c025288_685148_6.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "799", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "179", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "620", "nanos": 0}, "offerApplied": {"listingDescription": "77% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Phone Accessories", "dimensions": {"lengthInCm": 10, "widthInCm": 8, "heightInCm": 12, "volumeInCc": 0}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 150, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2025/4/21/caeab9ec-9b49-4e8f-ab2b-9f7ddf75236f_685148_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/21/413f9e39-641a-418e-a015-fff36fc77e7a_685148_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/21/2a54a674-c3d5-4548-8ed8-756e55e173c2_685148_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/21/d37c5a8a-c0de-4282-a68d-f924f88e40bf_685148_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/21/1974b6f5-3cb1-481a-9f4f-05ea63d815bf_685148_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2025/4/21/6ca5e58e-f504-48b7-bbde-36dd0c025288_685148_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.2", "valueColor": "positive", "count": "152", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"I64DTNYN0S\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "LDUJK7O7AB", "showQuantity": false, "adTrackingContext": "", "parentProductId": "8FT7SN8U5Q"}, {"displayName": "Portronics Harmonics Twins S18 In-Ear Truly Wireless Earbuds With Mic, 30 Hours Playtime, IPX5, Bluetooth v5.3 (Black)", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "JD4NLTNP5N", "spinId": "KA6RROWAM0", "quantityDescription": "1 unit", "displayName": "Portronics Harmonics Twins S18 In-Ear Truly Wireless Earbuds With Mic, 30 Hours Playtime, IPX5, Bluetooth v5.3 (Black)", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/9/7/2c7105e2-d60c-47f2-a0ee-b492d575ff47_170614_1.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/13a0d634-1bae-40ed-a029-85a3d278b773_170614_2.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/96a81bb6-3116-4845-b58a-891fc8f8f2a2_170614_3.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/d0eddc0c-a220-41af-93ba-645eaedc30bc_170614_4.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/7dac1158-17e3-4566-ad29-57f98458a3b9_170614_5.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/233d328b-507d-40f1-ac0a-23b95f21295d_170614_6.png", "NI_CATALOG/IMAGES/CIW/2024/9/7/d5b46138-8261-4b36-b641-3a2f05d4e2af_170614_7.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "1999", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "619", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "1380", "nanos": 0}, "offerApplied": {"listingDescription": "69% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Earbuds and Headsets", "dimensions": {"lengthInCm": 9, "widthInCm": 10, "heightInCm": 5, "volumeInCc": 450}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 224, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/2c7105e2-d60c-47f2-a0ee-b492d575ff47_170614_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/13a0d634-1bae-40ed-a029-85a3d278b773_170614_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/96a81bb6-3116-4845-b58a-891fc8f8f2a2_170614_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/d0eddc0c-a220-41af-93ba-645eaedc30bc_170614_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/7dac1158-17e3-4566-ad29-57f98458a3b9_170614_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/233d328b-507d-40f1-ac0a-23b95f21295d_170614_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/9/7/d5b46138-8261-4b36-b641-3a2f05d4e2af_170614_7.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.0", "valueColor": "positive", "count": "210", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"JD4NLTNP5N\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "HH3F6M0Y8L", "showQuantity": false, "adTrackingContext": "", "parentProductId": "Z5D7RS2P99"}, {"displayName": "Portronics Konnect Y 60W Type C to Type C Cable Strong Braided Cable 1.5M Length,4.5mm Thickness", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "ILSP1PS6VV", "spinId": "8QSHH0RF7S", "quantityDescription": "1 Piece", "displayName": "Portronics Konnect Y 60W Type C to Type C Cable Strong Braided Cable 1.5M Length,4.5mm Thickness", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/12/5/11cfaa30-d366-4a83-87a2-e5d5d5151be8_654701_1.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/*************-4e98-93b4-e315aa9fbfaa_654701_2.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/cd3dae01-38a9-49ce-beec-aec54b477ba0_654701_3.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/0d8d3ba2-9e23-4d2a-a9df-889fe51b87d6_654701_4.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/be88bbde-085f-4d5b-8210-37fb6dbd51c9_654701_5.png", "NI_CATALOG/IMAGES/CIW/2024/12/5/e8339b08-71a9-4fd7-8086-1aa60d8c6768_654701_6.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "799", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "165", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "634", "nanos": 0}, "offerApplied": {"listingDescription": "79% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Powerbanks and Chargers", "dimensions": {"lengthInCm": 86, "widthInCm": 1, "heightInCm": 1, "volumeInCc": 86}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 2, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 43, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/11cfaa30-d366-4a83-87a2-e5d5d5151be8_654701_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/*************-4e98-93b4-e315aa9fbfaa_654701_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/cd3dae01-38a9-49ce-beec-aec54b477ba0_654701_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/0d8d3ba2-9e23-4d2a-a9df-889fe51b87d6_654701_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/be88bbde-085f-4d5b-8210-37fb6dbd51c9_654701_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/5/e8339b08-71a9-4fd7-8086-1aa60d8c6768_654701_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "4.4", "valueColor": "positive", "count": "15", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"ILSP1PS6VV\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "3OHGGWUHOD", "showQuantity": false, "adTrackingContext": "", "parentProductId": "6GS53BFR0E"}, {"displayName": "Portronics Mopcop Rechargeable Vacuum Cleaner with Mutli-functional Nozzles & Single-Hand Use", "brand": "Portronics", "inStock": true, "isAvail": true, "variations": [{"skuId": "JA3YF2P5MH", "spinId": "VOV6N7FPIZ", "quantityDescription": "1 Piece", "displayName": "Portronics Mopcop Rechargeable Vacuum Cleaner with Mutli-functional Nozzles & Single-Hand Use", "brandName": "Portronics", "imageIds": ["NI_CATALOG/IMAGES/CIW/2024/12/4/d993d6e8-f7d8-4c83-80ec-309a11efe05b_778721_1.png", "NI_CATALOG/IMAGES/CIW/2024/12/4/22c7b868-5e7a-4066-9487-5c53c2559512_778721_2.png", "NI_CATALOG/IMAGES/CIW/2024/12/4/11851138-6592-45e5-a2a6-c3d1dc1d688f_778721_3.png", "NI_CATALOG/IMAGES/CIW/2024/12/4/01a54f51-0edd-43ea-814a-84db2d592602_778721_4.png", "NI_CATALOG/IMAGES/CIW/2024/12/4/17930774-beda-4f89-a002-f703dec9f813_778721_5.png", "NI_CATALOG/IMAGES/CIW/2024/12/4/e6fe69a2-4932-4cf7-817e-ae2388d9c34c_778721_6.png"], "listingVariant": true, "superSaver": false, "price": {"mrp": {"currencyCode": "INR", "units": "2999", "nanos": 0}, "offerPrice": {"currencyCode": "INR", "units": "1139", "nanos": 0}, "unitLevelPrice": "", "discountValue": {"currencyCode": "INR", "units": "1860", "nanos": 0}, "offerApplied": {"listingDescription": "62% OFF", "superOffer": false, "movThreshold": 0}, "maxSaverPrice": null, "flashSalePriceDetails": null}, "category": "Home Appliances", "dimensions": {"lengthInCm": 1720, "widthInCm": 1, "heightInCm": 1, "volumeInCc": 1720}, "slotInfo": {"isAvail": true, "message": ""}, "cartAllowedQuantity": {"allowedQuantity": 1, "quantityLimitBreachedMessage": "That’s all we have in stock at the moment!"}, "inventory": {"inStock": true}, "weightInGrams": 860, "volumetricWeight": 0, "subCategoryType": "", "secondaryQuantityDescription": "", "podId": "1403687", "couponLessOffers": [], "shortDescription": "", "externalPharmacyItem": false, "rxRequired": false, "variationTags": [], "medias": [{"id": "NI_CATALOG/IMAGES/CIW/2024/12/4/d993d6e8-f7d8-4c83-80ec-309a11efe05b_778721_1.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/4/22c7b868-5e7a-4066-9487-5c53c2559512_778721_2.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/4/11851138-6592-45e5-a2a6-c3d1dc1d688f_778721_3.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/4/01a54f51-0edd-43ea-814a-84db2d592602_778721_4.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/4/17930774-beda-4f89-a002-f703dec9f813_778721_5.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}, {"id": "NI_CATALOG/IMAGES/CIW/2024/12/4/e6fe69a2-4932-4cf7-817e-ae2388d9c34c_778721_6.png", "thumbnailId": "", "type": "MEDIA_TYPE_IMAGE", "aspectRatio": null}], "attributeTags": [], "rating": {"value": "3.8", "valueColor": "positive", "count": "19", "backgroundColor": "#DFF9EE", "bgGradient": null, "ratingIconColor": "", "ratingCountTextColor": "", "ratingFontName": "FONT_NAME_INVALID", "ratingCountFontName": "FONT_NAME_INVALID"}, "sla": null, "vegClassifier": "VEG_CLASSIFIER_INVALID", "emiInfo": null}], "badges": [], "source": "", "analytics": {"screenName": "", "context": "{\"itemId\":\"JA3YF2P5MH\",\"slotAvailability\":true}", "objectValue": "", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "attributionContext": "", "extraFields": {}, "position": 0}, "productId": "JDK8J1355G", "showQuantity": false, "adTrackingContext": "", "parentProductId": "191DGPDRVB"}], "itemStyle": {"width": {"type": "TYPE_ABSOLUTE", "value": 94, "reference": "RELATIVE_DIMENSION_REFERENCE_INVALID"}, "height": {"type": "TYPE_ABSOLUTE", "value": 178, "reference": "RELATIVE_DIMENSION_REFERENCE_INVALID"}}, "themedItemStyle": null, "themeType": "THEME_TYPE_INVALID", "variantAttributes": null}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"collectionId": "219049", "widgetId": "166428"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "166428", "entityType": "IM_WIDGET"}, "relevance": null}}, {"cardList": {"cards": [{"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": null, "layout": {"rows": 1, "columns": 1, "horizontalScrollEnabled": true, "shouldSnap": false, "itemSpacing": 16, "lineSpacing": 16, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 0, "top": 0, "right": 0, "bottom": 0}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": {"isScrollEnabled": false, "autoScrollDuration": 4, "autoScrollResetDuration": 0, "autoScrollType": "AUTO_SCROLL_TYPE_INVALID"}, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#00FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": null}, "ordering": "ORDERING_INVALID", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "183306", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.MediaInfoLayoutCard", "info": [{"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.LotteInfoLayoutCard.LotteCardInfo", "id": "5839309", "lotteUrl": "https://media-assets.swiggy.com/MERCHANDISING_BANNERS/LOTTIES/MERCH/2025/7/25/9e9c8441-8a28-43b7-918e-00a302dc6c62_NagPanchami.json", "action": {"link": "swiggy://stores/instamart/campaign-collection/listing?custom_back=true&layoutId=16855", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{\"clusterId\":\"184429\"}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "5839309"}, "position": 0}, "lotteStyle": {"repeatCount": "-1", "repeatMode": "LOTTE_REPEAT_MODE_RESTART"}, "thumbnail": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityId": "/instamart/campaign-collection/listing?custom_back=true&layoutId=16855", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "lotteFallback": "", "slaDetails": null, "externalMarketing": null, "lotteBehaviour": "LOTTE_BEHAVIOUR_INVALID", "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "dynamicInfo": [], "dynamicInfoV2": [], "title": "", "subTitle": "", "titleIcon": "", "autoScrollTimer": 0, "overlay": null, "darkMode": null}], "style": {"width": {"type": "TYPE_RELATIVE", "value": 1, "reference": "RELATIVE_DIMENSION_REFERENCE_CONTAINER_WIDTH"}, "height": {"type": "TYPE_RELATIVE", "value": 0.22222222, "reference": "RELATIVE_DIMENSION_REFERENCE_WIDTH"}, "borderStyle": null, "cornerRadius": null}, "aspectRatio": "", "mediaGridLayoutType": "MEDIA_INFO_WIDGET_TYPE_INVALID", "fabTimer": 0}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{\"clusterId\":\"184429\"}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "183306"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "", "entityType": ""}, "relevance": null}, {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": null, "layout": {"rows": 1, "columns": 4, "horizontalScrollEnabled": true, "shouldSnap": false, "itemSpacing": 8, "lineSpacing": 16, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 16, "top": 16, "right": 16, "bottom": 16}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": {"isScrollEnabled": false, "autoScrollDuration": 4, "autoScrollResetDuration": 0, "autoScrollType": "AUTO_SCROLL_TYPE_INVALID"}, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#00FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": null}, "ordering": "ORDERING_INVALID", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "183307", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.ImageInfoLayoutCard", "info": [{"id": "5839300", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/24/8ed63fcc-620c-49e2-ab25-9bf36d10f148_Pujaneedsflowers.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/listing?custom_back=true&layoutId=16453", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{\"clusterId\":\"184429\"}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "5839300"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/listing?custom_back=true&layoutId=16453", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "5839301", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/24/c580a458-534c-4c97-af26-d6c0dfbf50b4_MilkcurdHoney.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/listing?custom_back=true&layoutId=16455", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{\"clusterId\":\"184429\"}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "5839301"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/listing?custom_back=true&layoutId=16455", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "5839303", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/24/7cf41992-19af-46d2-931b-acc42305c0f6_Sweetsdessertmixes.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/listing?custom_back=true&layoutId=16464", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{\"clusterId\":\"184429\"}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "5839303"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/listing?custom_back=true&layoutId=16464", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "5839305", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/24/f24a003b-d26e-422d-9264-550cc9bc568c_Fruitsfastingneeds.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/listing?custom_back=true&layoutId=16452", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{\"clusterId\":\"184429\"}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "5839305"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/listing?custom_back=true&layoutId=16452", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}], "style": {"width": {"type": "TYPE_ABSOLUTE", "value": 90, "reference": "RELATIVE_DIMENSION_REFERENCE_INVALID"}, "height": {"type": "TYPE_ABSOLUTE", "value": 108, "reference": "RELATIVE_DIMENSION_REFERENCE_INVALID"}}, "widgetType": "WIDGET_TYPE_IMAGE_WITH_TEXT"}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{\"clusterId\":\"184429\"}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "183307"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "", "entityType": ""}, "relevance": null}, {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": null, "layout": {"rows": 1, "columns": 1, "horizontalScrollEnabled": true, "shouldSnap": false, "itemSpacing": 0, "lineSpacing": 16, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 0, "top": 0, "right": 0, "bottom": 0}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": {"isScrollEnabled": false, "autoScrollDuration": 4, "autoScrollResetDuration": 0, "autoScrollType": "AUTO_SCROLL_TYPE_INVALID"}, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#00FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": null}, "ordering": "ORDERING_INVALID", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "183308", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.ImageInfoLayoutCard", "info": [{"id": "5880359", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/28/f0141942-1747-4bf4-bd6d-326a43b4ad50_Strip360x25FreeTickets.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/mxn?custom_back=true&layoutId=16805&customerPage=STORES_MxN_10", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{\"clusterId\":\"184429\"}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "5880359"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=16805&customerPage=STORES_MxN_10", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}], "style": {"width": {"type": "TYPE_RELATIVE", "value": 1, "reference": "RELATIVE_DIMENSION_REFERENCE_CONTAINER_WIDTH"}, "height": {"type": "TYPE_RELATIVE", "value": 0.07777778, "reference": "RELATIVE_DIMENSION_REFERENCE_WIDTH"}}, "widgetType": "WIDGET_TYPE_IMAGE_WITH_TEXT"}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{\"clusterId\":\"184429\"}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "183308"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "", "entityType": ""}, "relevance": null}, {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.BackgroundLayoutWidget", "items": [{"background": {"image": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/24/6f0784f9-3695-4241-beff-11b41c079606_BG360X200.png", "backgroundColor": "", "textColor": "", "isFullScreen": false, "scrolledSelectedPathwayColor": ""}, "showBottomRoundedCorners": false, "accountIcon": "", "id": "184429", "aspectRatio": 0, "widgetIds": ["183306", "183307", "183308"], "childWidgetDirection": "top", "theme": "THEME_TYPE_INVALID", "entityId": "", "entityType": "", "frequencyCapping": null}], "layout": null, "showScaleAnimation": false, "stopScaleAnimation": false, "showShimmerAsLoader": false}, "relevance": null}], "cardListType": "CARD_LIST_TYPE_CLUSTER_WIDGET", "cardListViewType": "CARD_LIST_VIEW_TYPE_INVALID"}}, {"card": {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": null, "layout": {"rows": 1, "columns": 1, "horizontalScrollEnabled": true, "shouldSnap": false, "itemSpacing": 16, "lineSpacing": 16, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 0, "top": 16, "right": 16, "bottom": 0}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": {"isScrollEnabled": false, "autoScrollDuration": 4, "autoScrollResetDuration": 0, "autoScrollType": "AUTO_SCROLL_TYPE_INVALID"}, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#00FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": null}, "ordering": "ORDERING_INVALID", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "159249", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.ImageInfoLayoutCard", "info": [{"id": "3423295", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/3/30/42dd9721-5dce-41fe-b7bc-79bc649fc890_88de9d1a05464aba99322cf582610fbatransparent1.png", "action": null, "entityType": "static", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "3423295"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "N/A", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}], "style": {"width": {"type": "TYPE_RELATIVE", "value": 1, "reference": "RELATIVE_DIMENSION_REFERENCE_CONTAINER_WIDTH"}, "height": {"type": "TYPE_RELATIVE", "value": 0.027777778, "reference": "RELATIVE_DIMENSION_REFERENCE_WIDTH"}}, "widgetType": "WIDGET_TYPE_IMAGE_WITH_TEXT"}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "159249"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "", "entityType": ""}, "relevance": null}}, {"card": {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": null, "layout": {"rows": 1, "columns": 1, "horizontalScrollEnabled": false, "shouldSnap": false, "itemSpacing": 16, "lineSpacing": 16, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 16, "top": 0, "right": 16, "bottom": 10}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": {"isScrollEnabled": false, "autoScrollDuration": 4, "autoScrollResetDuration": 0, "autoScrollType": "AUTO_SCROLL_TYPE_INVALID"}, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#00FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": null}, "ordering": "ORDERING_INVALID", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "159090", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.ImageInfoLayoutCard", "info": [{"id": "5838653", "imageId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/23/6a7ef050-3d98-4030-8f08-ef114a04e42b_187.png", "action": {"link": "swiggy://stores/instamart/campaign-collection/listing?custom_back=true&layoutId=16559", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "deeplink", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "attributionContext": "", "extraFields": {"bannerId": "5838653"}, "position": 0}, "altText": "", "accessibility": {"altText": "", "altTextCta": ""}, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "/instamart/campaign-collection/listing?custom_back=true&layoutId=16559", "frequencyCapping": {"cappingEnabled": false, "cappingMode": ""}, "isManualAds": false, "externalMarketing": null, "description": "", "descriptionStyle": null, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}], "style": {"width": {"type": "TYPE_RELATIVE", "value": 1, "reference": "RELATIVE_DIMENSION_REFERENCE_CONTAINER_WIDTH"}, "height": {"type": "TYPE_RELATIVE", "value": 0.3597561, "reference": "RELATIVE_DIMENSION_REFERENCE_WIDTH"}}, "widgetType": "WIDGET_TYPE_IMAGE_WITH_TEXT"}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "159090"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "", "entityType": ""}, "relevance": null}}, {"card": {"card": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.GridWidget", "header": {"title": "GROCERY & KITCHEN", "subtitle": "", "action": null, "imageId": "", "headerStyling": {"padding": {"left": 16, "top": 0, "right": 16, "bottom": 20}, "titleSize": 0, "subtitleSize": 0, "titleColor": "text_high_emphasis", "subtitleColor": "", "titleFontName": "FONT_NAME_V2_OVERLINE_NEUTRAL_BOLD_SPACED", "subtitleTopPadding": 0, "imageStyle": null, "showSeparator": true, "subtitleFontName": "FONT_NAME_INVALID", "colorMap": {}, "gradientStyle": null, "alignment": "ALIGNMENT_INVALID"}, "leadingText": "", "visibility": false, "tooltip": "", "analytics": null, "collapseConfig": null, "secondaryAction": null, "shareCta": null, "isBackNavigation": false, "chevronHidden": false, "subtitlePrefixIcon": ""}, "layout": {"rows": 3, "columns": 4, "horizontalScrollEnabled": false, "shouldSnap": false, "itemSpacing": 8, "lineSpacing": 20, "widgetPadding": null, "containerStyle": {"containerPadding": {"left": 16, "top": 0, "right": 16, "bottom": 32}, "containerCornerRadius": null, "containerColor": "", "containerSeparatorConfig": null, "containerBackgroundGradient": null, "elevation": "ELEVATION_LEVEL_INVALID"}, "viewPort": 0, "scrollBar": null, "autoScrollConfig": null, "separatorConfig": null, "backgroundColor": "", "widgetBorderStyle": null, "widgetTheme": {"defaultMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_LIGHT"}, "darkMode": {"backgroundColour": "#FFFFFF", "theme": "THEME_TYPE_DARK"}}, "ordering": "ORDERING_ROW_WISE", "dotScrollBar": null, "displayStyle": "", "backgroundAssetId": "", "pageScrollBar": null, "scrollAnimationType": "SCROLL_ANIMATION_TYPE_INVALID", "foregroundAsset": null}, "id": "114601", "gridElements": {"infoWithStyle": {"@type": "type.googleapis.com/swiggy.gandalf.widgets.v2.ImageInfoLayoutCard", "info": [{"id": "6822eeeded32000001e25aa1", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/e9395c2f-a316-4899-8a05-c08afa34de5a_9ada60fa-2794-45d7-b780-50015de09230", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Fresh Vegetables&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Fresh Vegetables", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Fresh Vegetables", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25aae", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/7e47a6e7-f40d-457f-9b46-fa23e3e015d6_3954cc97-e485-4725-b595-3a6e7029211f", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Fresh Fruits&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Fresh Fruits", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Fresh Fruits", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25abb", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/a8042459-6e03-40b7-88e6-5d7acb437880_1f641338-2306-4841-97aa-cb65b497d132", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Dairy, Bread and Eggs&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Dairy, Bread and Eggs", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Dairy, Bread and Eggs", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25aca", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/1f081643-e392-4585-a8e7-04ace4ace1ef_2e314d6c-ed11-49af-8927-e3eadde4dbad", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Cereals and Breakfast&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Cereals and Breakfast", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Cereals and Breakfast", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25add", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/fe25fe8b-a26f-49dc-af50-c748fff09475_165b10b8-00b7-4837-a27e-6399a3c99c29", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Atta, Rice and Dal&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Atta, Rice and Dal", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Atta, Rice and Dal", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25aec", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/b5e07c24-ed2b-4101-a87d-aeed35b4e2e7_30d06ae8-5347-4961-8d68-02b189159321", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Oils and Ghee&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Oils and Ghee", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Oils and Ghee", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25af9", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/5fb9f911-93e8-47d2-afd3-a9c876bbffe2_f00a60a1-de42-4132-808e-dc1136c27bac", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Masalas&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Masalas", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Masalas", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25b06", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/49c9852b-967f-40ef-8ff1-6c28a4282c65_9dcb6679-51f7-45ae-a367-eb056a8713e2", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Dry Fruits and Seeds Mix&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Dry Fruits and Seeds Mix", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Dry Fruits and Seeds Mix", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25b13", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/93e396cf-71df-4ed3-9015-af13fa3c9081_ccdcba8a-aa86-4ff9-9229-ed8f6c53cac0", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Biscuits and Cakes&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Biscuits and Cakes", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Biscuits and Cakes", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25b24", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/c54610b9-868e-4fdd-83d5-4d723ee93261_d0ee1b6c-dd6a-45fc-8f99-929a56764014", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Tea, Coffee and Milk drinks&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Tea, Coffee and Milk drinks", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Tea, Coffee and Milk drinks", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25b31", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/49f512f4-5613-45af-bbd4-2eaa13906240_a026b538-95c9-4060-8cd0-0f44e0c2742f", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Sauces and Spreads&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Sauces and Spreads", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Sauces and Spreads", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}, {"id": "6822eeeded32000001e25b3c", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/774ad667-efe6-4145-ad58-3362632508fd_b8600286-4ff0-45bf-bfc6-45f4098cec35", "action": {"link": "swiggy://stores/instamart/category-listing?categoryName=Meat and Seafood&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false", "text": "", "type": "DEEPLINK", "metaInfo": null, "subtext": "", "disabledText": "", "additionalInfo": null, "strikethroughText": "", "tooltip": null, "dlsType": "", "analytics": null, "creativeId": ""}, "entityType": "", "isFrequencyCappingEnabled": false, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-category", "clickObjectName": "click-category", "attributionContext": "", "extraFields": {"l1NodeVal": "Meat and Seafood", "widgetId": "114601"}, "position": 0}, "altText": "", "accessibility": null, "entityCount": 0, "entityCountDescription": "", "slaDetails": null, "entityId": "", "frequencyCapping": null, "isManualAds": false, "externalMarketing": null, "description": "Meat and Seafood", "descriptionStyle": {"textColor": "text_high_emphasis", "fontName": "FONT_NAME_V2_BODY3_NEUTRAL_BOLD", "padding": {"left": 0, "top": 8, "right": 0, "bottom": 0}, "decoration": "TEXT_DECORATION_INVALID"}, "themeType": "THEME_TYPE_INVALID", "adProviderType": "AD_PROVIDER_TYPE_INVALID", "tag": "", "thumbnail": "", "backgroundImage": "", "title": "", "subTitle": "", "titleIcon": "", "aspectRatio": "", "autoScrollTimer": 0, "overlayIcon": null, "overlay": null}], "style": {"width": {"type": "TYPE_RELATIVE", "value": 0.25, "reference": "RELATIVE_DIMENSION_REFERENCE_CONTAINER_WIDTH"}, "height": {"type": "TYPE_RELATIVE", "value": 1.1315789, "reference": "RELATIVE_DIMENSION_REFERENCE_WIDTH"}}, "widgetType": "WIDGET_TYPE_IMAGE_WITH_TEXT"}}, "footer": null, "meta": null, "analytics": {"screenName": "", "context": "{}", "objectValue": "", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "attributionContext": "", "extraFields": {"widgetId": "114601"}, "position": 0}, "animation": null, "scrollAnchor": null, "frequencyCapping": null, "entityId": "114601", "entityType": "IM_WIDGET"}, "relevance": null}}], "communication": null, "firstOffsetRequest": false, "ribbons": [], "cacheExpiryTime": 300, "nextFetch": 0, "configs": {}, "hasFooter": false, "encodedIrctcInfo": "", "searchResultsOffset": "0", "requestId": "58394eca-4066-4108-bff6-15f8da07b5af", "statusCode": 0}, "statusCode": 0}