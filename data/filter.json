{"data": {"collectionData": {"id": 28924, "entityData": [{"id": "IU4NHXNSDS"}, {"id": "HFIDROW4TG"}, {"id": "B9PKP9GIF8"}, {"id": "ZSUPYR4BQP"}, {"id": "3TA5QPWH1O"}, {"id": "ENVHP3H2HL"}, {"id": "FDUBBSQG68"}, {"id": "4RL2JFCWQG"}, {"id": "QM6TKWBHJ9"}, {"id": "8BB9HM9FUS"}, {"id": "X6SSJHKVRS"}, {"id": "4CIAR6GSG0"}, {"id": "P45DGM8MTR"}, {"id": "LBYG20V3WS"}, {"id": "S10HQYDTR6"}, {"id": "JUJTP22IXN"}, {"id": "LM5GQQVUSN"}, {"id": "RE308ZYSKZ"}, {"id": "IHT9YBM83Q"}, {"id": "XC3J58DTKQ"}, {"id": "TT3ITP2XM0"}, {"id": "97OH73Q970"}, {"id": "XU3WG7463P"}, {"id": "GA8CI0464U"}, {"id": "H7XVS3WSFO"}, {"id": "5ZHRF1MPZ1"}, {"id": "A5YVJ7O232"}, {"id": "4VIWA2UA00"}, {"id": "CLNT4XC1NX"}, {"id": "OHLFU8GV7P"}, {"id": "R16TUIDXGD"}, {"id": "6VC1YLV21X"}, {"id": "QTUSKNCKVK"}, {"id": "KB3ZX88Z2H"}, {"id": "ISEZHYU95P"}, {"id": "ALIQ55CX0O"}, {"id": "DL60HRDWIA"}, {"id": "YIT3WIYDGW"}, {"id": "XWIDGWPV2Z"}, {"id": "JJNSECOQ3Z"}, {"id": "X7L4VSY9CN"}, {"id": "VHK566K0MT"}, {"id": "I0UKZ781P5"}, {"id": "43QXHQN6EW"}, {"id": "JVI9EAPTNX"}, {"id": "I2L4MBI0MH"}], "type": "DASH_ITEM", "context": {"tags": []}}, "pageNo": 0, "hasMore": true, "storeDetails": {"id": "1403687", "name": "Instamart", "address": "Ground Floor, Unit No CPM 35-B1-BG35, CPM 36-B1-BG36,CPM37-B1-BG37,<PERSON><PERSON> ,Sector 105, Mohali Punjab - 140307", "description": "", "active": true, "priority": 2, "locality": "Emaar The Views", "categories": [{"id": 52, "name": "Instamart"}], "tags": [], "sources": [{"source": {"id": 6, "name": "Offline Sourced"}, "id_in_source": ""}], "badges": [], "maxAllowedItemsInCart": 200, "maxAllowedCartWeightInGrams": 15000, "maxAllowedCartVolumeInCc": 42875, "minimumOrderValueInRs": 0, "subCategoryTypeMaxAllowedWeights": {}, "discountInfo": {"couponLessDiscounts": [{"description": "", "short_description": "", "discount_type": "FREEBIE", "logo_id": "e0cf25488aee88d25b9df510a56809f2", "title": "Sample - Buy any Rakhi and get a Kalyan voucher worth Rs.2100", "sub_title": "Sample - Buy any Rakhi and get a Kalyan voucher worth Rs.2100", "tnc": ["Offer valid till stocks last", "Offer valid for a limited period only", "Other TnCs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-08-10T18:29:00.000+0000", "offer_id": "b091ac92-fec2-46bb-9bde-c412d052d384", "minimum_order_value": 1, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=b091ac92-fec2-46bb-9bde-c412d052d384"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Rakhi items worth ₹1 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Convenience fees waived off", "sub_title": "Convenience fees waived off", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 30, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "aee47903-2307-4b74-beab-a16579105c14", "minimum_order_value": 1, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery on orders above ₹49", "sub_title": "Free Delivery on orders above ₹49", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2029-06-29T18:29:00.000+0000", "offer_id": "83c5bc5d-a07f-4ed9-bcca-0b0027901226", "minimum_order_value": 49, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "4a075af1-5370-4254-9293-dd09a1466672", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "579334a7-e682-41ee-a0dd-85b4192651b2", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "d328b922-37bb-4799-b45a-5419f19ead3c", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "117a395a-3e28-40fd-9cda-eabceab2398e", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "136a4e3a-03d3-4b50-b74e-de97600c7482", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "31bcfef4-6193-4de8-aa90-8310636b07f6", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "4a36cace-6f82-4b3a-989e-a781d0df00be", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "7a984027-c27f-4f77-be40-ec44fa05b077", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8167cae1-e162-4ead-851a-00c9fe3a7e92", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8271d0c9-a4ef-44c3-8a20-3b3b5edf467b", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "8e4d1422-bcb9-4168-b6ec-4858e2626685", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8f6ff210-6733-49da-b49a-41e8d354c820", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "93894e04-d811-4ef8-b965-5aa1c7a82fa5", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "bf8aeb64-5a8f-4728-93af-71bb5aac64f3", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy Mars Products worth Rs.299/- and get 5% off", "sub_title": "Buy Mars Products worth Rs.299/- and get 5% off", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-07-29T18:29:00.000+0000", "offer_id": "663381d2-e287-48d1-a6af-b78c3f498044", "minimum_order_value": 299, "discount_value": 5, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=663381d2-e287-48d1-a6af-b78c3f498044"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add MARS items worth ₹299 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy products worth Rs.799 from WishCare and get additional 5% off", "sub_title": "Buy products worth Rs.799 from WishCare and get additional 5% off", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "9797d066-eac8-4c22-8326-498b7678e7df", "minimum_order_value": 799, "discount_value": 5, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=9797d066-eac8-4c22-8326-498b7678e7df"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Wishcare items worth ₹799 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy Productsworth Rs.499 from Coloressence and get 3% discount", "sub_title": "Buy Productsworth Rs.499 from Coloressence and get 3% discount", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-08-31T18:29:00.000+0000", "offer_id": "828e45a0-29b6-4837-bea6-318eb590d9f1", "minimum_order_value": 499, "discount_value": 3, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=828e45a0-29b6-4837-bea6-318eb590d9f1"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Coloressence items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_CATEGORY%20/Quench%20logo.webp?updatedAt=1751957849182", "title": "Get Rs.100 off on order above Rs.399 on Quench products", "sub_title": "Get Rs.100 off on order above Rs.399 on Quench products", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 100, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "674efb29-b555-43e7-87e2-2ce4e4b011f4", "minimum_order_value": 399, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=674efb29-b555-43e7-87e2-2ce4e4b011f4"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Wella Professionals items worth ₹399 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 99 off on minimum Basket Value - Rs.999", "sub_title": "Rs. 99 off on minimum Basket Value - Rs.999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "54e73b98-dbd4-44c5-a08c-e26ecc72da37", "minimum_order_value": 999, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=54e73b98-dbd4-44c5-a08c-e26ecc72da37"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Purina items worth ₹999 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Flat ₹99 on orders Above ₹999", "sub_title": "Flat ₹99 on orders Above ₹999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "65e5e1dc-22f0-4601-91f8-bd3c9c9a5907", "minimum_order_value": 99, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=65e5e1dc-22f0-4601-91f8-bd3c9c9a5907"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Brainsmith  items worth ₹99 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Flat ₹99 off on orders Above ₹999.", "sub_title": "Flat ₹99 off on orders Above ₹999.", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "9f476014-1ecb-4550-9c1f-cdf0e6322772", "minimum_order_value": 999, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=9f476014-1ecb-4550-9c1f-cdf0e6322772"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Bamboo Nature items worth ₹999 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 55 off on minimum Basket Value - Rs.599", "sub_title": "Rs. 55 off on minimum Basket Value - Rs.599", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 55, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "7fd7af88-457e-4216-a1c1-49259b13a1c8", "minimum_order_value": 599, "discount_value": 55, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=7fd7af88-457e-4216-a1c1-49259b13a1c8"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Petcrux items worth ₹599 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 50 off on minimum Basket Value - Rs.499", "sub_title": "Rs. 50 off on minimum Basket Value - Rs.499", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 50, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "c92082ac-f117-4eed-b7c0-7cb803e5187c", "minimum_order_value": 499, "discount_value": 50, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=c92082ac-f117-4eed-b7c0-7cb803e5187c"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Himalaya items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_Category/images%20(1).png?updatedAt=1751466553287", "title": "Rs. 20 off on minimum Basket Value - Rs.499", "sub_title": "Rs. 20 off on minimum Basket Value - Rs.499", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 20, "valid_till": "2025-07-31T07:45:52.000+0000", "offer_id": "8b4c6182-c2e4-4deb-b4ce-85a747434bb6", "minimum_order_value": 499, "discount_value": 20, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=8b4c6182-c2e4-4deb-b4ce-85a747434bb6"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Torrent Pharma items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}], "superOffer": false, "discounts": [{"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_Category/Edgewell-Personal-Care-Stayfree-Logo-_3x_2cc54d21-3bdf-4a6a-bb1b-b5cc08753eba_540x_wfhgsh%20(1).png?updatedAt=1750431171496", "title": "Buy product worth ₹499 and Get ₹49 Off on selected Stayfree products", "sub_title": "Use code STAYFREE49 & get Get ₹49 Off on orders above ₹499", "tnc": ["Valid only on selected Stayfree products", "The cart can have other items too but the discount will be valid only on Stayfree products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "STAYFREE49", "super_offer": false, "minimum_order_value": 49900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "IM_assets/philips.png", "title": "Flat 10% off on selected range of Philips Avent Items", "sub_title": "Flat 10% off on selected range of Philips Avent Items", "tnc": ["Valid only on Philips Avent Items", "The cart can have other items too but the discount will be valid only on Philips Avent Items", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "AVENT10", "super_offer": false, "minimum_order_value": 100, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Get Flat 10% off on sunfeast Biscuits & cakes  worth Rs.149 or above", "sub_title": "Get Flat 10% off on sunfeast Biscuits & cakes  worth Rs.149 or above", "tnc": ["Valid only on Sunfeast worth ₹149 or more", "The cart can have other items too but the discount will be valid only on The Sunfeast products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "ITC10", "super_offer": false, "minimum_order_value": 14900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/MP.png?updatedAt=1748682263320", "title": "Buy product worth ₹999 and Get ₹110 Off on selected Mamypoko products", "sub_title": "Use code HAPPY110 & Get ₹110 Off on orders above ₹999 ", "tnc": ["Valid only on Mamypoko products", "The cart can have other items too but the discount will be valid only on Mamypoko products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "HAPPY110", "super_offer": false, "minimum_order_value": 99900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/Pampers.JPG?updatedAt=1748682462220", "title": "Get Flat ₹99 OFF on purchase of select range of baby care products worth ₹999 & above", "sub_title": "Use code PAMPERS99 & get Flat ₹99 OFF on orders above ₹999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "PAMPERS99", "super_offer": false, "minimum_order_value": 99900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/bumtum.png?updatedAt=1748681661079", "title": "Buy product worth ₹699 and Get ₹69 Off on Bumtum Items", "sub_title": "Use code BUMTUM69 & Get ₹69 Off on orders above ₹699", "tnc": ["Valid only on Bumtum Items", "The cart can have other items too but the discount will be valid only on Bumtum Items", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "BUMTUM69", "super_offer": false, "minimum_order_value": 69900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/himalayalogo.png?updatedAt=1748682315961", "title": "Buy product worth ₹500 and Get ₹50 Off on selected Himalaya Products", "sub_title": "Use code HIMALAYA50 & Get ₹50 Off on orders above ₹500", "tnc": ["Valid only on Himalaya Products", "The cart can have other items too but the discount will be valid only on Himalaya Products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "HIMALAYA50", "super_offer": false, "minimum_order_value": 50000, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/<PERSON>_baby.jpg?updatedAt=1748682409764", "title": "Buy product worth ₹499 and Get ₹49 Off on Johnson & Johnson Products", "sub_title": "Use code JJ49 & get Get ₹49 Off on orders above ₹499", "tnc": ["Valid only on Johnson & Johnson Products", "The cart can have other items too but the discount will be valid only on Johnson & Johnson Products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "JJ49", "super_offer": false, "minimum_order_value": 49900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}]}, "metadata": {"unstructured_enabled": false, "user_verification_required": false, "guaranteed_delivery": false}, "lastOrderTimestamp": 0, "inStockItems": 0, "deliverySlots": [], "invoiceDetails": {"invoice_entity_name": ""}, "hasPharmacy": false, "pharmacyEnabled": false, "sellerName": "", "sellerFssaiAuthorisedAddress": "Gf, Unit No CPM 35-B1-BG35,<PERSON><PERSON> ,Sector 105, Mohali Punjab - 140307", "sellerFssaiNumber": "20250326107137030", "partner": true, "slottedDeliveryAvailable": false, "unstructuredEnabled": false, "lat_long": "30.6566956,76.6821416", "phone_numbers": "**********", "is_verified": true, "is_partner": true, "image_id": "", "product_categories": [], "store_chain": {"id": 229, "name": "Instamart"}, "store_document": {"fssai_license_no": "", "drug_license_no": "", "gstin_number": "03AAFCJ5741H1ZQ", "liquor_license_no": "", "fssai_reference_no": "20250326107137030", "fssai_registered_business_entity_name": "Jaldi Technologies Pvt Ltd", "fssai_authorised_premises_address": "Gf, Unit No CPM 35-B1-BG35,<PERSON><PERSON> ,Sector 105, Mohali Punjab - 140307"}, "popular_category": {"id": 0, "name": ""}}, "totalItems": 46, "widgets": [{"collectionId": 1, "layoutId": 2373, "id": 72491, "active": 1, "widgetInfo": {"widgetType": "NxM_BANNER", "title": "L2 page", "fallbackTitle": "", "affordance": true, "autoScrollTime": 3, "description": "L2 page", "subType": "CATEGORY_TOP_WIDGET", "widgetStyles": {}, "cities": [], "analytics": {"objectValue": "Fresh Fruits", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "72491"}}, "row": 1, "column": 9, "aspectRatio": {"width": null, "height": null}, "carouselWidgetType": "FRESH_FRUITS:FRESH_FRUITS", "minimumBanner": 1, "fullGrid": true, "orientation": "VERTICAL", "subTitle": "", "adsSlots": "", "fullMonetized": true, "clickable": false, "fitToWidth": false, "dataProvider": "CAROUSEL", "isMasthead": false}, "data": [{"bannerId": 5621980, "creativeId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/2/5f515d54-6fd4-47a6-ad66-2e777666ffed_41L22.png", "entityType": "deeplink", "entityId": "/instamart/collection-listing?collectionId=208038", "entityDeepLink": "/instamart/collection-listing?collectionId=208038", "carouselTitle": "Fruits & Vegetables Category", "page": "STORES_MENU", "cappingEnabled": false, "widgetData": {"widgetType": "FRESH_FRUITS:FRESH_FRUITS", "mediaType": "IMAGE", "monetised": true}, "creativeType": "IMAGE", "analyticsData": {"objectName": null, "adTrackingContext": "cid=e927037d-4f3c-4fa1-8bea-a1de2c773013~~adtrid=f8c093e0-e3ed-4194-8cf0-669fd10e4545~~adgrpid=e927037d-4f3c-4fa1-8bea-a1de2c773013#ag1~~cndid=5621980~~bp=TUaxNjNaNBXiZ3FmXAfCTS+KvKgEWv5ujCRsrRtOuwD5Oh0MFglclwFiZcnAvO915LkJxLaf6pZ+4UX1PHquEZDlRka5rA==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753780664645~~plid=3e984908-8dd0-4a2d-ae3c-af6f3bc870a7~~plpr=IM_L2_PAGE~~kw=~~cityid=11"}, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"adTrackingId\":\"cid=e927037d-4f3c-4fa1-8bea-a1de2c773013~~adtrid=f8c093e0-e3ed-4194-8cf0-669fd10e4545~~adgrpid=e927037d-4f3c-4fa1-8bea-a1de2c773013#ag1~~cndid=5621980~~bp=TUaxNjNaNBXiZ3FmXAfCTS+KvKgEWv5ujCRsrRtOuwD5Oh0MFglclwFiZcnAvO915LkJxLaf6pZ+4UX1PHquEZDlRka5rA==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753780664645~~plid=3e984908-8dd0-4a2d-ae3c-af6f3bc870a7~~plpr=IM_L2_PAGE~~kw=~~cityid=11\"}", "impressionObjectName": "impression-banner-ad", "clickObjectName": "click-banner-ad", "extraFields": {"l2NodeVal": "Fresh Fruits", "bannerId": "5621980"}}, "score": 0.006234400000000004, "adItem": true}, {"bannerId": 5625643, "creativeId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/3/7864b018-1e62-4108-90b0-bdc486a928b1_L211.png", "entityType": "deeplink", "entityId": "/instamart/collection-listing?collectionId=191177", "entityDeepLink": "/instamart/collection-listing?collectionId=191177", "carouselTitle": "Fruits & Vegetables Category", "page": "STORES_MENU", "cappingEnabled": false, "widgetData": {"widgetType": "FRESH_FRUITS:FRESH_FRUITS", "mediaType": "IMAGE", "monetised": true}, "creativeType": "IMAGE", "analyticsData": {"objectName": null, "adTrackingContext": "cid=4970e078-af16-49b9-aec3-7b97e975cb3a~~adtrid=805f29ad-e113-426e-a8a7-848d23aee43a~~adgrpid=4970e078-af16-49b9-aec3-7b97e975cb3a#ag1~~cndid=5625643~~bp=qyBfvES4okwG1tYvGkEFRbj+5oMoxHY5J/6QsrVnfFzYziTAIKVPOmZOQ5mVmsqe6jZanzSzJO/MIxM7PzHksmrAkeNTYg==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753780664645~~plid=3e984908-8dd0-4a2d-ae3c-af6f3bc870a7~~plpr=IM_L2_PAGE~~kw=~~cityid=11"}, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"adTrackingId\":\"cid=4970e078-af16-49b9-aec3-7b97e975cb3a~~adtrid=805f29ad-e113-426e-a8a7-848d23aee43a~~adgrpid=4970e078-af16-49b9-aec3-7b97e975cb3a#ag1~~cndid=5625643~~bp=qyBfvES4okwG1tYvGkEFRbj+5oMoxHY5J/6QsrVnfFzYziTAIKVPOmZOQ5mVmsqe6jZanzSzJO/MIxM7PzHksmrAkeNTYg==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753780664645~~plid=3e984908-8dd0-4a2d-ae3c-af6f3bc870a7~~plpr=IM_L2_PAGE~~kw=~~cityid=11\"}", "impressionObjectName": "impression-banner-ad", "clickObjectName": "click-banner-ad", "extraFields": {"l2NodeVal": "Fresh Fruits", "bannerId": "5625643"}}, "score": 0.004666400000000004, "adItem": true}], "type": "NxM_BANNER", "priority": 2, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3339, "id": 113420, "active": 1, "widgetInfo": {"widgetType": "FILTER_WIDGET", "header": null, "title": "Filters", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "Filters", "platformVersionInfoList": null, "widgetStyles": null, "widgetIdentifier": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "113420"}}}, "data": [{"facetList": [{"label": "Pick By Quality", "id": "demandshaping", "openFilter": false, "searchable": true, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}, "selectionType": "FACET_TYPE_MULTI_SELECT", "facetInfo": [{"label": "Exotics", "key": "Exotics", "openFilter": true, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}}]}, {"label": "Type", "id": "type", "openFilter": true, "searchable": true, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}, "selectionType": "FACET_TYPE_MULTI_SELECT", "facetInfo": [{"label": "Apple", "key": "Apple", "openFilter": true, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}}, {"label": "Pear", "key": "Pear", "openFilter": true, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}}, {"label": "Banana", "key": "Banana", "openFilter": true, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}}, {"label": "Pineapple", "key": "Pineapple", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON><PERSON>", "key": "<PERSON><PERSON>", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Pomegranate", "key": "Pomegranate", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Sweet Lime", "key": "Sweet Lime", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Apricot", "key": "Apricot", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Avocado", "key": "Avocado", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Blueberry", "key": "Blueberry", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Chikoo", "key": "Chikoo", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Gooseberry", "key": "Gooseberry", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Grape Fruit", "key": "Grape Fruit", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Grapes", "key": "Grapes", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Guava", "key": "Guava", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON><PERSON>", "key": "<PERSON><PERSON>", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Muskmelon", "key": "Muskmelon", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON><PERSON>", "key": "<PERSON><PERSON>", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Ra<PERSON><PERSON>", "key": "Ra<PERSON><PERSON>", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Tender Coconut", "key": "Tender Coconut", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Watermelon", "key": "Watermelon", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}]}], "sortAttributes": [{"key": "default", "title": "Relevance", "selected": false, "defaultSelection": true}, {"key": "priceLowToHigh", "title": "Price (Low To High)", "selected": true, "defaultSelection": false}, {"key": "priceHighToLow", "title": "Price (High To Low)", "selected": false, "defaultSelection": false}, {"key": "discountPercentHighToLow", "title": "Discount (High To Low)", "selected": false, "defaultSelection": false}], "quickFilterTitle": "Quick filters"}], "type": "FILTER_WIDGET", "priority": 1, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3339, "id": 101657, "active": 1, "widgetInfo": {"widgetType": "TEXT_WIDGET", "header": null, "title": "<b>46 items </b>in Fresh Fruits", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "", "platformVersionInfoList": null, "widgetStyles": null, "cities": [], "widgetIdentifier": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "101657"}}}, "data": null, "type": "TEXT_WIDGET", "priority": 2, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3339, "id": 101658, "active": 1, "widgetInfo": {"widgetType": "PRODUCT_LIST", "header": null, "title": "", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "", "platformVersionInfoList": null, "widgetStyles": null, "cities": [], "widgetIdentifier": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "101658"}}, "itemCount": 6}, "data": [{"display_name": "Sweet Lime", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "UDH2S7L4XR", "display_name": "Sweet Lime", "listing_variant": true, "spin": "05JCU27YK6", "super_saver": false, "images": ["d97d16a1814f90ef92f6cb814bc8a330", "ca9124aa344fdc269ae13d8aa56158d5", "31d47e98292ac1235367f9d449276612"], "images_v2": [], "price": {"mrp": 44, "store_price": 44, "offer_price": 35, "offer_applied": {"offer_id": "9ebd523a-e1c6-495f-928f-79610c920ecb", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "7/100 g", "loyalty_pricing": {"super_offer_price": 35, "non_super_offer_price": 35}, "discount_value": 9, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Mildly sweet, refreshing juice; rehydrates naturally", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 1000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "6 - 9 pcs", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Sweet Lime", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Sweet Lime", "sub_category_l4": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "LPI0MKT3ZX", "display_name": "Sweet Lime (Mosambi)", "listing_variant": false, "spin": "PRYH4CLS6S", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2024/11/21/d77c4270-71e4-41f3-9cdf-b462372d7015_297567.png"], "images_v2": [], "price": {"mrp": 88, "store_price": 88, "offer_price": 70, "offer_applied": {"offer_id": "9ebd523a-e1c6-495f-928f-79610c920ecb", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "7/100 g", "loyalty_pricing": {"super_offer_price": 70, "non_super_offer_price": 70}, "discount_value": 18, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "500 g x 2", "unit_of_measure": "kg", "meta": {"short_description": "Mildly sweet, refreshing juice; rehydrates naturally", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 2000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 22, "width_in_cm": 11, "height_in_cm": 20, "volume_in_cc": 4840}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Sweet Lime (Mosambi)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Sweet Lime", "sub_category_l4": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"UDH2S7L4XR\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "H7XVS3WSFO", "product_name_without_brand": "Sweet Lime"}, {"display_name": "<PERSON>", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "ADWIUYJMPA", "display_name": "<PERSON>", "listing_variant": true, "spin": "PR8FT7YZLV", "super_saver": false, "images": ["959f3368e153e4cf3475e41318b33f8b", "3e4e1c9d5477e13f7e0d30fb2ee9104e"], "images_v2": [], "price": {"mrp": 56, "store_price": 56, "offer_price": 45, "offer_applied": {"offer_id": "a2adb941-fa44-46a2-b7e8-82dd449ed3e7", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 45, "non_super_offer_price": 45}, "discount_value": 11, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "4", "sku_quantity_with_combo": "4 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Tiny, sweet bananas; kid-friendly snack", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 4, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 4, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Banana", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "300 - 400 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON>", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Banana", "sub_category_l4": "<PERSON><PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"ADWIUYJMPA\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "IU4NHXNSDS", "product_name_without_brand": "<PERSON>"}, {"display_name": "Premium Sweet Lime (Mosambi)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "SWNQPXUWTO", "display_name": "Premium Sweet Lime (Mosambi)", "listing_variant": true, "spin": "1T8DU85GPH", "super_saver": false, "images": ["a2394b5d4842415d27230ceee6dc5d04", "2f71d4386d97fcfdb4c50c2c7449f5cb", "e73bf59dda3b79b2f6080ecf50eb84a8"], "images_v2": [], "price": {"mrp": 58, "store_price": 58, "offer_price": 46, "offer_applied": {"offer_id": "292db408-230d-4bd7-a618-87ce55848f98", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 46, "non_super_offer_price": 46}, "discount_value": 12, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Sweet, pulpy and juicy", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "Only 6 unit(s) of this item can be added per order."}, "weight_in_grams": 1000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Premium Sweet Lime (Mosambi)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Sweet Lime", "sub_category_l4": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"SWNQPXUWTO\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "5ZHRF1MPZ1", "product_name_without_brand": "Premium Sweet Lime (Mosambi)"}, {"display_name": "<PERSON><PERSON>", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From Shimla, Himachal", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From Shimla, Himachal", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "GG59RBIKU9", "display_name": "<PERSON><PERSON>", "listing_variant": true, "spin": "14TVLS11NU", "super_saver": false, "images": ["e0d73b966ffdcb94d92fb1ee27d996a1", "546d2c4441d943e810205682c3237deb", "fba29d1b71055c96e5e2a3ef5473542d"], "images_v2": [], "price": {"mrp": 74, "store_price": 74, "offer_price": 59, "offer_applied": {"offer_id": "b0788d52-65aa-4f64-b228-46f521ad3a49", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 59, "non_super_offer_price": 59}, "discount_value": 15, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "3", "sku_quantity_with_combo": "3 Medium", "unit_of_measure": "Medium", "meta": {"short_description": "Firm bite, mildly sweet & super hydrating", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 5, "cart_allowed_quantity": {"total": 5, "message": "Only 5 unit(s) of this item can be added per order."}, "weight_in_grams": 3, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fresh Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "240 - 330g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON>", "sourcing_time": "", "sourced_from": "From Shimla, Himachal", "super_category": "Fruits and Vegetables", "sub_category_l3": "Pear", "sub_category_l4": "<PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"GG59RBIKU9\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "ENVHP3H2HL", "product_name_without_brand": "<PERSON><PERSON>"}, {"display_name": "<PERSON><PERSON><PERSON> (Kela)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "RPWU8W3L3U", "display_name": "<PERSON><PERSON><PERSON> (Kela)", "listing_variant": true, "spin": "AODY6XZFVF", "super_saver": false, "images": ["e14f5d679f7ac81a64b7ae831afcfe9a", "24f0dc04efae5b291dc90379b443d4c7"], "images_v2": [], "price": {"mrp": 75, "store_price": 75, "offer_price": 60, "offer_applied": {"offer_id": "e321a117-bbb6-4562-ae8d-66ec0663d679", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "15/piece", "loyalty_pricing": {"super_offer_price": 60, "non_super_offer_price": 60}, "discount_value": 15, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "4", "sku_quantity_with_combo": "4 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Soft, sweet, perfect for snacking or smoothies.", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "Only 6 unit(s) of this item can be added per order."}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Banana", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "500 - 700 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON><PERSON> (Kela)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Banana", "sub_category_l4": "<PERSON><PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "JZJZYMV5YK", "display_name": "<PERSON><PERSON><PERSON>", "listing_variant": false, "spin": "SM49O0BFUJ", "super_saver": false, "images": ["7d16044af1d3c5612089e0e8e5364f29", "24f0dc04efae5b291dc90379b443d4c7"], "images_v2": [], "price": {"mrp": 150, "store_price": 150, "offer_price": 119, "offer_applied": {"offer_id": "e321a117-bbb6-4562-ae8d-66ec0663d679", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "14.9/piece", "loyalty_pricing": {"super_offer_price": 119, "non_super_offer_price": 119}, "discount_value": 31, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "8", "sku_quantity_with_combo": "4 pieces x 2", "unit_of_measure": "pieces", "meta": {"short_description": "Soft, sweet, perfect for snacking or smoothies.", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "Only 1 unit(s) of this item can be added per order."}, "weight_in_grams": 1000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "1 - 1.4 kg", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON><PERSON>", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Banana", "sub_category_l4": "<PERSON><PERSON><PERSON>", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"RPWU8W3L3U\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "HFIDROW4TG", "product_name_without_brand": "<PERSON><PERSON><PERSON> (Kela)"}, {"display_name": "Apple Golden Delicious", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From Shimla", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From Shimla", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "Q0Z4X839WO", "display_name": "Apple Golden Delicious", "listing_variant": true, "spin": "U8UMCH7A4Z", "super_saver": false, "images": ["of5xzabeastayfrzhi7n"], "images_v2": [], "price": {"mrp": 76, "store_price": 76, "offer_price": 61, "offer_applied": {"offer_id": "9b4929b8-7c56-4c08-8860-fb680fe81f4b", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 61, "non_super_offer_price": 61}, "discount_value": 15, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Soft, sweet, and juicy with a golden skin", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 5, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 320, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fresh Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "250 - 350 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Apple Golden Delicious", "sourcing_time": "", "sourced_from": "From Shimla", "super_category": "Fruits and Vegetables", "sub_category_l3": "Apple", "sub_category_l4": "Apple Golden Delicious", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"Q0Z4X839WO\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "GA8CI0464U", "product_name_without_brand": "Apple Golden Delicious"}], "type": "PRODUCT_LIST", "priority": 3, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3339, "id": 101659, "active": 1, "widgetInfo": {"widgetType": "NxM_BANNER", "title": "", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "", "subType": "CATEGORY_NXM_BANNER", "widgetStyles": {"videoHeight": "0", "padding": {"top": "", "right": "", "bottom": "", "left": ""}}, "cities": [], "analytics": {"objectValue": "Fresh Fruits", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "101659"}}, "row": 1, "column": 1, "aspectRatio": {"width": "284", "height": "100"}, "carouselWidgetType": "VARIETY_FRESH_FRUITS:FRESH_FRUITS", "minimumBanner": 1, "fullGrid": false, "orientation": "VERTICAL", "subTitle": "", "adsSlots": "", "fullMonetized": false, "clickable": false, "fitToWidth": false, "mastheadHeight": "", "dataProvider": "CAROUSEL", "isMasthead": false}, "data": [{"bannerId": 1198579, "creativeId": "rng/md/carousel/production/900930b4aae2085818cde6b990ea2a00", "entityType": "deeplink", "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=3402&customerPage=STORES_MxN_45", "entityDeepLink": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=3402&customerPage=STORES_MxN_45", "carouselTitle": "<PERSON><PERSON>", "page": "STORES_MENU", "cappingEnabled": false, "widgetData": {"widgetType": "VARIETY_FRESH_FRUITS:FRESH_FRUITS", "monetised": true}, "metadata": {"offerText": null}, "creativeType": "IMAGE", "analytics": {"objectValue": "Fresh Fruits", "context": "{}", "impressionObjectName": "impression-banner", "clickObjectName": "click-banner", "extraFields": {"l2NodeVal": "Fresh Fruits", "bannerId": "1198579"}}, "score": 0, "adItem": false}], "type": "NxM_BANNER", "priority": 4, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3339, "id": 101908, "active": 1, "widgetInfo": {"widgetType": "PRODUCT_LIST", "header": null, "title": "", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "", "platformVersionInfoList": null, "widgetStyles": null, "cities": [], "widgetIdentifier": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "101908"}}, "itemCount": 20}, "data": [{"display_name": "Gooseberry (Amla)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "9J246OGQD1", "display_name": "Gooseberry (Amla)", "listing_variant": true, "spin": "MOACWID769", "super_saver": false, "images": ["95f15521bbaad2d632a40f8f5d8120cb", "48bbd4c7b4e12d61d512e31b8958e2e0", "d4c0d72d9af95d49164d5466d85d8cf8"], "images_v2": [], "price": {"mrp": 83, "store_price": 83, "offer_price": 66, "offer_applied": {"offer_id": "fc639e17-c915-483a-97a5-f5267725e608", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 66, "non_super_offer_price": 66}, "discount_value": 17, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Tart and rich in vitamin C, great for pickles or juices.", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Gooseberry", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Gooseberry (Amla)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Gooseberry", "sub_category_l4": "<PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"9J246OGQD1\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "CLNT4XC1NX", "product_name_without_brand": "Gooseberry (Amla)"}, {"display_name": "Indian Pear Red", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From Shimla, Himachal", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From Shimla, Himachal", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "FVXBIUSII4", "display_name": "Indian Pear Red", "listing_variant": true, "spin": "L12YY4YO79", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2025/7/11/b9480d42-47ba-4f38-88fc-25c2a5baa13d_20602.jpg", "NI_CATALOG/IMAGES/CIW/2025/7/11/86daf4f9-42b3-4c89-a14c-e9c0b6b1d381_20602_AL1.jpg", "NI_CATALOG/IMAGES/CIW/2025/7/11/095a812e-b7b1-46fd-b1c4-dc93aa50077c_20602_AL2.jpg"], "images_v2": [], "price": {"mrp": 85, "store_price": 85, "offer_price": 68, "offer_applied": {"offer_id": "1e4005b4-7b0e-4048-b106-8481224caab7", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 68, "non_super_offer_price": 68}, "discount_value": 17, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Rosy skin, soft bite & sweet, juicy flavour", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 5, "cart_allowed_quantity": {"total": 3, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 20, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Indian Pear Red", "sourcing_time": "", "sourced_from": "From Shimla, Himachal", "super_category": "Fruits and Vegetables", "sub_category_l3": "Pear", "sub_category_l4": "Red pear", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"FVXBIUSII4\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "FDUBBSQG68", "product_name_without_brand": "Indian Pear Red"}, {"display_name": "Indian Pear <PERSON>", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From Shimla, Himachal", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From Shimla, Himachal", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "H41F9JT6OX", "display_name": "Indian Pear <PERSON>", "listing_variant": true, "spin": "VYW05YRNWT", "super_saver": false, "images": ["dabf70039bf01d87bcfe7c8135c560b7", "c7be8ef6e3b39fdb0392c6f18d9daf04", "a09f79a1a74044dc6315909e0696e034"], "images_v2": [], "price": {"mrp": 96, "store_price": 96, "offer_price": 77, "offer_applied": {"offer_id": "66e0e347-1212-474e-b612-cb468e1a986b", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 77, "non_super_offer_price": 77}, "discount_value": 19, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Naturally sweet with grainy, buttery flesh", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 5, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 360, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "300 - 400 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Indian Pear <PERSON>", "sourcing_time": "", "sourced_from": "From Shimla, Himachal", "super_category": "Fruits and Vegetables", "sub_category_l3": "Pear", "sub_category_l4": "<PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"H41F9JT6OX\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "B9PKP9GIF8", "product_name_without_brand": "Indian Pear <PERSON>"}, {"display_name": "Chikoo (Sapota)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "FWPA59CJS5", "display_name": "Chikoo (Sapota)", "listing_variant": true, "spin": "870FD4TH6C", "super_saver": false, "images": ["640a2b2e5d2b553d4c94336f6e802154", "f23d2b70cd0009cd8a23daff99775c9e"], "images_v2": [], "price": {"mrp": 99, "store_price": 99, "offer_price": 79, "offer_applied": {"offer_id": "e128d85b-6a17-4f7d-ba17-39c47366a4d1", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 79, "non_super_offer_price": 79}, "discount_value": 20, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Ripens in 1-2 days", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Chikoo", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Chikoo (Sapota)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Chikoo", "sub_category_l4": "<PERSON><PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"FWPA59CJS5\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "QM6TKWBHJ9", "product_name_without_brand": "Chikoo (Sapota)"}, {"display_name": "Grape Fruit", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From South Africa", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From South Africa", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "Q94RX6GZ81", "display_name": "Grape Fruit", "listing_variant": true, "spin": "46MIM445S4", "super_saver": false, "images": ["b50164a1bb72ee29e7848466040c231f", "624de5d17532324f7b0cd7d58b48f1c6", "86708ce0d580d9c51c245313074d18dc"], "images_v2": [], "price": {"mrp": 111, "store_price": 111, "offer_price": 89, "offer_applied": {"offer_id": "71ddc3a8-7463-4084-b2e0-07722b97924c", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 89, "non_super_offer_price": 89}, "discount_value": 22, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Piece", "unit_of_measure": "Piece", "meta": {"short_description": "Semi-sweet, bitter fruit", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 300, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "150 - 300 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Grape Fruit", "sourcing_time": "", "sourced_from": "From South Africa", "super_category": "Fruits and Vegetables", "sub_category_l3": "Grape Fruit", "sub_category_l4": "Exotics", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Exotics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"Q94RX6GZ81\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "6VC1YLV21X", "product_name_without_brand": "Grape Fruit"}, {"display_name": "Tender Coconut", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "FVKQ3ZAYOI", "display_name": "Tender Coconut", "listing_variant": true, "spin": "97VIJRXBA7", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2025/3/10/eafdfc16-c064-41bb-95c6-10606b6fca4f_357325_1.png", "NI_CATALOG/IMAGES/CIW/2025/3/10/02aba20a-0d19-4053-988a-f5ddc33fac7d_357325_2.png", "NI_CATALOG/IMAGES/CIW/2025/3/10/6b299fa5-8f3d-4a32-862f-fe2be4f5359d_357325_3.png"], "images_v2": [], "price": {"mrp": 125, "store_price": 125, "offer_price": 100, "offer_applied": {"offer_id": "5f901b44-1626-45cd-a90e-c6adf4a9b141", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 100, "non_super_offer_price": 100}, "discount_value": 25, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Piece", "unit_of_measure": "Piece", "meta": {"short_description": "Refreshing and hydrating, great for drinking.", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 4, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 2200, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "200 - 300 ml", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Tender Coconut", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Tender Coconut", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "VXQUCL4N9F", "display_name": "Tender coconut", "listing_variant": false, "spin": "ZZ6FZR67BB", "super_saver": true, "images": ["NI_CATALOG/IMAGES/CIW/2025/3/10/eafdfc16-c064-41bb-95c6-10606b6fca4f_357325_1.png", "NI_CATALOG/IMAGES/CIW/2025/3/10/02aba20a-0d19-4053-988a-f5ddc33fac7d_357325_2.png", "NI_CATALOG/IMAGES/CIW/2025/3/10/6b299fa5-8f3d-4a32-862f-fe2be4f5359d_357325_3.png"], "images_v2": [], "price": {"mrp": 375, "store_price": 375, "offer_price": 292, "offer_applied": {"offer_id": "5f901b44-1626-45cd-a90e-c6adf4a9b141", "listing_description": "22% OFF", "product_description": "22% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "97.3/piece", "loyalty_pricing": {"super_offer_price": 292, "non_super_offer_price": 292}, "discount_value": 83, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "3", "sku_quantity_with_combo": "1 Piece x 3", "unit_of_measure": "pieces", "meta": {"short_description": "Refreshing and hydrating, great for drinking.", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 6600, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Tender Coconut", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "200 - 300 ml", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Tender coconut", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Tender Coconut", "sub_category_l4": "Regular", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"FVKQ3ZAYOI\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "4RL2JFCWQG", "product_name_without_brand": "Tender Coconut"}, {"display_name": "Imported Plum", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From South Africa", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From South Africa", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "GYI0G2XMQP", "display_name": "Imported Plum", "listing_variant": true, "spin": "IN6ZKJ8V3J", "super_saver": false, "images": ["f3e722b48e4f698ca5235c56a1a0ee81", "9a50f4d7fb617eb0a1d7ea05bb4650d2", "17f025ccc707f2e21ea0e24a530a5d1e"], "images_v2": [], "price": {"mrp": 130, "store_price": 130, "offer_price": 104, "offer_applied": {"offer_id": "03279824-c9d8-4f9d-8207-86f4208ec814", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 104, "non_super_offer_price": 104}, "discount_value": 26, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Juicy, pulpy and tarty", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 4, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fresh Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Imported Plum", "sourcing_time": "", "sourced_from": "From South Africa", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON>", "sub_category_l4": "Exotics", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Exotics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"GYI0G2XMQP\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "4VIWA2UA00", "product_name_without_brand": "Imported Plum"}, {"display_name": "Papaya (Papita)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "D258FBYAAX", "display_name": "Papaya (Papita)", "listing_variant": true, "spin": "CGNMT6PLI2", "super_saver": false, "images": ["6df726d012d112bf79fbe258cf0550be", "f2809b3126086a83c90962bbf70efb0c"], "images_v2": [], "price": {"mrp": 134, "store_price": 134, "offer_price": 107, "offer_applied": {"offer_id": "cd1f4119-dfeb-4628-8798-db1a8243619c", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 107, "non_super_offer_price": 107}, "discount_value": 27, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Medium", "unit_of_measure": "Medium", "meta": {"short_description": "Ripens in 1-2 days", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "Only 6 unit(s) of this item can be added per order."}, "weight_in_grams": 1000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "800 - 1200 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Papaya (Papita)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON>", "sub_category_l4": "Lady <PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"D258FBYAAX\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "X6SSJHKVRS", "product_name_without_brand": "Papaya (Papita)"}, {"display_name": "Sun Melon", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "XIANLFB7CQ", "display_name": "Sun Melon", "listing_variant": true, "spin": "IU6IVMAZRI", "super_saver": false, "images": ["9095b93e495e31c2222eff91c26fc135", "d48e8cb237444b762ad45b5ea1062b73"], "images_v2": [], "price": {"mrp": 144, "store_price": 144, "offer_price": 115, "offer_applied": {"offer_id": "63be029e-df52-46bb-a415-5b8af0d99f10", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 115, "non_super_offer_price": 115}, "discount_value": 29, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Piece", "unit_of_measure": "Piece", "meta": {"short_description": "Golden, juicy & sweeter than muskmelon—refreshingly healthy", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 800, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "500 - 800 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Sun Melon", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Muskmelon", "sub_category_l4": "Sun Melon", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"XIANLFB7CQ\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "QTUSKNCKVK", "product_name_without_brand": "Sun Melon"}, {"display_name": "<PERSON><PERSON> Pear - South Africa", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From South Africa", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From South Africa", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "CUCY3CTRRA", "display_name": "<PERSON><PERSON> Pear - South Africa", "listing_variant": true, "spin": "CBW6AARR9K", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2025/5/9/d5a2e008-5b2a-48cd-af78-4105d0452b14_1102.png", "880ec1fdd81f4af804f6040e644a5e5c", "1d3417db2a91b410fa84acc681bd7806"], "images_v2": [], "price": {"mrp": 146, "store_price": 146, "offer_price": 117, "offer_applied": {"offer_id": "ff7150bc-00cb-4bf1-90d4-5b1b2b9df02c", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 117, "non_super_offer_price": 117}, "discount_value": 29, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Spongy pulp, salad star", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 160, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "250 - 400 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON> Pear - South Africa", "sourcing_time": "", "sourced_from": "From South Africa", "super_category": "Fruits and Vegetables", "sub_category_l3": "Pear", "sub_category_l4": "Green Pear", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"CUCY3CTRRA\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "ZSUPYR4BQP", "product_name_without_brand": "<PERSON><PERSON> Pear - South Africa"}, {"display_name": "Snacky Indian Apple (Seb)", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From Shimla", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From Shimla", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "LV4Y5R70RI", "display_name": "Snacky Indian Apple (Seb)", "listing_variant": true, "spin": "D8QGUYIT62", "super_saver": false, "images": ["664a61817cca53acc3beaeef3dac941e", "aa738defa19e2147c3fcb11f473ac752", "1877969e2c373e75b022bdcf1ea5e4ac"], "images_v2": [], "price": {"mrp": 148, "store_price": 148, "offer_price": 118, "offer_applied": {"offer_id": "b2c257e9-06d0-4923-b11b-52665a8b7602", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 118, "non_super_offer_price": 118}, "discount_value": 30, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Small-sized, crisp & perfect for snacking", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 5, "cart_allowed_quantity": {"total": 5, "message": "Only 5 unit(s) of this item can be added per order."}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fresh Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "4 - 6 pcs", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Snacky Indian Apple (Seb)", "sourcing_time": "", "sourced_from": "From Shimla", "super_category": "Fruits and Vegetables", "sub_category_l3": "Apple", "sub_category_l4": "Indian Apple", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"LV4Y5R70RI\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "XU3WG7463P", "product_name_without_brand": "Snacky Indian Apple (Seb)"}, {"display_name": "<PERSON><PERSON>", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From Chile", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From Chile", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "8FTHIM8Q8O", "display_name": "<PERSON><PERSON>", "listing_variant": true, "spin": "VODG6HASR0", "super_saver": false, "images": ["e22a43ef919527925bb20ab08a9e56f1", "961904c19b2d85708c234827402ade30", "93f040a3d5601537c7d1da690d33fa83"], "images_v2": [], "price": {"mrp": 149, "store_price": 149, "offer_price": 119, "offer_applied": {"offer_id": "04721d19-e186-4d7e-9e0b-e8841459bb6f", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 119, "non_super_offer_price": 119}, "discount_value": 30, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "3", "sku_quantity_with_combo": "3 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Soft, seedy center with sweet-sour freshness", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "Only 6 unit(s) of this item can be added per order."}, "weight_in_grams": 360, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "200 - 300 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON>", "sourcing_time": "", "sourced_from": "From Chile", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON>", "sub_category_l4": "Exotics", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Exotics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"8FTHIM8Q8O\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "4CIAR6GSG0", "product_name_without_brand": "<PERSON><PERSON>"}, {"display_name": "Indian Plum", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From Shimla", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From Shimla", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "P6MTIAR8GH", "display_name": "Indian Plum", "listing_variant": true, "spin": "GXYZ2QAIJT", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2024/6/17/9de73b6a-2a87-40b7-bdaf-67a1b7b454d4_freshfruits_GXYZ2QAIJT_MN.png"], "images_v2": [], "price": {"mrp": 149, "store_price": 149, "offer_price": 119, "offer_applied": {"offer_id": "de364ed4-b453-4f74-b527-90557c26e799", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 119, "non_super_offer_price": 119}, "discount_value": 30, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Soft & dark red skin with very juicy pulp", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 5, "cart_allowed_quantity": {"total": 5, "message": "Only 5 unit(s) of this item can be added per order."}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Indian Plum", "sourcing_time": "", "sourced_from": "From Shimla", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON>", "sub_category_l4": "Exotics", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Exotics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"P6MTIAR8GH\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "A5YVJ7O232", "product_name_without_brand": "Indian Plum"}, {"display_name": "Pineapple (Ananas)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "ZZWI7HBT1H", "display_name": "Pineapple (Ananas)", "listing_variant": true, "spin": "1S00DD9GQ3", "super_saver": false, "images": ["bd93a62c1f03fb5325d20a56fd9decf6", "07c67b604b23002dadbfa85f453472b7", "9435a7f9ad9237a693f218e1c6ba5e57"], "images_v2": [], "price": {"mrp": 151, "store_price": 151, "offer_price": 121, "offer_applied": {"offer_id": "b06aa8d0-2d48-4690-a5f0-074e9dcaf9bc", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 121, "non_super_offer_price": 121}, "discount_value": 30, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Medium", "unit_of_measure": "Medium", "meta": {"short_description": "Ripens in 2-3 days", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 1000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fresh Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "700 - 1200 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Pineapple (Ananas)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Pineapple", "sub_category_l4": "Fruit", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"ZZWI7HBT1H\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "8BB9HM9FUS", "product_name_without_brand": "Pineapple (Ananas)"}, {"display_name": "Imported Daily Apple (Seb)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "L3POU5ZBCM", "display_name": "Imported Daily Apple (Seb)", "listing_variant": true, "spin": "SPTUC142PW", "super_saver": false, "images": ["8631423fdda91d968a3889a37cc1fc8c", "490dbe43fe4df38bb39ce60e39985d41", "56acdbd10e5c0b1fa9f9ce8e36fa602a"], "images_v2": [], "price": {"mrp": 155, "store_price": 155, "offer_price": 124, "offer_applied": {"offer_id": "580582f4-ce94-4018-ba56-d4ca69c54e0b", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "62/piece", "loyalty_pricing": {"super_offer_price": 124, "non_super_offer_price": 124}, "discount_value": 31, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Everyday sweet, crisp, and juicy with a balanced flavor", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "Only 6 unit(s) of this item can be added per order."}, "weight_in_grams": 20, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "200 - 400 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Imported Daily Apple (Seb)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Apple", "sub_category_l4": "Imported Apple", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "7QWDDM47DY", "display_name": "Imported Daily Apple", "listing_variant": false, "spin": "OICDKIHLD6", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2024/12/21/a9c3a8fc-884b-4a11-b166-38c1f6ac7929_35103.png", "490dbe43fe4df38bb39ce60e39985d41", "56acdbd10e5c0b1fa9f9ce8e36fa602a"], "images_v2": [], "price": {"mrp": 310, "store_price": 310, "offer_price": 245, "offer_applied": {"offer_id": "580582f4-ce94-4018-ba56-d4ca69c54e0b", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "61.3/piece", "loyalty_pricing": {"super_offer_price": 245, "non_super_offer_price": 245}, "discount_value": 65, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "8", "sku_quantity_with_combo": "2 pieces x 2", "unit_of_measure": "pieces", "meta": {"short_description": "Sweet and crunchy, ideal for fresh snacking.", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "Only 1 unit(s) of this item can be added per order."}, "weight_in_grams": 40, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fresh Fruit", "dimensions": {"length_in_cm": 22, "width_in_cm": 11, "height_in_cm": 20, "volume_in_cc": 4840}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "400 - 800 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Imported Daily Apple", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Apple", "sub_category_l4": "Imported Apple", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"L3POU5ZBCM\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "S10HQYDTR6", "product_name_without_brand": "Imported Daily Apple (Seb)"}, {"display_name": "<PERSON><PERSON>la Apple (Seb)", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From Shimla", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From Shimla", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "1XPFN01SKD", "display_name": "<PERSON><PERSON>la Apple (Seb)", "listing_variant": true, "spin": "BYM8C5167H", "super_saver": false, "images": ["375d95d5a658b14866e1c586a22f3e6b", "1547cb6c2f07aadeb45aed388f5dfd92"], "images_v2": [], "price": {"mrp": 161, "store_price": 161, "offer_price": 129, "offer_applied": {"offer_id": "79401e85-97af-480a-a910-633b862d3541", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 129, "non_super_offer_price": 129}, "discount_value": 32, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Crisp & light with a mild sweet-tart bite", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 20, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruits", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "250 - 370 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON>la Apple (Seb)", "sourcing_time": "", "sourced_from": "From Shimla", "super_category": "Fruits and Vegetables", "sub_category_l3": "Apple", "sub_category_l4": "Shimla Apple", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"1XPFN01SKD\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "XC3J58DTKQ", "product_name_without_brand": "<PERSON><PERSON>la Apple (Seb)"}, {"display_name": "Apple Pink Lady (Seb)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "G7HOZC1W0O", "display_name": "Apple Pink Lady (Seb)", "listing_variant": true, "spin": "TGRXRLDW19", "super_saver": false, "images": ["87a820afee00a7b5ceba2dbefbdc5faf", "121cf8c673dc4abd39eed5e269f0f839", "79022ef83ef6d55c96bae7a7b8698d3e"], "images_v2": [], "price": {"mrp": 164, "store_price": 164, "offer_price": 131, "offer_applied": {"offer_id": "6cb25c7f-fdc6-419d-9ec3-39da87f4dfa6", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 131, "non_super_offer_price": 131}, "discount_value": 33, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Crisp, firm, and sweet-tart with a juicy crunch", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "Only 6 unit(s) of this item can be added per order."}, "weight_in_grams": 20, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "300 - 400 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Apple Pink Lady (Seb)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Apple", "sub_category_l4": "Apple Pink Lady", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"G7HOZC1W0O\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "TT3ITP2XM0", "product_name_without_brand": "Apple Pink Lady (Seb)"}, {"display_name": "<PERSON> (Tarbooj)", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "From Anantapur", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "From Anantapur", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "NOZXL25G6K", "display_name": "<PERSON> (Tarbooj)", "listing_variant": true, "spin": "4SZ3YOFZ49", "super_saver": false, "images": ["6c508995bf8266ef8c46237ec2f61832", "2948b09569d1b65ad0c6f087ff767fa6"], "images_v2": [], "price": {"mrp": 169, "store_price": 169, "offer_price": 135, "offer_applied": {"offer_id": "12e3f343-d4bc-41b7-a6e7-4887949147f5", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 135, "non_super_offer_price": 135}, "discount_value": 34, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Medium", "unit_of_measure": "Medium", "meta": {"short_description": "Sweet and juicy, great for hydration.", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 4, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 2500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "2.5 kg - 3 kg", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON> (Tarbooj)", "sourcing_time": "", "sourced_from": "From Anantapur", "super_category": "Fruits and Vegetables", "sub_category_l3": "Watermelon", "sub_category_l4": "<PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"NOZXL25G6K\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "LBYG20V3WS", "product_name_without_brand": "<PERSON> (Tarbooj)"}, {"display_name": "Green Apple (Seb)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "6CL84ULGOD", "display_name": "Green Apple (Seb)", "listing_variant": true, "spin": "05OPI10Y6F", "super_saver": false, "images": ["21b51bc797e5be6e8ee6c4dbbbed860d", "89910bfd624a2f01e1dfe946e5e2133e", "1d03d46c90582c08054a9589e34ff558"], "images_v2": [], "price": {"mrp": 176, "store_price": 176, "offer_price": 141, "offer_applied": {"offer_id": "b5fe4aac-8fec-47be-8fce-5c63bb5ec2c2", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 141, "non_super_offer_price": 141}, "discount_value": 35, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Tart, crisp, and firm with a refreshing texture", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 4, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 160, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Fruit", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "300 - 400 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Green Apple (Seb)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Apple", "sub_category_l4": "Green Apple", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"6CL84ULGOD\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "IHT9YBM83Q", "product_name_without_brand": "Green Apple (Seb)"}, {"display_name": "Red Delicious Apple", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "RTXD3V8R23", "display_name": "Red Delicious Apple", "listing_variant": true, "spin": "ZGMQPZABRC", "super_saver": false, "images": ["96cb0cacbd19101e54538e3c86edb514", "0fa7376996c254e5ad998bc8ea09afe6", "36d427ef2fd885d158d9cd734b115d02"], "images_v2": [], "price": {"mrp": 180, "store_price": 180, "offer_price": 144, "offer_applied": {"offer_id": "cfee4e5f-ad51-487c-af83-af6608e7f103", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 144, "non_super_offer_price": 144}, "discount_value": 36, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Sweet, juicy, and crisp with a deep red color", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 4, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 360, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Fruits", "last_ordered_at": 0, "category_id": "2fbb2e2f-457a-4c60-a127-5fb805bef10a", "sku_secondary_quantity": "250 - 350 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Red Delicious Apple", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Apple", "sub_category_l4": "Red Delicious Apple", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Fruits", "context": "{\"itemId\":\"RTXD3V8R23\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "JUJTP22IXN", "product_name_without_brand": "Red Delicious Apple"}], "type": "PRODUCT_LIST", "priority": 5, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}], "offset": 26, "serviceLine": "INSTAMART", "filterName": "Fresh Fruits", "categoryName": "Fresh Fruits", "requestId": "dff48ddf-4df5-43b3-989e-57969ef16384", "storesDetails": [{"id": "1403687", "name": "Instamart", "storeConstraints": {"maxAllowedItemsInCart": 200, "maxAllowedCartWeightInGrams": 15000, "maxAllowedCartVolumeInCc": 42875}, "discountInfo": {"couponLessDiscounts": [{"description": "", "short_description": "", "discount_type": "FREEBIE", "logo_id": "e0cf25488aee88d25b9df510a56809f2", "title": "Sample - Buy any Rakhi and get a Kalyan voucher worth Rs.2100", "sub_title": "Sample - Buy any Rakhi and get a Kalyan voucher worth Rs.2100", "tnc": ["Offer valid till stocks last", "Offer valid for a limited period only", "Other TnCs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-08-10T18:29:00.000+0000", "offer_id": "b091ac92-fec2-46bb-9bde-c412d052d384", "minimum_order_value": 1, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=b091ac92-fec2-46bb-9bde-c412d052d384"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Rakhi items worth ₹1 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Convenience fees waived off", "sub_title": "Convenience fees waived off", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 30, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "aee47903-2307-4b74-beab-a16579105c14", "minimum_order_value": 1, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery on orders above ₹49", "sub_title": "Free Delivery on orders above ₹49", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2029-06-29T18:29:00.000+0000", "offer_id": "83c5bc5d-a07f-4ed9-bcca-0b0027901226", "minimum_order_value": 49, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "4a075af1-5370-4254-9293-dd09a1466672", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "579334a7-e682-41ee-a0dd-85b4192651b2", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "d328b922-37bb-4799-b45a-5419f19ead3c", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "117a395a-3e28-40fd-9cda-eabceab2398e", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "136a4e3a-03d3-4b50-b74e-de97600c7482", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "31bcfef4-6193-4de8-aa90-8310636b07f6", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "4a36cace-6f82-4b3a-989e-a781d0df00be", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "7a984027-c27f-4f77-be40-ec44fa05b077", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8167cae1-e162-4ead-851a-00c9fe3a7e92", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8271d0c9-a4ef-44c3-8a20-3b3b5edf467b", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "8e4d1422-bcb9-4168-b6ec-4858e2626685", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8f6ff210-6733-49da-b49a-41e8d354c820", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "93894e04-d811-4ef8-b965-5aa1c7a82fa5", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "bf8aeb64-5a8f-4728-93af-71bb5aac64f3", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy Mars Products worth Rs.299/- and get 5% off", "sub_title": "Buy Mars Products worth Rs.299/- and get 5% off", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-07-29T18:29:00.000+0000", "offer_id": "663381d2-e287-48d1-a6af-b78c3f498044", "minimum_order_value": 299, "discount_value": 5, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=663381d2-e287-48d1-a6af-b78c3f498044"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add MARS items worth ₹299 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy products worth Rs.799 from WishCare and get additional 5% off", "sub_title": "Buy products worth Rs.799 from WishCare and get additional 5% off", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "9797d066-eac8-4c22-8326-498b7678e7df", "minimum_order_value": 799, "discount_value": 5, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=9797d066-eac8-4c22-8326-498b7678e7df"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Wishcare items worth ₹799 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy Productsworth Rs.499 from Coloressence and get 3% discount", "sub_title": "Buy Productsworth Rs.499 from Coloressence and get 3% discount", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-08-31T18:29:00.000+0000", "offer_id": "828e45a0-29b6-4837-bea6-318eb590d9f1", "minimum_order_value": 499, "discount_value": 3, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=828e45a0-29b6-4837-bea6-318eb590d9f1"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Coloressence items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_CATEGORY%20/Quench%20logo.webp?updatedAt=1751957849182", "title": "Get Rs.100 off on order above Rs.399 on Quench products", "sub_title": "Get Rs.100 off on order above Rs.399 on Quench products", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 100, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "674efb29-b555-43e7-87e2-2ce4e4b011f4", "minimum_order_value": 399, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=674efb29-b555-43e7-87e2-2ce4e4b011f4"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Wella Professionals items worth ₹399 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 99 off on minimum Basket Value - Rs.999", "sub_title": "Rs. 99 off on minimum Basket Value - Rs.999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "54e73b98-dbd4-44c5-a08c-e26ecc72da37", "minimum_order_value": 999, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=54e73b98-dbd4-44c5-a08c-e26ecc72da37"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Purina items worth ₹999 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Flat ₹99 on orders Above ₹999", "sub_title": "Flat ₹99 on orders Above ₹999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "65e5e1dc-22f0-4601-91f8-bd3c9c9a5907", "minimum_order_value": 99, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=65e5e1dc-22f0-4601-91f8-bd3c9c9a5907"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Brainsmith  items worth ₹99 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Flat ₹99 off on orders Above ₹999.", "sub_title": "Flat ₹99 off on orders Above ₹999.", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "9f476014-1ecb-4550-9c1f-cdf0e6322772", "minimum_order_value": 999, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=9f476014-1ecb-4550-9c1f-cdf0e6322772"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Bamboo Nature items worth ₹999 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 55 off on minimum Basket Value - Rs.599", "sub_title": "Rs. 55 off on minimum Basket Value - Rs.599", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 55, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "7fd7af88-457e-4216-a1c1-49259b13a1c8", "minimum_order_value": 599, "discount_value": 55, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=7fd7af88-457e-4216-a1c1-49259b13a1c8"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Petcrux items worth ₹599 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 50 off on minimum Basket Value - Rs.499", "sub_title": "Rs. 50 off on minimum Basket Value - Rs.499", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 50, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "c92082ac-f117-4eed-b7c0-7cb803e5187c", "minimum_order_value": 499, "discount_value": 50, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=c92082ac-f117-4eed-b7c0-7cb803e5187c"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Himalaya items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_Category/images%20(1).png?updatedAt=1751466553287", "title": "Rs. 20 off on minimum Basket Value - Rs.499", "sub_title": "Rs. 20 off on minimum Basket Value - Rs.499", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 20, "valid_till": "2025-07-31T07:45:52.000+0000", "offer_id": "8b4c6182-c2e4-4deb-b4ce-85a747434bb6", "minimum_order_value": 499, "discount_value": 20, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=8b4c6182-c2e4-4deb-b4ce-85a747434bb6"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Torrent Pharma items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}], "superOffer": false, "discounts": [{"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_Category/Edgewell-Personal-Care-Stayfree-Logo-_3x_2cc54d21-3bdf-4a6a-bb1b-b5cc08753eba_540x_wfhgsh%20(1).png?updatedAt=1750431171496", "title": "Buy product worth ₹499 and Get ₹49 Off on selected Stayfree products", "sub_title": "Use code STAYFREE49 & get Get ₹49 Off on orders above ₹499", "tnc": ["Valid only on selected Stayfree products", "The cart can have other items too but the discount will be valid only on Stayfree products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "STAYFREE49", "super_offer": false, "minimum_order_value": 49900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "IM_assets/philips.png", "title": "Flat 10% off on selected range of Philips Avent Items", "sub_title": "Flat 10% off on selected range of Philips Avent Items", "tnc": ["Valid only on Philips Avent Items", "The cart can have other items too but the discount will be valid only on Philips Avent Items", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "AVENT10", "super_offer": false, "minimum_order_value": 100, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Get Flat 10% off on sunfeast Biscuits & cakes  worth Rs.149 or above", "sub_title": "Get Flat 10% off on sunfeast Biscuits & cakes  worth Rs.149 or above", "tnc": ["Valid only on Sunfeast worth ₹149 or more", "The cart can have other items too but the discount will be valid only on The Sunfeast products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "ITC10", "super_offer": false, "minimum_order_value": 14900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/MP.png?updatedAt=1748682263320", "title": "Buy product worth ₹999 and Get ₹110 Off on selected Mamypoko products", "sub_title": "Use code HAPPY110 & Get ₹110 Off on orders above ₹999 ", "tnc": ["Valid only on Mamypoko products", "The cart can have other items too but the discount will be valid only on Mamypoko products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "HAPPY110", "super_offer": false, "minimum_order_value": 99900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/Pampers.JPG?updatedAt=1748682462220", "title": "Get Flat ₹99 OFF on purchase of select range of baby care products worth ₹999 & above", "sub_title": "Use code PAMPERS99 & get Flat ₹99 OFF on orders above ₹999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "PAMPERS99", "super_offer": false, "minimum_order_value": 99900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/bumtum.png?updatedAt=1748681661079", "title": "Buy product worth ₹699 and Get ₹69 Off on Bumtum Items", "sub_title": "Use code BUMTUM69 & Get ₹69 Off on orders above ₹699", "tnc": ["Valid only on Bumtum Items", "The cart can have other items too but the discount will be valid only on Bumtum Items", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "BUMTUM69", "super_offer": false, "minimum_order_value": 69900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/himalayalogo.png?updatedAt=1748682315961", "title": "Buy product worth ₹500 and Get ₹50 Off on selected Himalaya Products", "sub_title": "Use code HIMALAYA50 & Get ₹50 Off on orders above ₹500", "tnc": ["Valid only on Himalaya Products", "The cart can have other items too but the discount will be valid only on Himalaya Products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "HIMALAYA50", "super_offer": false, "minimum_order_value": 50000, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/<PERSON>_baby.jpg?updatedAt=1748682409764", "title": "Buy product worth ₹499 and Get ₹49 Off on Johnson & Johnson Products", "sub_title": "Use code JJ49 & get Get ₹49 Off on orders above ₹499", "tnc": ["Valid only on Johnson & Johnson Products", "The cart can have other items too but the discount will be valid only on Johnson & Johnson Products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "JJ49", "super_offer": false, "minimum_order_value": 49900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}]}, "priority": "PRIORITY_PRIMARY"}]}, "statusCode": 0}