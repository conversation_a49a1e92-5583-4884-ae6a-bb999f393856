{"data": {"categories": [{"id": "6822eeeded32000001e25aa1", "displayName": "Fresh Vegetables", "productCount": 272, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/e9395c2f-a316-4899-8a05-c08afa34de5a_9ada60fa-2794-45d7-b780-50015de09230"}, {"id": "6822eeeded32000001e25aae", "displayName": "Fresh Fruits", "productCount": 264, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/7e47a6e7-f40d-457f-9b46-fa23e3e015d6_3954cc97-e485-4725-b595-3a6e7029211f"}, {"id": "6822eeeded32000001e25abb", "displayName": "Dairy, Bread and Eggs", "productCount": 667, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/a8042459-6e03-40b7-88e6-5d7acb437880_1f641338-2306-4841-97aa-cb65b497d132"}, {"id": "6822eeeded32000001e25aca", "displayName": "Cereals and Breakfast", "productCount": 1316, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/1f081643-e392-4585-a8e7-04ace4ace1ef_2e314d6c-ed11-49af-8927-e3eadde4dbad"}, {"id": "6822eeeded32000001e25add", "displayName": "Atta, Rice and Dal", "productCount": 1187, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/fe25fe8b-a26f-49dc-af50-c748fff09475_165b10b8-00b7-4837-a27e-6399a3c99c29"}, {"id": "6822eeeded32000001e25aec", "displayName": "Oils and Ghee", "productCount": 616, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/b5e07c24-ed2b-4101-a87d-aeed35b4e2e7_30d06ae8-5347-4961-8d68-02b189159321"}, {"id": "6822eeeded32000001e25af9", "displayName": "Masalas", "productCount": 1019, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/5fb9f911-93e8-47d2-afd3-a9c876bbffe2_f00a60a1-de42-4132-808e-dc1136c27bac"}, {"id": "6822eeeded32000001e25b06", "displayName": "Dry Fruits and Seeds Mix", "productCount": 599, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/49c9852b-967f-40ef-8ff1-6c28a4282c65_9dcb6679-51f7-45ae-a367-eb056a8713e2"}, {"id": "6822eeeded32000001e25b13", "displayName": "Biscuits and Cakes", "productCount": 916, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/93e396cf-71df-4ed3-9015-af13fa3c9081_ccdcba8a-aa86-4ff9-9229-ed8f6c53cac0"}, {"id": "6822eeeded32000001e25b24", "displayName": "Tea, Coffee and Milk drinks", "productCount": 954, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/c54610b9-868e-4fdd-83d5-4d723ee93261_d0ee1b6c-dd6a-45fc-8f99-929a56764014"}, {"id": "6822eeeded32000001e25b31", "displayName": "Sauces and Spreads", "productCount": 407, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/49f512f4-5613-45af-bbd4-2eaa13906240_a026b538-95c9-4060-8cd0-0f44e0c2742f"}, {"id": "6822eeeded32000001e25b3c", "displayName": "Meat and Seafood", "productCount": 470, "ageConsentRequired": false, "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/15/774ad667-efe6-4145-ad58-3362632508fd_b8600286-4ff0-45bf-bfc6-45f4098cec35"}], "selectedCategoryId": "6822eeeded32000001e25aa1", "selectedCategoryName": "Fresh Vegetables", "filters": [{"name": "Fresh Vegetables", "id": "6822eeeded32000001e25aa2", "imageId": "NI_CATALOG/IMAGES/CIW/2025/3/19/b2676f05-a400-41d7-9972-bc7a182c9a57_6d253ad3-aa89-460e-adce-3e20698c008f", "type": "Speciality taxonomy 1", "productCount": 53, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Fresh Vegetables", "l2NodePos": "1"}}}, {"name": "Leafy and Seasonings", "id": "6822eeeded32000001e25aa3", "imageId": "NI_CATALOG/IMAGES/CIW/2025/3/19/30b88794-29ec-4c4a-a983-1e9c7b75bde3_0496956a-6096-4027-842b-7505f9bd3196", "type": "Speciality taxonomy 1", "productCount": 28, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Leafy and Seasonings", "l2NodePos": "2"}}}, {"name": "Exotic Vegetables", "id": "6822eeeded32000001e25aa4", "imageId": "NI_CATALOG/IMAGES/CIW/2025/3/19/4ee251e0-82ba-470e-a5c1-29660f5ca2cd_10165714-d5c1-4e05-b29a-d1598937f73b", "type": "Speciality taxonomy 1", "productCount": 31, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Exotic Vegetables", "l2NodePos": "3"}}}, {"name": "Seasonal Fruits", "id": "6822eeeded32000001e25aa5", "imageId": "NI_CATALOG/IMAGES/CIW/2025/7/11/7db2113a-0de5-4dde-9fa9-08934e206cb6_b800242f-e606-4ac4-957a-ac6e22be50be", "type": "Speciality taxonomy 1", "productCount": 19, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Seasonal Fruits", "l2NodePos": "4"}}}, {"name": "Pooja & Festive", "id": "6822eeeded32000001e25aa7", "imageId": "NI_CATALOG/IMAGES/CIW/2025/3/19/15b9ed6a-7961-453d-9e19-61001e78666a_b9a24a86-77e1-49e2-892e-305a2a45b25a", "type": "Speciality taxonomy 1", "productCount": 26, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Pooja & Festive", "l2NodePos": "5"}}}, {"name": "Cuts & sprouts", "id": "6822eeeded32000001e25aa6", "imageId": "NI_CATALOG/IMAGES/CIW/2025/3/19/03b3be59-51bc-4127-9cea-8179017f68d5_4420c246-b05a-413a-8494-deeb470112c7", "type": "Speciality taxonomy 1", "productCount": 12, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Cuts & sprouts", "l2NodePos": "6"}}}, {"name": "Certified Organics", "id": "6822eeeded32000001e25aa8", "imageId": "NI_CATALOG/IMAGES/CIW/2024/8/16/f8aca2f6-4317-4eb5-9b96-8cd5fb4fd616_Certifiedorganic.png", "type": "Speciality taxonomy 1", "productCount": 22, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Certified Organics", "l2NodePos": "7"}}}, {"name": "Combos", "id": "6822eeeded32000001e25aa9", "imageId": "NI_CATALOG/IMAGES/CIW/2025/3/19/292cee7c-d800-4171-aa01-4de71a1d144e_e7513aae-2462-4834-8c24-4f4e6cd897ba", "type": "Speciality taxonomy 1", "productCount": 2, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Combos", "l2NodePos": "8"}}}, {"name": "Exotic Fruits", "id": "6822eeeded32000001e25aab", "imageId": "NI_CATALOG/IMAGES/CIW/2025/5/14/e9baeaed-9583-4ef2-8d1a-675282f1a35b_a9dc3683-c0e0-4b71-bb97-7e71da9e7744", "type": "Speciality taxonomy 1", "productCount": 17, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Exotic Fruits", "l2NodePos": "9"}}}, {"name": "Cut Fruits and Juices", "id": "6822eeeded32000001e25aac", "imageId": "NI_CATALOG/IMAGES/CIW/2025/3/19/1a650bf3-bd22-49c9-a77f-e23ba0b598b5_b22f5453-8374-40f6-840a-2ba00b46e881", "type": "Speciality taxonomy 1", "productCount": 3, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Cut Fruits and Juices", "l2NodePos": "10"}}}, {"name": "Fresh Fruits", "id": "6822eeeded32000001e25aad", "imageId": "NI_CATALOG/IMAGES/CIW/2025/3/19/f369d6d4-49c6-4f57-a801-dfe3d0f3073b_057f4b62-a4b2-4c82-80b2-fbc20d06387a", "type": "Speciality taxonomy 1", "productCount": 46, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Fresh Fruits", "l2NodePos": "11"}}}, {"name": "Frozen Vegetables", "id": "68243edc0c0f930001b2188d", "imageId": "NI_CATALOG/IMAGES/CIW/2025/5/14/be65bbd1-c143-4507-9284-c5d549d69b69_45309b2a-510a-4b62-9d06-ded69947e065", "type": "Speciality taxonomy 1", "productCount": 13, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-l2-node", "clickObjectName": "click-l2-node", "extraFields": {"l2NodeVal": "Frozen Vegetables", "l2NodePos": "12"}}}], "selectedFilterId": "6822eeeded32000001e25aa2", "hasMore": true, "pageNo": 0, "offset": 40, "widgets": [{"collectionId": 1, "layoutId": 2373, "id": 72491, "active": 1, "widgetInfo": {"widgetType": "NxM_BANNER", "title": "L2 page", "fallbackTitle": "", "affordance": true, "autoScrollTime": 3, "description": "L2 page", "subType": "CATEGORY_TOP_WIDGET", "widgetStyles": {}, "cities": [], "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "72491"}}, "row": 1, "column": 9, "aspectRatio": {"width": null, "height": null}, "carouselWidgetType": "FRESH_VEGETABLES:FRESH_VEGETABLES", "minimumBanner": 1, "fullGrid": true, "orientation": "VERTICAL", "subTitle": "", "adsSlots": "", "fullMonetized": true, "clickable": false, "fitToWidth": false, "dataProvider": "CAROUSEL", "isMasthead": false}, "data": [{"bannerId": 5886664, "creativeId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/28/78f19093-10fe-4129-b5a3-0281b296b9f3_L21.png", "entityType": "deeplink", "entityId": "/instamart/collection-listing?collectionId=191177", "entityDeepLink": "/instamart/collection-listing?collectionId=191177", "carouselTitle": "Fruits & Vegetables Category", "page": "STORES_MENU", "cappingEnabled": false, "widgetData": {"widgetType": "FRESH_VEGETABLES:FRESH_VEGETABLES", "mediaType": "IMAGE", "monetised": true}, "creativeType": "IMAGE", "analyticsData": {"objectName": null, "adTrackingContext": "cid=cbbf602f-9f91-45e3-ba07-de5af65b21a9~~adtrid=b51d7ea6-c8f3-4296-baed-070b7561c3ba~~adgrpid=cbbf602f-9f91-45e3-ba07-de5af65b21a9#ag1~~cndid=5886664~~bp=3UBT84/sv5WfM3tt3Cgw/0ClrYnaRDOIgUiOkw0vKNIwLXDiSVsJKZhQVgI+KT9HFuhWnzTt2T9oD1nxrwUuwwRk+MIpxw==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753779037541~~plid=71377933-115c-413b-9748-b45609d3a44f~~plpr=IM_L2_PAGE~~kw=~~cityid=11"}, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"adTrackingId\":\"cid=cbbf602f-9f91-45e3-ba07-de5af65b21a9~~adtrid=b51d7ea6-c8f3-4296-baed-070b7561c3ba~~adgrpid=cbbf602f-9f91-45e3-ba07-de5af65b21a9#ag1~~cndid=5886664~~bp=3UBT84/sv5WfM3tt3Cgw/0ClrYnaRDOIgUiOkw0vKNIwLXDiSVsJKZhQVgI+KT9HFuhWnzTt2T9oD1nxrwUuwwRk+MIpxw==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753779037541~~plid=71377933-115c-413b-9748-b45609d3a44f~~plpr=IM_L2_PAGE~~kw=~~cityid=11\"}", "impressionObjectName": "impression-banner-ad", "clickObjectName": "click-banner-ad", "extraFields": {"l2NodeVal": "Fresh Vegetables", "bannerId": "5886664"}}, "score": 0.004666400000000004, "adItem": true}, {"bannerId": 5887206, "creativeId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/28/5d52f1b8-784e-4134-9930-a973ff1478dd_823L21.png", "entityType": "deeplink", "entityId": "/instamart/collection-listing?collectionId=89532", "entityDeepLink": "/instamart/collection-listing?collectionId=89532", "carouselTitle": "Fruits & Vegetables Category", "page": "STORES_MENU", "cappingEnabled": false, "widgetData": {"widgetType": "FRESH_VEGETABLES:FRESH_VEGETABLES", "mediaType": "IMAGE", "monetised": true}, "creativeType": "IMAGE", "analyticsData": {"objectName": null, "adTrackingContext": "cid=a0343c77-e47f-4191-8e2c-8baa2a9e0b76~~adtrid=420c84c3-a388-4972-944b-3a8ec2cd2f1f~~adgrpid=a0343c77-e47f-4191-8e2c-8baa2a9e0b76#ag1~~cndid=5887206~~bp=QlHghVYT14kNVInoN0Rz4VdzFFsO5H1VkfjQ37OUjvORO9GumWmcnAlIV61BOrcc6/fcm2p5cN2PBegTIcHHVevBh2GeBA==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753779037541~~plid=71377933-115c-413b-9748-b45609d3a44f~~plpr=IM_L2_PAGE~~kw=~~cityid=11"}, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"adTrackingId\":\"cid=a0343c77-e47f-4191-8e2c-8baa2a9e0b76~~adtrid=420c84c3-a388-4972-944b-3a8ec2cd2f1f~~adgrpid=a0343c77-e47f-4191-8e2c-8baa2a9e0b76#ag1~~cndid=5887206~~bp=QlHghVYT14kNVInoN0Rz4VdzFFsO5H1VkfjQ37OUjvORO9GumWmcnAlIV61BOrcc6/fcm2p5cN2PBegTIcHHVevBh2GeBA==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753779037541~~plid=71377933-115c-413b-9748-b45609d3a44f~~plpr=IM_L2_PAGE~~kw=~~cityid=11\"}", "impressionObjectName": "impression-banner-ad", "clickObjectName": "click-banner-ad", "extraFields": {"l2NodeVal": "Fresh Vegetables", "bannerId": "5887206"}}, "score": 0.004411600000000003, "adItem": true}, {"bannerId": 5808969, "creativeId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2025/7/21/d76b43e8-9c1b-4120-9ad4-0dcd413d0177_467L2.png", "entityType": "deeplink", "entityId": "/instamart/collection-listing?collectionId=226186", "entityDeepLink": "/instamart/collection-listing?collectionId=226186", "carouselTitle": "Fruits & Vegetables Category", "page": "STORES_MENU", "cappingEnabled": false, "widgetData": {"widgetType": "FRESH_VEGETABLES:FRESH_VEGETABLES", "mediaType": "IMAGE", "monetised": true}, "creativeType": "IMAGE", "analyticsData": {"objectName": null, "adTrackingContext": "cid=76aa9a9e-0dea-4d41-876f-eb5e1ff30e6e~~adtrid=6721d9ad-a3e4-4fcd-9297-2c5260b7adda~~adgrpid=76aa9a9e-0dea-4d41-876f-eb5e1ff30e6e#ag1~~cndid=5808969~~bp=8AuvNN/OWOp9n0fhnZi7ZgpDQjOH98d5ny6nL4k8bn4q1nQF6e/0v7JW/A0WIumSL+MxXpcJ1Y7o7Rf3QTjj2ZB2QyQ7jQ==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753779037541~~plid=71377933-115c-413b-9748-b45609d3a44f~~plpr=IM_L2_PAGE~~kw=~~cityid=11"}, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"adTrackingId\":\"cid=76aa9a9e-0dea-4d41-876f-eb5e1ff30e6e~~adtrid=6721d9ad-a3e4-4fcd-9297-2c5260b7adda~~adgrpid=76aa9a9e-0dea-4d41-876f-eb5e1ff30e6e#ag1~~cndid=5808969~~bp=8AuvNN/OWOp9n0fhnZi7ZgpDQjOH98d5ny6nL4k8bn4q1nQF6e/0v7JW/A0WIumSL+MxXpcJ1Y7o7Rf3QTjj2ZB2QyQ7jQ==~~mp=SWIGGY_IN~~bl=IM~~st=~~srvts=1753779037541~~plid=71377933-115c-413b-9748-b45609d3a44f~~plpr=IM_L2_PAGE~~kw=~~cityid=11\"}", "impressionObjectName": "impression-banner-ad", "clickObjectName": "click-banner-ad", "extraFields": {"l2NodeVal": "Fresh Vegetables", "bannerId": "5808969"}}, "score": 0.0040000000000000036, "adItem": true}], "type": "NxM_BANNER", "priority": 2, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3263, "id": 113412, "active": 1, "widgetInfo": {"widgetType": "FILTER_WIDGET", "header": null, "title": "Filters", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "Filters", "platformVersionInfoList": null, "widgetStyles": null, "widgetIdentifier": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "113412"}}}, "data": [{"facetList": [{"label": "Pick By Quality", "id": "demandshaping", "openFilter": false, "searchable": true, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}, "selectionType": "FACET_TYPE_MULTI_SELECT", "facetInfo": [{"label": "Exotics", "key": "Exotics", "openFilter": true, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}}]}, {"label": "Type", "id": "type", "openFilter": true, "searchable": true, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}, "selectionType": "FACET_TYPE_MULTI_SELECT", "facetInfo": [{"label": "Gourd", "key": "Gourd", "openFilter": true, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON><PERSON>", "openFilter": true, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}}, {"label": "Potato", "key": "Potato", "openFilter": true, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_QUICK"}}, {"label": "Capsicum", "key": "Capsicum", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Coconut", "key": "Coconut", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Raw Mango", "key": "Raw Mango", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Tomato", "key": "Tomato", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON><PERSON>", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON>s", "key": "<PERSON>s", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Beetroot", "key": "Beetroot", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON><PERSON>", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Carrot", "key": "Carrot", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Coccinia", "key": "Coccinia", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON><PERSON><PERSON>ber", "key": "<PERSON><PERSON><PERSON>ber", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Drumstick", "key": "Drumstick", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Dry Coconut", "key": "Dry Coconut", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Groundnut", "key": "Groundnut", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Lady's Finger", "key": "Lady's Finger", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Onion", "key": "Onion", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Peas", "key": "Peas", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON><PERSON><PERSON>", "key": "<PERSON><PERSON><PERSON>", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Radish", "key": "Radish", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "<PERSON>ana", "key": "<PERSON>ana", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Raw Papaya", "key": "Raw Papaya", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Sambar Onion", "key": "Sambar Onion", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Sweet Corn", "key": "Sweet Corn", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Sweet Potato", "key": "Sweet Potato", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}, {"label": "Yam", "key": "Yam", "openFilter": false, "selected": false, "icon": {}, "uiAttributes": {"facetCategory": "FACET_CATEGORY_INVALID"}}]}], "sortAttributes": [{"key": "default", "title": "Relevance", "selected": false, "defaultSelection": true}, {"key": "priceLowToHigh", "title": "Price (Low To High)", "selected": false, "defaultSelection": false}, {"key": "priceHighToLow", "title": "Price (High To Low)", "selected": false, "defaultSelection": false}, {"key": "discountPercentHighToLow", "title": "Discount (High To Low)", "selected": false, "defaultSelection": false}], "quickFilterTitle": "Quick filters"}], "type": "FILTER_WIDGET", "priority": 1, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3263, "id": 101076, "active": 1, "widgetInfo": {"widgetType": "TEXT_WIDGET", "header": null, "title": "<b>53 items </b>in Fresh Vegetables", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "", "platformVersionInfoList": null, "widgetStyles": null, "cities": [], "widgetIdentifier": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "101076"}}}, "data": null, "type": "TEXT_WIDGET", "priority": 2, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3263, "id": 101237, "active": 1, "widgetInfo": {"widgetType": "PRODUCT_LIST", "header": null, "title": "", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "", "platformVersionInfoList": null, "widgetStyles": null, "cities": [], "widgetIdentifier": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "101237"}}, "itemCount": 20}, "data": [{"display_name": "<PERSON><PERSON> (Tamatar)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "V2C3JZ8DJ2", "display_name": "<PERSON><PERSON> (Tamatar)", "listing_variant": true, "spin": "0GWBSRKMYQ", "super_saver": false, "images": ["054e83b5ebe789cf4b7b146319df4cfc", "f00e32194bcd76550c7e3dc1fe2e4ac4"], "images_v2": [], "price": {"mrp": 50, "store_price": 50, "offer_price": 40, "offer_applied": {"offer_id": "d5b2f0ae-a2c7-4cdf-b7b8-a627b7f7a2df", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 40, "non_super_offer_price": 40}, "discount_value": 10, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Juicy, tangy & ideal for all Indian curries", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "Only 6 unit(s) of this item can be added per order."}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": [1754310638839]}, "default": false, "product_name_without_brand": "<PERSON><PERSON> (Tamatar)", "sourcing_time": "Sourced at 5 AM", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Tomato", "sub_category_l4": "Indian Tomato", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"V2C3JZ8DJ2\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "Q3J3C2YRB4", "product_name_without_brand": "<PERSON><PERSON> (Tamatar)"}, {"display_name": "Hybrid Tomato (Tamatar)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "T7XB6SQXNO", "display_name": "Hybrid Tomato (Tamatar)", "listing_variant": true, "spin": "TOXY8MIG4N", "super_saver": false, "images": ["4a50a7b9bb1791dc454235a8943fcb90", "cadf7af37de6a356a1ecce7f8fd2375c"], "images_v2": [], "price": {"mrp": 55, "store_price": 55, "offer_price": 44, "offer_applied": {"offer_id": "6daa6ee9-51b8-4abf-bcfe-4c340044af2c", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 44, "non_super_offer_price": 44}, "discount_value": 11, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Fresh, firm & great in gravies or sauces", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "Only 6 unit(s) of this item can be added per order."}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Hybrid Tomato (Tamatar)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Tomato", "sub_category_l4": "Hybrid Tomato", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"T7XB6SQXNO\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "A8S21QP2A1", "product_name_without_brand": "Hybrid Tomato (Tamatar)"}, {"display_name": "Organic Certified Hybrid Tomato", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "6V6JRZIIJX", "display_name": "Organic Certified Hybrid Tomato", "listing_variant": true, "spin": "RBCL4S5JU7", "super_saver": false, "images": ["6294841c36b68534ec2edcccea0fa41b", "cadf7af37de6a356a1ecce7f8fd2375c"], "images_v2": [], "price": {"mrp": 61, "store_price": 61, "offer_price": 49, "offer_applied": {"offer_id": "8c0c5413-d437-4ad9-be85-665ef4df1f98", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 49, "non_super_offer_price": 49}, "discount_value": 12, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Pure, high-yield, rich taste for daily cooking", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 15, "width_in_cm": 16, "height_in_cm": 3, "volume_in_cc": 720}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Organic", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Organic Certified Hybrid Tomato", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Tomato", "sub_category_l4": "Hybrid Tomato", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Organics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"6V6JRZIIJX\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "5CAAARJD79", "product_name_without_brand": "Organic Certified Hybrid Tomato"}, {"display_name": "Potato (Aloo)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "3VGZBCNP9H", "display_name": "Potato", "listing_variant": false, "spin": "R86ACGK8JG", "super_saver": true, "images": ["0fd6f926feeab1b82e3579673277e01a", "e4e6aac5e407b3cb41b667fd2153e1c9"], "images_v2": [], "price": {"mrp": 123, "store_price": 123, "offer_price": 96, "offer_applied": {"offer_id": "564a1e79-ff33-4049-823c-44288dfbb6c5", "listing_description": "21% OFF", "product_description": "21% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "32/kg", "loyalty_pricing": {"super_offer_price": 96, "non_super_offer_price": 96}, "discount_value": 27, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "3", "sku_quantity_with_combo": "1 kg x 3", "unit_of_measure": "kg", "meta": {"short_description": "Agra’s finest potatoes; great for fries, mash & curries", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "Only 1 unit(s) of this item can be added per order."}, "weight_in_grams": 3000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 24, "width_in_cm": 20, "height_in_cm": 2, "volume_in_cc": 960}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Potato", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Potato", "sub_category_l4": "Regular", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "GTPCLC1JU5", "display_name": "Potato (Aloo)", "listing_variant": true, "spin": "NI3G71DSGI", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2025/1/15/d81d80c8-9153-4e88-9cb1-4eb5f01b412e_1108.png", "e4e6aac5e407b3cb41b667fd2153e1c9"], "images_v2": [], "price": {"mrp": 41, "store_price": 41, "offer_price": 33, "offer_applied": {"offer_id": "564a1e79-ff33-4049-823c-44288dfbb6c5", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 33, "non_super_offer_price": 33}, "discount_value": 8, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 kg", "unit_of_measure": "kg", "meta": {"short_description": "Agra’s finest potatoes; great for fries, mash & curries", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 5, "cart_allowed_quantity": {"total": 5, "message": "Only 5 unit(s) of this item can be added per order."}, "weight_in_grams": 1000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Potato (Aloo)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Potato", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"GTPCLC1JU5\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "A4RKJ8JRVI", "product_name_without_brand": "Potato (Aloo)"}, {"display_name": "Organic Certified Potato (Aloo)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "NKAG4HWFI9", "display_name": "Organic Certified Potato (Aloo)", "listing_variant": true, "spin": "O1RGYUTXM3", "super_saver": false, "images": ["62dcb64ffe8e03419c79d5b621a784d5", "eb30d285ea5775511afb9c28e1047a1b"], "images_v2": [], "price": {"mrp": 59, "store_price": 59, "offer_price": 47, "offer_applied": {"offer_id": "ec709252-9df8-4bf1-b06d-de908bc535d0", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 47, "non_super_offer_price": 47}, "discount_value": 12, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 kg", "unit_of_measure": "kg", "meta": {"short_description": "Agra’s finest potatoes; great for fries, mash & curries", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 1, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Potato", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Organic", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Organic Certified Potato (Aloo)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Potato", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Organics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"NKAG4HWFI9\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "GHYKQ0JOGK", "product_name_without_brand": "Organic Certified Potato (Aloo)"}, {"display_name": "<PERSON>'s Finger (Bhindi)", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "7491C1GXSD", "display_name": "<PERSON>'s Finger (Bhindi)", "listing_variant": true, "spin": "LF7QPS6SNK", "super_saver": false, "images": ["576c7500d96e8d87fa5194ecc977a9ce", "83b2b1365e771c008e5dcb18b8c6c766"], "images_v2": [], "price": {"mrp": 25, "store_price": 25, "offer_price": 20, "offer_applied": {"offer_id": "baa3dd4d-77a9-4af2-b6d2-8c24555bd50e", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "8/100 g", "loyalty_pricing": {"super_offer_price": 20, "non_super_offer_price": 20}, "discount_value": 5, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Fiber-rich, fresh & great for bhindi fry or curry", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 10, "width_in_cm": 10, "height_in_cm": 5, "volume_in_cc": 500}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON>'s Finger (Bhindi)", "sourcing_time": "Sourced at 5 AM", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Lady's Finger", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "MWTNLZ612H", "display_name": "Lady's Finger Combo", "listing_variant": false, "spin": "AXIIW8LUYD", "super_saver": false, "images": ["576c7500d96e8d87fa5194ecc977a9ce", "83b2b1365e771c008e5dcb18b8c6c766"], "images_v2": [], "price": {"mrp": 50, "store_price": 50, "offer_price": 37, "offer_applied": {"offer_id": "baa3dd4d-77a9-4af2-b6d2-8c24555bd50e", "listing_description": "26% OFF", "product_description": "26% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "7.4/100 g", "loyalty_pricing": {"super_offer_price": 37, "non_super_offer_price": 37}, "discount_value": 13, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "250 g x 2", "unit_of_measure": "g", "meta": {"short_description": "Fiber-rich, fresh & great for bhindi fry or curry", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": false, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 0, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Lady's Finger Combo", "sourcing_time": "Sourced at 5 AM", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Lady's Finger", "sub_category_l4": "Regular", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"7491C1GXSD\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "AHEHQCK0WW", "product_name_without_brand": "<PERSON>'s Finger (Bhindi)"}, {"display_name": "Organic Certified Lady's Finger", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "BNBICULTM8", "display_name": "Organic Certified Lady's Finger", "listing_variant": true, "spin": "9RYY58AJ2J", "super_saver": false, "images": ["453075e6eea4c156a8214418abf08089"], "images_v2": [], "price": {"mrp": 38, "store_price": 38, "offer_price": 30, "offer_applied": {"offer_id": "8c014cbb-2859-440d-8a8f-a4ac5435f88b", "listing_description": "21% OFF", "product_description": "21% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 30, "non_super_offer_price": 30}, "discount_value": 8, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Organic, crisp & healthy for daily meals", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 24, "width_in_cm": 18, "height_in_cm": 2, "volume_in_cc": 864}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Organic", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Organic Certified Lady's Finger", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Lady's Finger", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Organics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"BNBICULTM8\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "GPD8TK86UN", "product_name_without_brand": "Organic Certified Lady's Finger"}, {"display_name": "Onion (Pyaaz)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "FVFO5YYZTG", "display_name": "Onion", "listing_variant": false, "spin": "JLO0J9XUU5", "super_saver": true, "images": ["765ff374dd4023fde2d3936ec482814d", "05b5c3c44a0fd1ac737337add052f4a3"], "images_v2": [], "price": {"mrp": 135, "store_price": 135, "offer_price": 105, "offer_applied": {"offer_id": "207522c7-5ad4-43a1-89c6-300837288db7", "listing_description": "22% OFF", "product_description": "22% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "35/kg", "loyalty_pricing": {"super_offer_price": 105, "non_super_offer_price": 105}, "discount_value": 30, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "3", "sku_quantity_with_combo": "1 kg x 3", "unit_of_measure": "kg", "meta": {"short_description": "Rich flavor, high in antioxidants, perfect for everyday cooking", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "Only 1 unit(s) of this item can be added per order."}, "weight_in_grams": 3000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Onion", "dimensions": {"length_in_cm": 22, "width_in_cm": 23, "height_in_cm": 5, "volume_in_cc": 2530}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Onion", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Onion", "sub_category_l4": "Regular", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "KNF9LWD1NV", "display_name": "Onion (Pyaaz)", "listing_variant": true, "spin": "FG8WDTXSUN", "super_saver": false, "images": ["765ff374dd4023fde2d3936ec482814d", "05b5c3c44a0fd1ac737337add052f4a3"], "images_v2": [], "price": {"mrp": 45, "store_price": 45, "offer_price": 36, "offer_applied": {"offer_id": "207522c7-5ad4-43a1-89c6-300837288db7", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 36, "non_super_offer_price": 36}, "discount_value": 9, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 kg", "unit_of_measure": "kg", "meta": {"short_description": "Rich flavor, high in antioxidants, perfect for everyday cooking", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 5, "cart_allowed_quantity": {"total": 5, "message": "Only 5 unit(s) of this item can be added per order."}, "weight_in_grams": 1000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 15, "width_in_cm": 12, "height_in_cm": 12, "volume_in_cc": 2160}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": [1754115037438]}, "default": false, "product_name_without_brand": "Onion (Pyaaz)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Onion", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"KNF9LWD1NV\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "4M2JLMBEXY", "product_name_without_brand": "Onion (Pyaaz)"}, {"display_name": "Green Capsicum", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "YYWSH8ZA7M", "display_name": "Green Capsicum", "listing_variant": true, "spin": "6LQGC4JOV8", "super_saver": false, "images": ["b817b4278093e6a7ca0b95496806d6bd", "ebcbb61a84568a46d7ee979a1f13baaf"], "images_v2": [], "price": {"mrp": 50, "store_price": 50, "offer_price": 40, "offer_applied": {"offer_id": "1ebdec37-3a1c-444b-a5fa-b9d2bd0648a4", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 40, "non_super_offer_price": 40}, "discount_value": 10, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 Medium", "unit_of_measure": "Medium", "meta": {"short_description": "Crunchy, vitamin-rich bell pepper for any dish!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 3, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 20, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "250 - 350 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Green Capsicum", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Capsicum", "sub_category_l4": "Green Capsicum", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"YYWSH8ZA7M\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "7H5FH0OAN6", "product_name_without_brand": "Green Capsicum"}, {"display_name": "Red & Yellow Bell Peppers", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "CZ5D7AK54Q", "display_name": "Red & Yellow Bell Peppers", "listing_variant": true, "spin": "834G9JIV7K", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2025/6/17/7a7b497d-264b-40ea-ac22-6ad41fedb746_3797_1.png", "0b66d693c01e7bf5e190bed4c3f639a8", "bf363c3c51a78b0c33dacb5ca8039d8d"], "images_v2": [], "price": {"mrp": 163, "store_price": 163, "offer_price": 130, "offer_applied": {"offer_id": "39588670-4337-4f9c-979b-2ddae9e78dbe", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 130, "non_super_offer_price": 130}, "discount_value": 33, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Sweet, colorful peppers for gourmet cooking!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "250 - 400g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Red & Yellow Bell Peppers", "sourcing_time": "Sourced at 5 AM", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Capsicum", "sub_category_l4": "Exotics", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Exotics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"CZ5D7AK54Q\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "NOJL3WVNXG", "product_name_without_brand": "Red & Yellow Bell Peppers"}, {"display_name": "<PERSON><PERSON><PERSON> (Carrot)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "QYAX00QSIU", "display_name": "<PERSON><PERSON><PERSON> (Carrot)", "listing_variant": true, "spin": "LD6149U2Z0", "super_saver": false, "images": ["cd9136ecedec6ed4f49a568cf7be8dbc", "c9b9f394e9e1d8e64e1a1bd84fd7d90b", "900a402fbc4a570045137509591e1478"], "images_v2": [], "price": {"mrp": 31, "store_price": 31, "offer_price": 25, "offer_applied": {"offer_id": "aea68f6e-952f-4cd6-a110-2bc6cda7b03b", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 25, "non_super_offer_price": 25}, "discount_value": 6, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Sweet mountain-grown carrot with vibrant color!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 5, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON><PERSON> (Carrot)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Carrot", "sub_category_l4": "<PERSON><PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"QYAX00QSIU\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "8XUTKS0CZ3", "product_name_without_brand": "<PERSON><PERSON><PERSON> (Carrot)"}, {"display_name": "<PERSON><PERSON><PERSON>ber", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "4LNBSCV4NB", "display_name": "<PERSON><PERSON><PERSON>ber", "listing_variant": true, "spin": "SWKMAPUSAM", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2024/5/6/2bea05e2-0359-47d7-ae52-999430efb3f3_freshvegetables_MFPXQBV7WU_MN.png"], "images_v2": [], "price": {"mrp": 45, "store_price": 45, "offer_price": 36, "offer_applied": {"offer_id": "ead53b0e-e163-450c-b655-7f14b817893c", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 36, "non_super_offer_price": 36}, "discount_value": 9, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Fresh, juicy & best in salads or chutneys", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 3, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 2, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 22, "width_in_cm": 18, "height_in_cm": 3, "volume_in_cc": 1188}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON><PERSON>ber", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON>ber", "sub_category_l4": "Green Cucumber", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"4LNBSCV4NB\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "AIZ20L10E6", "product_name_without_brand": "<PERSON><PERSON><PERSON>ber"}, {"display_name": "Bottle Gourd Round (Go<PERSON> Lauki)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "9ZHG7R0QX0", "display_name": "Bottle Gourd Round (Go<PERSON> Lauki)", "listing_variant": true, "spin": "NBZL5XGWLO", "super_saver": false, "images": ["93e7ffab81f6d499a8066b5dbec7e13a", "0043431a1254f436faad93e7fc3a2a07"], "images_v2": [], "price": {"mrp": 38, "store_price": 38, "offer_price": 30, "offer_applied": {"offer_id": "d3123a22-a491-4be9-8650-b959b2ea7901", "listing_description": "21% OFF", "product_description": "21% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 30, "non_super_offer_price": 30}, "discount_value": 8, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Piece", "unit_of_measure": "Piece", "meta": {"short_description": "Tender, water-rich & great in curries or soups", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 6, "message": "Only 6 unit(s) of this item can be added per order."}, "weight_in_grams": 10, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Bottle Gourd", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "800 - 1.5 Kg", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Bottle Gourd Round (Go<PERSON> Lauki)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Gourd", "sub_category_l4": "Bottle Gourd", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"9ZHG7R0QX0\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "ZDWW6FN4N3", "product_name_without_brand": "Bottle Gourd Round (Go<PERSON> Lauki)"}, {"display_name": "Pointed Gourd", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "XVD61SCONF", "display_name": "Pointed Gourd", "listing_variant": true, "spin": "K6Y51ILLG8", "super_saver": false, "images": ["d75f6331bc07532081234babe24583f2", "1af421251b461f8526a1adb20b3a9c18"], "images_v2": [], "price": {"mrp": 35, "store_price": 35, "offer_price": 28, "offer_applied": {"offer_id": "7da696d2-96d5-4bc9-927d-5e8c6f2edf18", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "11.2/100 g", "loyalty_pricing": {"super_offer_price": 28, "non_super_offer_price": 28}, "discount_value": 7, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Soft, nutritious & perfect in curry or fry", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Pointed Gourd", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Gourd", "sub_category_l4": "Pointed Gourd", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "WKM03UZGOW", "display_name": "Pointed Gourd", "listing_variant": false, "spin": "NJQZQ3XWF9", "super_saver": false, "images": ["d75f6331bc07532081234babe24583f2", "1af421251b461f8526a1adb20b3a9c18"], "images_v2": [], "price": {"mrp": 70, "store_price": 70, "offer_price": 53, "offer_applied": {"offer_id": "7da696d2-96d5-4bc9-927d-5e8c6f2edf18", "listing_description": "24% OFF", "product_description": "24% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "10.6/100 g", "loyalty_pricing": {"super_offer_price": 53, "non_super_offer_price": 53}, "discount_value": 17, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "250 g x 2", "unit_of_measure": "g", "meta": {"short_description": "Tender and mild, great for curries and stir-fries.", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": false, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 0, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Pointed Gourd", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Gourd", "sub_category_l4": "Pointed Gourd", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"XVD61SCONF\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "GXYWORFDVR", "product_name_without_brand": "Pointed Gourd"}, {"display_name": "French Beans (Phaliyaan)", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "KDGUKMZ07X", "display_name": "French Beans (Phaliyaan)", "listing_variant": true, "spin": "BR1WMWX8M6", "super_saver": false, "images": ["cf1faf72f6945dada7ffccf44a98ca01", "7b2256bc7f66a8ab65173866c7c8d7e1"], "images_v2": [], "price": {"mrp": 59, "store_price": 59, "offer_price": 47, "offer_applied": {"offer_id": "4cacd5e7-d97f-4613-8c89-5406dfb1ce04", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 47, "non_super_offer_price": 47}, "discount_value": 12, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Crisp, fiber-rich & great for dry sabzis", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "<PERSON>s", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "French Beans (Phaliyaan)", "sourcing_time": "Sourced at 5 AM", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON>s", "sub_category_l4": "French Beans", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"KDGUKMZ07X\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "GYZQB43AGT", "product_name_without_brand": "French Beans (Phaliyaan)"}, {"display_name": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "Y157DOV6JI", "display_name": "<PERSON><PERSON>a Purple Brinjal Combo", "listing_variant": false, "spin": "056AMI2WP9", "super_saver": true, "images": ["NI_CATALOG/IMAGES/CIW/2024/6/3/f249a564-bce8-428d-a927-5456bac1a045_freshvegetables_AHH0F7IKBG_MN.png", "bda9f02b09eff3f24a39850e933702b2"], "images_v2": [], "price": {"mrp": 56, "store_price": 56, "offer_price": 42, "offer_applied": {"offer_id": "4b0332a6-1a4c-424e-b320-ba3059998f68", "listing_description": "25% OFF", "product_description": "25% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "21/piece", "loyalty_pricing": {"super_offer_price": 42, "non_super_offer_price": 42}, "discount_value": 14, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "1 Piece x 2", "unit_of_measure": "pieces", "meta": {"short_description": "Rich, smoky flavor perfect for baingan bharta!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 800, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON>a Purple Brinjal Combo", "sourcing_time": "Sourced at 5 AM", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l4": "<PERSON> <PERSON><PERSON>", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "UVPIM0D6DS", "display_name": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "listing_variant": true, "spin": "SW2J07V5KK", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2024/5/31/7cc6c585-7f9f-4a57-ac38-0d5d61e672d4_freshvegetables_AHH0F7IKBG_MN.png", "bda9f02b09eff3f24a39850e933702b2"], "images_v2": [], "price": {"mrp": 28, "store_price": 28, "offer_price": 22, "offer_applied": {"offer_id": "4b0332a6-1a4c-424e-b320-ba3059998f68", "listing_description": "21% OFF", "product_description": "21% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 22, "non_super_offer_price": 22}, "discount_value": 6, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Piece", "unit_of_measure": "Piece", "meta": {"short_description": "Rich, smoky flavor perfect for baingan bharta!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 400, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "<PERSON><PERSON><PERSON><PERSON>", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "sourcing_time": "Sourced at 5 AM", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l4": "<PERSON> <PERSON><PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"UVPIM0D6DS\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "WUG5SXPJGP", "product_name_without_brand": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)"}, {"display_name": "Small Brinjal", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "SGAYWUGXN8", "display_name": "Small Brinjal", "listing_variant": false, "spin": "Y7J2MPO8B5", "super_saver": false, "images": ["68a814349e6c9fc5362791c5d27ef3e9", "cf612fe463b319d80006da72304854fe"], "images_v2": [], "price": {"mrp": 58, "store_price": 58, "offer_price": 43, "offer_applied": {"offer_id": "57edf302-4a5b-45b2-96e0-491a9a3bc530", "listing_description": "25% OFF", "product_description": "25% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "8.6/100 g", "loyalty_pricing": {"super_offer_price": 43, "non_super_offer_price": 43}, "discount_value": 15, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "250 g x 2", "unit_of_measure": "g", "meta": {"short_description": "Spicy stuffed preparation", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Small Brinjal", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l4": "Small Brinjal", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "86R633K5S0", "display_name": "Small Brinjal", "listing_variant": true, "spin": "5HKRSOPBOF", "super_saver": false, "images": ["68a814349e6c9fc5362791c5d27ef3e9", "cf612fe463b319d80006da72304854fe"], "images_v2": [], "price": {"mrp": 29, "store_price": 29, "offer_price": 23, "offer_applied": {"offer_id": "57edf302-4a5b-45b2-96e0-491a9a3bc530", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "9.2/100 g", "loyalty_pricing": {"super_offer_price": 23, "non_super_offer_price": 23}, "discount_value": 6, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Tender, fiber-rich & great for stuffed dishes", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Small Brinjal", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l4": "Small Brinjal", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"86R633K5S0\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "O87WWOBS6V", "product_name_without_brand": "Small Brinjal"}, {"display_name": "Organic Certified Cauliflower", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "8R249MOAU5", "display_name": "Organic Certified Cauliflower", "listing_variant": true, "spin": "RYQXZWVD4Q", "super_saver": false, "images": ["90ad882555482c17d0a6ed7603eaa23c"], "images_v2": [], "price": {"mrp": 136, "store_price": 136, "offer_price": 109, "offer_applied": {"offer_id": "29632707-b583-413d-8b07-7a25d3dc60bd", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 109, "non_super_offer_price": 109}, "discount_value": 27, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Piece", "unit_of_measure": "Piece", "meta": {"short_description": "Snow-white florets, perfect for curries & roasts!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 1, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Organic", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "400 - 600 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Organic Certified Cauliflower", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sub_category_l4": "Vegetable", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Organics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"8R249MOAU5\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "8ZC8BTSK6O", "product_name_without_brand": "Organic Certified Cauliflower"}, {"display_name": "<PERSON>", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "5JCJ1LK6C2", "display_name": "<PERSON>", "listing_variant": true, "spin": "5Y80B39I25", "super_saver": false, "images": ["dc8d35d6de49f1a72c28e32f42606b61", "08714214ae09b7a36298f1ed4d118cea"], "images_v2": [], "price": {"mrp": 40, "store_price": 40, "offer_price": 32, "offer_applied": {"offer_id": "7a3c4e75-76a4-40d1-90e8-b1cce7800142", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 32, "non_super_offer_price": 32}, "discount_value": 8, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Piece", "unit_of_measure": "Piece", "meta": {"short_description": "Young, tender squash for soups & stir-fries!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 1200, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "<PERSON><PERSON><PERSON>", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "1 - 2 kg", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON>", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON>", "sub_category_l4": "<PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"5JCJ1LK6C2\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "FMD0WSYRX1", "product_name_without_brand": "<PERSON>"}, {"display_name": "Beetroot (Chukandar)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "B1CM8FI980", "display_name": "Beetroot (Chukandar)", "listing_variant": true, "spin": "JK9DBBC4GW", "super_saver": false, "images": ["2c16f47d900ff298b17c4e95fe1d755c", "d6383f3b798a8bdaee611dd8a504cc6d"], "images_v2": [], "price": {"mrp": 45, "store_price": 45, "offer_price": 36, "offer_applied": {"offer_id": "c59a2a95-413c-4228-ba5f-fbbb9c3ea760", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 36, "non_super_offer_price": 36}, "discount_value": 9, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Rich in iron, earthy sweetness, ideal for salads/sides", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 3, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Beetroot", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Beetroot (Chukandar)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Beetroot", "sub_category_l4": "Vegetable", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"B1CM8FI980\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "QY3LA3QLL3", "product_name_without_brand": "Beetroot (Chukandar)"}], "type": "PRODUCT_LIST", "priority": 3, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3263, "id": 121584, "active": 1, "widgetInfo": {"widgetType": "NxM_BANNER", "title": "", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "", "subType": "PRODUCT_SUGGESTION_WIDGET", "widgetStyles": {"padding": {"top": "16", "right": "16", "bottom": "16", "left": "16"}}, "cities": [], "widgetIdentifier": "PRODUCT_SUGGESTION_WIDGET", "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "121584"}}, "row": 1, "column": 1, "aspectRatio": {"width": "140", "height": "140"}, "carouselWidgetType": "IM_PRODUCT_SUGGESTION", "minimumBanner": 1, "fullGrid": true, "orientation": "VERTICAL", "fullMonetized": false, "clickable": false, "fitToWidth": false, "dataProvider": "CAROUSEL", "isMasthead": false}, "data": [{"bannerId": 3294164, "creativeId": "MERCHANDISING_BANNERS/IMAGES/MERCH/2024/9/9/34ae159d-5bbf-45b4-9a79-512f74831a96_BulbGreyBG5x1.png", "entityType": "deeplink", "entityId": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=3402&customerPage=STORES_MxN_45", "entityDeepLink": "/instamart/campaign-collection/mxn?custom_back=true&layoutId=3402&customerPage=STORES_MxN_45", "page": "STORES_MENU", "cappingEnabled": false, "widgetData": {"widgetType": "IM_PRODUCT_SUGGESTION", "itemsCreativeId": "/InstamartAssets/<EMAIL>?updatedAt=1725955300534", "text": "Suggest a product", "backgroundColor": "#FFFFFF", "subText": "Tell us what you need and we'll do our best to stock up!", "description": "Fresh paneer, baked chips, Lego, adidas", "monetised": true}, "metadata": {"offerText": null}, "creativeType": "IMAGE", "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-assortment-suggestion-banner", "clickObjectName": "click-assortment-suggestion-banner", "extraFields": {"l2NodeVal": "Fresh Vegetables", "bannerId": "3294164"}}, "score": 0, "adItem": false}], "type": "NxM_BANNER", "priority": 4, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}, {"collectionId": 1, "layoutId": 3263, "id": 121583, "active": 1, "widgetInfo": {"widgetType": "PRODUCT_LIST", "header": null, "title": "", "fallbackTitle": "", "affordance": false, "autoScrollTime": -1, "description": "", "platformVersionInfoList": null, "widgetStyles": null, "cities": [], "widgetIdentifier": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{}", "impressionObjectName": "impression-menu-widget", "clickObjectName": "click-cta", "extraFields": {"widgetId": "121583"}}, "itemCount": 20}, "data": [{"display_name": "Sweet Potato (Shakarkand)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "N8M9KOUQMX", "display_name": "Sweet Potato", "listing_variant": false, "spin": "GSBC5RG88X", "super_saver": false, "images": ["3ce01c7320ab18d6829acc8f38dbf1e2", "3e1a92fef922c6bd2c24f5a577ae94b7"], "images_v2": [], "price": {"mrp": 176, "store_price": 176, "offer_price": 138, "offer_applied": {"offer_id": "ab8b0a68-5719-4ce0-ab76-ac81853a398b", "listing_description": "21% OFF", "product_description": "21% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "13.8/100 g", "loyalty_pricing": {"super_offer_price": 138, "non_super_offer_price": 138}, "discount_value": 38, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "500 g x 2", "unit_of_measure": "kg", "meta": {"short_description": "Iron-rich, sweet & tasty when roasted or boiled", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 1000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Sweet Potato", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Sweet Potato", "sub_category_l4": "Regular", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "KRU2BD57PQ", "display_name": "Sweet Potato (Shakarkand)", "listing_variant": true, "spin": "JO9UG2AOLX", "super_saver": false, "images": ["3ce01c7320ab18d6829acc8f38dbf1e2", "3e1a92fef922c6bd2c24f5a577ae94b7"], "images_v2": [], "price": {"mrp": 88, "store_price": 88, "offer_price": 70, "offer_applied": {"offer_id": "ab8b0a68-5719-4ce0-ab76-ac81853a398b", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "14/100 g", "loyalty_pricing": {"super_offer_price": 70, "non_super_offer_price": 70}, "discount_value": 18, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Iron-rich, sweet & tasty when roasted or boiled", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Sweet Potato (Shakarkand)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Sweet Potato", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"KRU2BD57PQ\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "FKNOG7L8AL", "product_name_without_brand": "Sweet Potato (Shakarkand)"}, {"display_name": "Coccinia", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "5R4ZIB6W9D", "display_name": "Coccinia Combo", "listing_variant": false, "spin": "SQ8311JQK9", "super_saver": false, "images": ["00c6c04557144b78f3927e836ca1792a", "39adb2e8c5060b2af4a081b976e0dabd"], "images_v2": [], "price": {"mrp": 48, "store_price": 48, "offer_price": 35, "offer_applied": {"offer_id": "59f4cad5-777e-4aa1-8617-96af19d928f4", "listing_description": "27% OFF", "product_description": "27% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "7/100 g", "loyalty_pricing": {"super_offer_price": 35, "non_super_offer_price": 35}, "discount_value": 13, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "250 g x 2", "unit_of_measure": "g", "meta": {"short_description": "Low-cal, crisp, ideal for stir-fry & sambar", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Coccinia", "dimensions": {"length_in_cm": 17, "width_in_cm": 9, "height_in_cm": 5, "volume_in_cc": 765}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Coccinia Combo", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Coccinia", "sub_category_l4": "Regular", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "SDHWRP04S9", "display_name": "Coccinia", "listing_variant": true, "spin": "L0Y2YQWMI6", "super_saver": false, "images": ["00c6c04557144b78f3927e836ca1792a", "39adb2e8c5060b2af4a081b976e0dabd"], "images_v2": [], "price": {"mrp": 24, "store_price": 24, "offer_price": 19, "offer_applied": {"offer_id": "59f4cad5-777e-4aa1-8617-96af19d928f4", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "7.6/100 g", "loyalty_pricing": {"super_offer_price": 19, "non_super_offer_price": 19}, "discount_value": 5, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Low-cal, crisp, ideal for stir-fry & sambar", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 3, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Coccinia", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": [1754286021931]}, "default": false, "product_name_without_brand": "Coccinia", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Coccinia", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"SDHWRP04S9\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "IZBFQNTHU4", "product_name_without_brand": "Coccinia"}, {"display_name": "Green Peas (Matar)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "L1SMHX2VKR", "display_name": "Green Peas (Matar)", "listing_variant": true, "spin": "4YVALFTTFJ", "super_saver": false, "images": ["9334ed74e5ec6472e4ac60404fc22a5e", "e07e0954951773ff1488c40bb217ebfc"], "images_v2": [], "price": {"mrp": 138, "store_price": 138, "offer_price": 110, "offer_applied": {"offer_id": "963c5f97-fc68-41d0-9c89-331588c4d02b", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 110, "non_super_offer_price": 110}, "discount_value": 28, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Sweet and protein-rich green pods", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 3, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Green Peas", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Green Peas (Matar)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Peas", "sub_category_l4": "Green Peas", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"L1SMHX2VKR\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "BSN7CF9GBV", "product_name_without_brand": "Green Peas (Matar)"}, {"display_name": "<PERSON>", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "SFQR7OR3VZ", "display_name": "<PERSON>", "listing_variant": true, "spin": "AWAAXU078H", "super_saver": false, "images": ["1784371265a5f4a8acc2fc3710f3a7e2", "1ad8369510be7000e14de8399fee92e6"], "images_v2": [], "price": {"mrp": 81, "store_price": 81, "offer_price": 65, "offer_applied": {"offer_id": "7a2d8099-5871-4538-ba42-69448360cc6d", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 65, "non_super_offer_price": 65}, "discount_value": 16, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Piece", "unit_of_measure": "Piece", "meta": {"short_description": "Low-cal, tender & best in grills or pasta", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 4, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 300, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "<PERSON><PERSON><PERSON><PERSON>", "dimensions": {"length_in_cm": 28, "width_in_cm": 25, "height_in_cm": 3, "volume_in_cc": 2100}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "150 - 220 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON>", "sourcing_time": "Sourced at 5 AM", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l4": "Exotics", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Exotics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"SFQR7OR3VZ\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "5IW5JVJJD1", "product_name_without_brand": "<PERSON>"}, {"display_name": "Sambar Onion (Pyaaz)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "7K8DOZUY6Q", "display_name": "Sambar Onion Combo", "listing_variant": false, "spin": "B4ANWXJYMR", "super_saver": false, "images": ["c4bf6be996dcdcc63d36a1733a99d7db", "9068b0383baff57c3a8f5d74d15c5992"], "images_v2": [], "price": {"mrp": 48, "store_price": 48, "offer_price": 35, "offer_applied": {"offer_id": "343b2bbe-8ef3-42a4-96cb-0f33a7956440", "listing_description": "27% OFF", "product_description": "27% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "7/100 g", "loyalty_pricing": {"super_offer_price": 35, "non_super_offer_price": 35}, "discount_value": 13, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "250 g x 2", "unit_of_measure": "g", "meta": {"short_description": "Tiny, pure & ideal for authentic sambhar", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 16, "width_in_cm": 9, "height_in_cm": 1, "volume_in_cc": 144}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Sambar Onion Combo", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Sambar Onion", "sub_category_l4": "Regular", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "KF6LOKMCWR", "display_name": "Sambar Onion (Pyaaz)", "listing_variant": true, "spin": "9HIGULYMZJ", "super_saver": false, "images": ["c4bf6be996dcdcc63d36a1733a99d7db", "9068b0383baff57c3a8f5d74d15c5992"], "images_v2": [], "price": {"mrp": 24, "store_price": 24, "offer_price": 19, "offer_applied": {"offer_id": "343b2bbe-8ef3-42a4-96cb-0f33a7956440", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "7.6/100 g", "loyalty_pricing": {"super_offer_price": 19, "non_super_offer_price": 19}, "discount_value": 5, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Tiny, pure & ideal for authentic sambhar", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 3, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Sambar Onion (Pyaaz)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Sambar Onion", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"KF6LOKMC<PERSON>\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "PA8AUTS6T8", "product_name_without_brand": "Sambar Onion (Pyaaz)"}, {"display_name": "White Radish", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "XQ8GV01K79", "display_name": "White Radish", "listing_variant": true, "spin": "SD9XMHV8OO", "super_saver": false, "images": ["0d321ef3ac2fe266c1080b964ee07f92", "b3b604d5c1b09269fe4d617ca741d675"], "images_v2": [], "price": {"mrp": 51, "store_price": 51, "offer_price": 41, "offer_applied": {"offer_id": "1a28177a-79a0-4130-9c72-f714ab703219", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 41, "non_super_offer_price": 41}, "discount_value": 10, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Crunchy, Aids digestion, for salads & curries!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 20, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "200 - 300 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "White Radish", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Radish", "sub_category_l4": "White Radish", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"XQ8GV01K79\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "VH2D314W2D", "product_name_without_brand": "White Radish"}, {"display_name": "Raw Mango", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "U9TZHZSTPU", "display_name": "Mango Raw(Kaccha Aam)", "listing_variant": false, "spin": "1U4JEGQB4T", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2025/4/29/0d3acfb6-61fe-4519-91a4-6010e2303da1_450600.png"], "images_v2": [], "price": {"mrp": 140, "store_price": 140, "offer_price": 112, "offer_applied": {"offer_id": "582d0d22-4c36-4f56-b8a0-8da31153c4c8", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "11.2/100 g", "loyalty_pricing": {"super_offer_price": 112, "non_super_offer_price": 112}, "discount_value": 28, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "500 g x 2", "unit_of_measure": "kg", "meta": {"short_description": "Ads tangy flavour", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 1000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Mango", "dimensions": {"length_in_cm": 14, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 686}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Mango Raw(Kaccha Aam)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Raw Mango", "sub_category_l4": "Regular", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "YKQ6QM96QP", "display_name": "Raw Mango", "listing_variant": true, "spin": "TW2RYMPVA6", "super_saver": false, "images": ["4733bca7c289cea3305e16c5659bf5e7", "2aaab0299a723a4f7c439cea8c3a2612", "bc33161a06f9cd941767156f882deb73"], "images_v2": [], "price": {"mrp": 70, "store_price": 70, "offer_price": 56, "offer_applied": {"offer_id": "582d0d22-4c36-4f56-b8a0-8da31153c4c8", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "11.2/100 g", "loyalty_pricing": {"super_offer_price": 56, "non_super_offer_price": 56}, "discount_value": 14, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Sour, fresh & perfect for chutneys or salads", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Raw Mango", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Raw Mango", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"YKQ6QM96QP\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "NQSEGAVNKI", "product_name_without_brand": "Raw Mango"}, {"display_name": "Onion, Potato & Desi Tomato", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "IP3U6J6JBD", "display_name": "Onion, Potato & Desi Tomato", "listing_variant": true, "spin": "KO0OKYOMV1", "super_saver": false, "images": ["e3e554e14180b27ad2607002a26889c5"], "images_v2": [], "price": {"mrp": 186, "store_price": 186, "offer_price": 146, "offer_applied": {"offer_id": "d5b2f0ae-a2c7-4cdf-b7b8-a627b7f7a2df", "listing_description": "21% OFF", "product_description": "21% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 146, "non_super_offer_price": 146}, "discount_value": 40, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 combo", "unit_of_measure": "combo", "meta": {"short_description": "Perfect for Indian home-cooked meals", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "Only 1 unit(s) of this item can be added per order."}, "weight_in_grams": 4500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetable Combo", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Onion, Potato & Desi Tomato", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Combo Kit", "sub_category_l4": "Onion, Potato and Desi Tomato", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"IP3U6J6JBD\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "LC12VPBZHB", "product_name_without_brand": "Onion, Potato & Desi Tomato"}, {"display_name": "Onion, Potato & Hybrid Tomato", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "LMJU1BJT76", "display_name": "Onion, Potato & Hybrid Tomato", "listing_variant": true, "spin": "12VS6CSFB6", "super_saver": false, "images": ["d77abbcb56c4e631a90aca5b06734a67"], "images_v2": [], "price": {"mrp": 196, "store_price": 196, "offer_price": 154, "offer_applied": {"offer_id": "207522c7-5ad4-43a1-89c6-300837288db7", "listing_description": "21% OFF", "product_description": "21% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 154, "non_super_offer_price": 154}, "discount_value": 42, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 combo", "unit_of_measure": "combo", "meta": {"short_description": "Basic essentials for daily cooking", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 1, "cart_allowed_quantity": {"total": 1, "message": "Only 1 unit(s) of this item can be added per order."}, "weight_in_grams": 3000, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Onion, Potato & Hybrid Tomato", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Combo Kit", "sub_category_l4": "Onion, Potato and Hybrid Tomato", "sub_category_l5": "Virtual Combo", "scm_item_type": "VIRTUAL_COMBO", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"LMJU1BJT76\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "IUZPIVKF5Y", "product_name_without_brand": "Onion, Potato & Hybrid Tomato"}, {"display_name": "Organic Certified Green Chilli", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "A1PSHGTI45", "display_name": "Organic Certified Green Chilli", "listing_variant": true, "spin": "RN41Y1UNPY", "super_saver": false, "images": ["e85c9d0b1473434524a25b288cf04802", "b7fb9229918bfa5b9913536afd673a06"], "images_v2": [], "price": {"mrp": 29, "store_price": 29, "offer_price": 23, "offer_applied": {"offer_id": "c508f730-5166-4169-9d27-49b33ddd7fb6", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 23, "non_super_offer_price": 23}, "discount_value": 6, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "100", "sku_quantity_with_combo": "100 g", "unit_of_measure": "g", "meta": {"short_description": "Spicy and rich in vitamin C", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 100, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 20, "width_in_cm": 15, "height_in_cm": 1, "volume_in_cc": 300}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Organic", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Organic Certified Green Chilli", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON>", "sub_category_l4": "<PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Organics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"A1PSHGTI45\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "8OGW9E1JME", "product_name_without_brand": "Organic Certified Green Chilli"}, {"display_name": "<PERSON><PERSON><PERSON><PERSON>", "ad_tracking_context": null, "badges": [{"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}], "badge_info": {"badge_type": "SOURCE", "badge_text": "Sourced at 5 AM", "badge_color": "#60b24626", "text_color": "#60B246"}, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "ONCY4QGNBF", "display_name": "<PERSON><PERSON><PERSON><PERSON>", "listing_variant": true, "spin": "OFPZ37P15Y", "super_saver": false, "images": ["fea0f495679abbe56d043c1c9767b9a0", "b1166ce15ef290c334908bc31a2ea73d", "1bc1253a1f43acd19c3d14002213e706"], "images_v2": [], "price": {"mrp": 140, "store_price": 140, "offer_price": 112, "offer_applied": {"offer_id": "2d19d7f0-a836-4d22-a418-3c04e55db342", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 112, "non_super_offer_price": 112}, "discount_value": 28, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Piece", "unit_of_measure": "Piece", "meta": {"short_description": "Rich in fiber, fresh & perfect for stir-fries", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 350, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "<PERSON><PERSON><PERSON><PERSON>", "dimensions": {"length_in_cm": 29, "width_in_cm": 29, "height_in_cm": 3, "volume_in_cc": 2523}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "180 - 300 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON><PERSON><PERSON>", "sourcing_time": "Sourced at 5 AM", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l4": "Exotics", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Exotics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"ONCY4QGNBF\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "IKTH5T6WWY", "product_name_without_brand": "<PERSON><PERSON><PERSON><PERSON>"}, {"display_name": "Small Coconut", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "2NJN495NJC", "display_name": "Coconut (Fresh)", "listing_variant": false, "spin": "4GB8YONBD7", "super_saver": false, "images": ["NI_CATALOG/IMAGES/CIW/2025/5/9/837468c8-b0c1-4140-8b28-0611267baa9e_1128.png", "182a860f9fc3d9d617f592828186ce31"], "images_v2": [], "price": {"mrp": 75, "store_price": 75, "offer_price": 60, "offer_applied": {"offer_id": "e062ddae-f78d-4a59-b5ea-09254a9ac8b9", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 60, "non_super_offer_price": 60}, "discount_value": 15, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Medium", "unit_of_measure": "Medium", "meta": {"short_description": "Fresh, nutrient-dense, used in chutney & gravies", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 450, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Others", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "400 - 500 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Coconut (Fresh)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Coconut", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}, {"id": "5DNG0KA3BX", "display_name": "Small Coconut", "listing_variant": true, "spin": "U2TFPWOX1Z", "super_saver": false, "images": ["52f36a26eebfe1f4e79ccea10f9d4315", "5168678be71ff335f629d2ab4c4325d6", "4e261cdd411395717aae3f2b02203efc"], "images_v2": [], "price": {"mrp": 85, "store_price": 85, "offer_price": 68, "offer_applied": {"offer_id": "6ce4f62d-d640-4fb1-a82b-14439fa5e084", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 68, "non_super_offer_price": 68}, "discount_value": 17, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "1", "sku_quantity_with_combo": "1 Small", "unit_of_measure": "Small", "meta": {"short_description": "Fresh, nutrient-dense, used in chutney & gravies", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 5, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 1, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Flower", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "250 - 300g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Small Coconut", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Coconut", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"5DNG0KA3BX\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "V83GLCO8NF", "product_name_without_brand": "Small Coconut"}, {"display_name": "Organic Certified Garlic", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "L15708Y99F", "display_name": "Organic Certified Garlic", "listing_variant": true, "spin": "52L71FJQJ2", "super_saver": false, "images": ["d88d40240cca66dedaa88a64671d6c22", "84e6f156a10698dfad0a4c49f6af4b80"], "images_v2": [], "price": {"mrp": 68, "store_price": 68, "offer_price": 54, "offer_applied": {"offer_id": "4af4c39e-b94b-4a51-b6ba-1f80f6b1dbef", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 54, "non_super_offer_price": 54}, "discount_value": 14, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "200", "sku_quantity_with_combo": "200 g", "unit_of_measure": "g", "meta": {"short_description": "Fresh & ready, immunity booster, for curries & soups", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 1, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 200, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Organic", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Organic Certified Garlic", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON>", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Organics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"L15708Y99F\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "MD1B10T7VW", "product_name_without_brand": "Organic Certified Garlic"}, {"display_name": "Drumstick (<PERSON><PERSON><PERSON>)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "FOOGADG2F0", "display_name": "Drumstick (<PERSON><PERSON><PERSON>)", "listing_variant": true, "spin": "7HJR4TB1XT", "super_saver": false, "images": ["261cb787e0bf6458338dd6cc6da98326", "b6be90a331c902fa63eef3c09112d05c"], "images_v2": [], "price": {"mrp": 60, "store_price": 60, "offer_price": 48, "offer_applied": {"offer_id": "5e23a786-19ff-4400-8df1-723c53ac3213", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 48, "non_super_offer_price": 48}, "discount_value": 12, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Nutrient-rich moringa pods, perfect for sambar & Soups!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 3, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 200, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 52, "width_in_cm": 19, "height_in_cm": 1, "volume_in_cc": 988}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "80- 140 g", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Drumstick (<PERSON><PERSON><PERSON>)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Drumstick", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"FOOGADG2F0\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "7DHHBWWGJL", "product_name_without_brand": "Drumstick (<PERSON><PERSON><PERSON>)"}, {"display_name": "Groundnut Raw", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "1R8WOQTVPV", "display_name": "Groundnut Raw", "listing_variant": true, "spin": "LIUA2Z5VLI", "super_saver": false, "images": ["c43c1e29c5311c96a0063f38765cdd0a", "fe6f457bfe7b5da73bc769b8c915f045"], "images_v2": [], "price": {"mrp": 130, "store_price": 130, "offer_price": 104, "offer_applied": {"offer_id": "d6efed5e-6418-41b2-b390-59a33d8c82b1", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 104, "non_super_offer_price": 104}, "discount_value": 26, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Protein-packed peanuts for wholesome snacking!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": true, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 2, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Groundnut Raw", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Groundnut", "sub_category_l4": "Groundnut Raw", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"1R8WOQTVPV\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": true, "is_avail": true, "product_id": "O54F928T02", "product_name_without_brand": "Groundnut Raw"}, {"display_name": "<PERSON><PERSON><PERSON> (Tori)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "08SPTLUBGM", "display_name": "<PERSON><PERSON><PERSON> (Tori)", "listing_variant": true, "spin": "QIRWK36IF7", "super_saver": false, "images": ["wyyq7jq8geayxm5bhfwe"], "images_v2": [], "price": {"mrp": 43, "store_price": 43, "offer_price": 34, "offer_applied": {"offer_id": "5bfdd6f9-c37f-403c-a4b6-5fae40fe96c3", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 34, "non_super_offer_price": 34}, "discount_value": 9, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Tender, hydrating & perfect for simple curries", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": false, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 0, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON><PERSON><PERSON> (Tori)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Gourd", "sub_category_l4": "Sponge Gourd", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"08SPTLUBGM\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": false, "is_avail": true, "product_id": "9GE2GEJABQ", "product_name_without_brand": "<PERSON><PERSON><PERSON> (Tori)"}, {"display_name": "<PERSON> (<PERSON><PERSON>)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "BC8GUFRGYB", "display_name": "<PERSON> (<PERSON><PERSON>)", "listing_variant": true, "spin": "DX7SJWHNXZ", "super_saver": false, "images": ["56a0557e3d6af641d997ea5cc0809895", "698d94891f6edd627abb3479319f54de"], "images_v2": [], "price": {"mrp": 26, "store_price": 26, "offer_price": 21, "offer_applied": {"offer_id": "fa696fd6-d2e4-43dd-b0ad-a8ba6c83b68e", "listing_description": "19% OFF", "product_description": "19% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 21, "non_super_offer_price": 21}, "discount_value": 5, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "500", "sku_quantity_with_combo": "500 g", "unit_of_measure": "g", "meta": {"short_description": "Tender, buttery gems for perfect roasting!", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": false, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 0, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 500, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Potato", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "<PERSON> (<PERSON><PERSON>)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Potato", "sub_category_l4": "<PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"BC8GUFRGYB\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": false, "is_avail": true, "product_id": "AF0CZSSPKK", "product_name_without_brand": "<PERSON> (<PERSON><PERSON>)"}, {"display_name": "Long Purple Brinjal (Baingan)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "UC8SL8URQI", "display_name": "Long Purple Brinjal (Baingan)", "listing_variant": true, "spin": "WS1IB52FZR", "super_saver": false, "images": ["67880d057c94c4c902be397ce6b88fd5", "a154c882754f865151e52c0ba856405c", "3f93d1baf461845cc06347a2844af1e7"], "images_v2": [], "price": {"mrp": 30, "store_price": 30, "offer_price": 24, "offer_applied": {"offer_id": "b936882e-8e4e-4e3f-abc8-1cf7acb3658b", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 24, "non_super_offer_price": 24}, "discount_value": 6, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "250", "sku_quantity_with_combo": "250 g", "unit_of_measure": "g", "meta": {"short_description": "Soft, tasty & ideal for fries, curries", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": false, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 0, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 250, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Long Purple Brinjal (Baingan)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON><PERSON><PERSON><PERSON>", "sub_category_l4": "<PERSON>", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"UC8SL8URQI\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": false, "is_avail": true, "product_id": "H3J4UN1DXU", "product_name_without_brand": "Long Purple Brinjal (Baingan)"}, {"display_name": "Sweet Corn", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "NTB6678MM1", "display_name": "Sweet Corn", "listing_variant": true, "spin": "G100GAHFI6", "super_saver": false, "images": ["3cc58bf99badb8469fbf6b62559a66fc", "7a6030225b8afc28aaf0e3cc302e2df5"], "images_v2": [], "price": {"mrp": 58, "store_price": 58, "offer_price": 46, "offer_applied": {"offer_id": "a5c473e3-143d-4e21-81e4-d18069738a4e", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 46, "non_super_offer_price": 46}, "discount_value": 12, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Sweet, juicy & perfect for snacks, soups or salad", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": false, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 0, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 600, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "Vegetables", "dimensions": {"length_in_cm": 29, "width_in_cm": 19, "height_in_cm": 3, "volume_in_cc": 1653}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "300 - 400 g", "store_id": "1403687", "display_variant": false, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Sweet Corn", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "Sweet Corn", "sub_category_l4": "Exotics", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": "Exotics", "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"NTB6678MM1\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": false, "is_avail": true, "product_id": "GF1ST5YGE6", "product_name_without_brand": "Sweet Corn"}, {"display_name": "Raw Banana (Kaccha Kela)", "ad_tracking_context": null, "badges": [], "badge_info": null, "item_card_view": "V2", "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "variations": [{"id": "FRQRYLSFBM", "display_name": "Raw Banana (Kaccha Kela)", "listing_variant": true, "spin": "QY9S401RHA", "super_saver": false, "images": ["77c140b6aece7dfcaf37f69f02580895", "2331134f686b4aaf2b280ed4dd4bec6b"], "images_v2": [], "price": {"mrp": 48, "store_price": 48, "offer_price": 38, "offer_applied": {"offer_id": "a8299e8b-fa29-45a5-a49f-557b82b08ae6", "listing_description": "20% OFF", "product_description": "20% OFF", "super_offer": false, "mov_threshold": 0}, "unit_level_price": "", "loyalty_pricing": {"super_offer_price": 38, "non_super_offer_price": 38}, "discount_value": 10, "max_saver_price": null, "flash_sale_price_details": null}, "quantity": "2", "sku_quantity_with_combo": "2 pieces", "unit_of_measure": "pieces", "meta": {"short_description": "Energy-rich & best for curries or kofta", "groups": [], "coupon_less_offers": [], "sticker": "", "handpicked": false, "applicable_business_category": ""}, "tags": null, "inventory": {"in_stock": false, "total": 0, "remaining": 0, "message": null}, "max_allowed_quantity": 6, "cart_allowed_quantity": {"total": 0, "message": "That’s all we have in stock at the moment!"}, "weight_in_grams": 2, "volumetric_weight": 0, "alcohol_content_percentage": 0, "container_type": "", "sub_category_type": "", "dimensions": {"length_in_cm": 7, "width_in_cm": 7, "height_in_cm": 7, "volume_in_cc": 343}, "brand": "Fruits and Vegetables Category", "brand_id": "5a717015a4d30132dd6736e079d518d7eb56eade", "category": "Vegetables", "last_ordered_at": 0, "category_id": "acac61bb-9df1-4626-b24b-e967c8cba248", "sku_secondary_quantity": "", "store_id": "1403687", "display_variant": true, "sku_slot_info": {"message": null, "avail": true, "is_avail": true, "next_change_time": []}, "default": false, "product_name_without_brand": "Raw Banana (Kaccha Kela)", "sourcing_time": "", "sourced_from": "", "super_category": "Fruits and Vegetables", "sub_category_l3": "<PERSON>ana", "sub_category_l4": "Regular", "sub_category_l5": "Normal", "scm_item_type": "NORMAL", "filters_tag": null, "external_pharmacy_item": false, "rx_required": false, "variation_tags": null}], "inline_message": null, "analytics": {"objectValue": "Fresh Vegetables", "context": "{\"itemId\":\"FRQRYLSFBM\",\"slotAvailability\":true}", "impressionObjectName": "item-impression", "clickObjectName": "click-item", "extraFields": {}}, "sos_ads_position_data": null, "avail": true, "ad_item": false, "in_stock": false, "is_avail": true, "product_id": "472LWN102M", "product_name_without_brand": "Raw Banana (Kaccha Kela)"}], "type": "PRODUCT_LIST", "priority": 5, "widgetTimeSlotInfo": {"alwaysActive": true, "widgetSlotDayListMap": null}, "segments": []}], "storeDetails": {"id": "1403687", "name": "Instamart", "address": "Ground Floor, Unit No CPM 35-B1-BG35, CPM 36-B1-BG36,CPM37-B1-BG37,<PERSON><PERSON> ,Sector 105, Mohali Punjab - 140307", "description": "", "active": true, "priority": 2, "locality": "Emaar The Views", "categories": [{"id": 52, "name": "Instamart"}], "tags": [], "sources": [{"source": {"id": 6, "name": "Offline Sourced"}, "id_in_source": ""}], "badges": [], "maxAllowedItemsInCart": 200, "maxAllowedCartWeightInGrams": 15000, "maxAllowedCartVolumeInCc": 42875, "minimumOrderValueInRs": 0, "subCategoryTypeMaxAllowedWeights": {}, "discountInfo": {"couponLessDiscounts": [{"description": "", "short_description": "", "discount_type": "FREEBIE", "logo_id": "e0cf25488aee88d25b9df510a56809f2", "title": "Sample - Buy any Rakhi and get a Kalyan voucher worth Rs.2100", "sub_title": "Sample - Buy any Rakhi and get a Kalyan voucher worth Rs.2100", "tnc": ["Offer valid till stocks last", "Offer valid for a limited period only", "Other TnCs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-08-10T18:29:00.000+0000", "offer_id": "b091ac92-fec2-46bb-9bde-c412d052d384", "minimum_order_value": 1, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=b091ac92-fec2-46bb-9bde-c412d052d384"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Rakhi items worth ₹1 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Convenience fees waived off", "sub_title": "Convenience fees waived off", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 30, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "aee47903-2307-4b74-beab-a16579105c14", "minimum_order_value": 1, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery on orders above ₹49", "sub_title": "Free Delivery on orders above ₹49", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2029-06-29T18:29:00.000+0000", "offer_id": "83c5bc5d-a07f-4ed9-bcca-0b0027901226", "minimum_order_value": 49, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "4a075af1-5370-4254-9293-dd09a1466672", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "579334a7-e682-41ee-a0dd-85b4192651b2", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "d328b922-37bb-4799-b45a-5419f19ead3c", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "117a395a-3e28-40fd-9cda-eabceab2398e", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "136a4e3a-03d3-4b50-b74e-de97600c7482", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "31bcfef4-6193-4de8-aa90-8310636b07f6", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "4a36cace-6f82-4b3a-989e-a781d0df00be", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "7a984027-c27f-4f77-be40-ec44fa05b077", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8167cae1-e162-4ead-851a-00c9fe3a7e92", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8271d0c9-a4ef-44c3-8a20-3b3b5edf467b", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "8e4d1422-bcb9-4168-b6ec-4858e2626685", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8f6ff210-6733-49da-b49a-41e8d354c820", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "93894e04-d811-4ef8-b965-5aa1c7a82fa5", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "bf8aeb64-5a8f-4728-93af-71bb5aac64f3", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy Mars Products worth Rs.299/- and get 5% off", "sub_title": "Buy Mars Products worth Rs.299/- and get 5% off", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-07-29T18:29:00.000+0000", "offer_id": "663381d2-e287-48d1-a6af-b78c3f498044", "minimum_order_value": 299, "discount_value": 5, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=663381d2-e287-48d1-a6af-b78c3f498044"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add MARS items worth ₹299 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy products worth Rs.799 from WishCare and get additional 5% off", "sub_title": "Buy products worth Rs.799 from WishCare and get additional 5% off", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "9797d066-eac8-4c22-8326-498b7678e7df", "minimum_order_value": 799, "discount_value": 5, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=9797d066-eac8-4c22-8326-498b7678e7df"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Wishcare items worth ₹799 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy Productsworth Rs.499 from Coloressence and get 3% discount", "sub_title": "Buy Productsworth Rs.499 from Coloressence and get 3% discount", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-08-31T18:29:00.000+0000", "offer_id": "828e45a0-29b6-4837-bea6-318eb590d9f1", "minimum_order_value": 499, "discount_value": 3, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=828e45a0-29b6-4837-bea6-318eb590d9f1"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Coloressence items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_CATEGORY%20/Quench%20logo.webp?updatedAt=1751957849182", "title": "Get Rs.100 off on order above Rs.399 on Quench products", "sub_title": "Get Rs.100 off on order above Rs.399 on Quench products", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 100, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "674efb29-b555-43e7-87e2-2ce4e4b011f4", "minimum_order_value": 399, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=674efb29-b555-43e7-87e2-2ce4e4b011f4"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Wella Professionals items worth ₹399 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 99 off on minimum Basket Value - Rs.999", "sub_title": "Rs. 99 off on minimum Basket Value - Rs.999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "54e73b98-dbd4-44c5-a08c-e26ecc72da37", "minimum_order_value": 999, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=54e73b98-dbd4-44c5-a08c-e26ecc72da37"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Purina items worth ₹999 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Flat ₹99 on orders Above ₹999", "sub_title": "Flat ₹99 on orders Above ₹999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "65e5e1dc-22f0-4601-91f8-bd3c9c9a5907", "minimum_order_value": 99, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=65e5e1dc-22f0-4601-91f8-bd3c9c9a5907"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Brainsmith  items worth ₹99 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Flat ₹99 off on orders Above ₹999.", "sub_title": "Flat ₹99 off on orders Above ₹999.", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "9f476014-1ecb-4550-9c1f-cdf0e6322772", "minimum_order_value": 999, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=9f476014-1ecb-4550-9c1f-cdf0e6322772"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Bamboo Nature items worth ₹999 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 55 off on minimum Basket Value - Rs.599", "sub_title": "Rs. 55 off on minimum Basket Value - Rs.599", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 55, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "7fd7af88-457e-4216-a1c1-49259b13a1c8", "minimum_order_value": 599, "discount_value": 55, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=7fd7af88-457e-4216-a1c1-49259b13a1c8"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Petcrux items worth ₹599 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 50 off on minimum Basket Value - Rs.499", "sub_title": "Rs. 50 off on minimum Basket Value - Rs.499", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 50, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "c92082ac-f117-4eed-b7c0-7cb803e5187c", "minimum_order_value": 499, "discount_value": 50, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=c92082ac-f117-4eed-b7c0-7cb803e5187c"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Himalaya items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_Category/images%20(1).png?updatedAt=1751466553287", "title": "Rs. 20 off on minimum Basket Value - Rs.499", "sub_title": "Rs. 20 off on minimum Basket Value - Rs.499", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 20, "valid_till": "2025-07-31T07:45:52.000+0000", "offer_id": "8b4c6182-c2e4-4deb-b4ce-85a747434bb6", "minimum_order_value": 499, "discount_value": 20, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=8b4c6182-c2e4-4deb-b4ce-85a747434bb6"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Torrent Pharma items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}], "superOffer": false, "discounts": [{"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_Category/Edgewell-Personal-Care-Stayfree-Logo-_3x_2cc54d21-3bdf-4a6a-bb1b-b5cc08753eba_540x_wfhgsh%20(1).png?updatedAt=1750431171496", "title": "Buy product worth ₹499 and Get ₹49 Off on selected Stayfree products", "sub_title": "Use code STAYFREE49 & get Get ₹49 Off on orders above ₹499", "tnc": ["Valid only on selected Stayfree products", "The cart can have other items too but the discount will be valid only on Stayfree products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "STAYFREE49", "super_offer": false, "minimum_order_value": 49900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "IM_assets/philips.png", "title": "Flat 10% off on selected range of Philips Avent Items", "sub_title": "Flat 10% off on selected range of Philips Avent Items", "tnc": ["Valid only on Philips Avent Items", "The cart can have other items too but the discount will be valid only on Philips Avent Items", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "AVENT10", "super_offer": false, "minimum_order_value": 100, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Get Flat 10% off on sunfeast Biscuits & cakes  worth Rs.149 or above", "sub_title": "Get Flat 10% off on sunfeast Biscuits & cakes  worth Rs.149 or above", "tnc": ["Valid only on Sunfeast worth ₹149 or more", "The cart can have other items too but the discount will be valid only on The Sunfeast products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "ITC10", "super_offer": false, "minimum_order_value": 14900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/MP.png?updatedAt=1748682263320", "title": "Buy product worth ₹999 and Get ₹110 Off on selected Mamypoko products", "sub_title": "Use code HAPPY110 & Get ₹110 Off on orders above ₹999 ", "tnc": ["Valid only on Mamypoko products", "The cart can have other items too but the discount will be valid only on Mamypoko products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "HAPPY110", "super_offer": false, "minimum_order_value": 99900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/Pampers.JPG?updatedAt=1748682462220", "title": "Get Flat ₹99 OFF on purchase of select range of baby care products worth ₹999 & above", "sub_title": "Use code PAMPERS99 & get Flat ₹99 OFF on orders above ₹999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "PAMPERS99", "super_offer": false, "minimum_order_value": 99900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/bumtum.png?updatedAt=1748681661079", "title": "Buy product worth ₹699 and Get ₹69 Off on Bumtum Items", "sub_title": "Use code BUMTUM69 & Get ₹69 Off on orders above ₹699", "tnc": ["Valid only on Bumtum Items", "The cart can have other items too but the discount will be valid only on Bumtum Items", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "BUMTUM69", "super_offer": false, "minimum_order_value": 69900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/himalayalogo.png?updatedAt=1748682315961", "title": "Buy product worth ₹500 and Get ₹50 Off on selected Himalaya Products", "sub_title": "Use code HIMALAYA50 & Get ₹50 Off on orders above ₹500", "tnc": ["Valid only on Himalaya Products", "The cart can have other items too but the discount will be valid only on Himalaya Products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "HIMALAYA50", "super_offer": false, "minimum_order_value": 50000, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/<PERSON>_baby.jpg?updatedAt=1748682409764", "title": "Buy product worth ₹499 and Get ₹49 Off on Johnson & Johnson Products", "sub_title": "Use code JJ49 & get Get ₹49 Off on orders above ₹499", "tnc": ["Valid only on Johnson & Johnson Products", "The cart can have other items too but the discount will be valid only on Johnson & Johnson Products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "JJ49", "super_offer": false, "minimum_order_value": 49900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}]}, "metadata": {"unstructured_enabled": false, "user_verification_required": false, "guaranteed_delivery": false}, "lastOrderTimestamp": 0, "inStockItems": 0, "deliverySlots": [], "invoiceDetails": {"invoice_entity_name": ""}, "hasPharmacy": false, "pharmacyEnabled": false, "partner": true, "sellerName": "", "sellerFssaiAuthorisedAddress": "Gf, Unit No CPM 35-B1-BG35,<PERSON><PERSON> ,Sector 105, Mohali Punjab - 140307", "sellerFssaiNumber": "20250326107137030", "slottedDeliveryAvailable": false, "unstructuredEnabled": false, "lat_long": "30.6566956,76.6821416", "phone_numbers": "**********", "is_verified": true, "is_partner": true, "image_id": "", "product_categories": [], "store_chain": {"id": 229, "name": "Instamart"}, "store_document": {"fssai_license_no": "", "drug_license_no": "", "gstin_number": "03AAFCJ5741H1ZQ", "liquor_license_no": "", "fssai_reference_no": "20250326107137030", "fssai_registered_business_entity_name": "Jaldi Technologies Pvt Ltd", "fssai_authorised_premises_address": "Gf, Unit No CPM 35-B1-BG35,<PERSON><PERSON> ,Sector 105, Mohali Punjab - 140307"}, "popular_category": {"id": 0, "name": ""}}, "requestId": "38d7e50c-8d4e-4f80-9f15-381536e0d5d0", "serviceLine": "INSTAMART", "storesDetails": [{"id": "1403687", "name": "Instamart", "storeConstraints": {"maxAllowedItemsInCart": 200, "maxAllowedCartWeightInGrams": 15000, "maxAllowedCartVolumeInCc": 42875}, "discountInfo": {"couponLessDiscounts": [{"description": "", "short_description": "", "discount_type": "FREEBIE", "logo_id": "e0cf25488aee88d25b9df510a56809f2", "title": "Sample - Buy any Rakhi and get a Kalyan voucher worth Rs.2100", "sub_title": "Sample - Buy any Rakhi and get a Kalyan voucher worth Rs.2100", "tnc": ["Offer valid till stocks last", "Offer valid for a limited period only", "Other TnCs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-08-10T18:29:00.000+0000", "offer_id": "b091ac92-fec2-46bb-9bde-c412d052d384", "minimum_order_value": 1, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=b091ac92-fec2-46bb-9bde-c412d052d384"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Rakhi items worth ₹1 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Convenience fees waived off", "sub_title": "Convenience fees waived off", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 30, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "aee47903-2307-4b74-beab-a16579105c14", "minimum_order_value": 1, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery on orders above ₹49", "sub_title": "Free Delivery on orders above ₹49", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2029-06-29T18:29:00.000+0000", "offer_id": "83c5bc5d-a07f-4ed9-bcca-0b0027901226", "minimum_order_value": 49, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "4a075af1-5370-4254-9293-dd09a1466672", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "579334a7-e682-41ee-a0dd-85b4192651b2", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Free Delivery above ₹499", "sub_title": "Get free delivery on orders above ₹499", "tnc": ["Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 199, "valid_till": "2025-12-30T18:29:00.000+0000", "offer_id": "d328b922-37bb-4799-b45a-5419f19ead3c", "minimum_order_value": 499, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "117a395a-3e28-40fd-9cda-eabceab2398e", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "136a4e3a-03d3-4b50-b74e-de97600c7482", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "31bcfef4-6193-4de8-aa90-8310636b07f6", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "4a36cace-6f82-4b3a-989e-a781d0df00be", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "7a984027-c27f-4f77-be40-ec44fa05b077", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8167cae1-e162-4ead-851a-00c9fe3a7e92", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8271d0c9-a4ef-44c3-8a20-3b3b5edf467b", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T17:30:00.000+0000", "offer_id": "8e4d1422-bcb9-4168-b6ec-4858e2626685", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "8f6ff210-6733-49da-b49a-41e8d354c820", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "93894e04-d811-4ef8-b965-5aa1c7a82fa5", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "INVALID", "logo_id": "", "title": "", "sub_title": "", "tnc": [""], "coupon_code": "", "super_offer": false, "discount_cap": 0, "valid_till": "2025-07-29T09:30:00.000+0000", "offer_id": "bf8aeb64-5a8f-4728-93af-71bb5aac64f3", "minimum_order_value": 399, "discount_value": 0, "nudge": {"message": "", "logoId": "", "threshold": 0, "frequency": 35, "timeout": 10000, "cta": {"text": "", "link": ""}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy Mars Products worth Rs.299/- and get 5% off", "sub_title": "Buy Mars Products worth Rs.299/- and get 5% off", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-07-29T18:29:00.000+0000", "offer_id": "663381d2-e287-48d1-a6af-b78c3f498044", "minimum_order_value": 299, "discount_value": 5, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=663381d2-e287-48d1-a6af-b78c3f498044"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add MARS items worth ₹299 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy products worth Rs.799 from WishCare and get additional 5% off", "sub_title": "Buy products worth Rs.799 from WishCare and get additional 5% off", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "9797d066-eac8-4c22-8326-498b7678e7df", "minimum_order_value": 799, "discount_value": 5, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=9797d066-eac8-4c22-8326-498b7678e7df"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Wishcare items worth ₹799 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Buy Productsworth Rs.499 from Coloressence and get 3% discount", "sub_title": "Buy Productsworth Rs.499 from Coloressence and get 3% discount", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 1000, "valid_till": "2025-08-31T18:29:00.000+0000", "offer_id": "828e45a0-29b6-4837-bea6-318eb590d9f1", "minimum_order_value": 499, "discount_value": 3, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=828e45a0-29b6-4837-bea6-318eb590d9f1"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Coloressence items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_CATEGORY%20/Quench%20logo.webp?updatedAt=1751957849182", "title": "Get Rs.100 off on order above Rs.399 on Quench products", "sub_title": "Get Rs.100 off on order above Rs.399 on Quench products", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 100, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "674efb29-b555-43e7-87e2-2ce4e4b011f4", "minimum_order_value": 399, "discount_value": 100, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=674efb29-b555-43e7-87e2-2ce4e4b011f4"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Wella Professionals items worth ₹399 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 99 off on minimum Basket Value - Rs.999", "sub_title": "Rs. 99 off on minimum Basket Value - Rs.999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "54e73b98-dbd4-44c5-a08c-e26ecc72da37", "minimum_order_value": 999, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=54e73b98-dbd4-44c5-a08c-e26ecc72da37"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Purina items worth ₹999 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Flat ₹99 on orders Above ₹999", "sub_title": "Flat ₹99 on orders Above ₹999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "65e5e1dc-22f0-4601-91f8-bd3c9c9a5907", "minimum_order_value": 99, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=65e5e1dc-22f0-4601-91f8-bd3c9c9a5907"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Brainsmith  items worth ₹99 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Flat ₹99 off on orders Above ₹999.", "sub_title": "Flat ₹99 off on orders Above ₹999.", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 99, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "9f476014-1ecb-4550-9c1f-cdf0e6322772", "minimum_order_value": 999, "discount_value": 99, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=9f476014-1ecb-4550-9c1f-cdf0e6322772"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Bamboo Nature items worth ₹999 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 55 off on minimum Basket Value - Rs.599", "sub_title": "Rs. 55 off on minimum Basket Value - Rs.599", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 55, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "7fd7af88-457e-4216-a1c1-49259b13a1c8", "minimum_order_value": 599, "discount_value": 55, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=7fd7af88-457e-4216-a1c1-49259b13a1c8"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Petcrux items worth ₹599 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Rs. 50 off on minimum Basket Value - Rs.499", "sub_title": "Rs. 50 off on minimum Basket Value - Rs.499", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 50, "valid_till": "2025-07-31T18:29:00.000+0000", "offer_id": "c92082ac-f117-4eed-b7c0-7cb803e5187c", "minimum_order_value": 499, "discount_value": 50, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=c92082ac-f117-4eed-b7c0-7cb803e5187c"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Himalaya items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_Category/images%20(1).png?updatedAt=1751466553287", "title": "Rs. 20 off on minimum Basket Value - Rs.499", "sub_title": "Rs. 20 off on minimum Basket Value - Rs.499", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "", "super_offer": false, "discount_cap": 20, "valid_till": "2025-07-31T07:45:52.000+0000", "offer_id": "8b4c6182-c2e4-4deb-b4ce-85a747434bb6", "minimum_order_value": 499, "discount_value": 20, "nudge": {"message": "", "logoId": "", "threshold": 50, "frequency": 35, "timeout": 10000, "cta": {"text": "View Items", "link": "/instamart/collection-listing?collectionId=93461&disable_view_offer=true&offerId=8b4c6182-c2e4-4deb-b4ce-85a747434bb6"}}, "success_dialog": {"title": "", "message": "", "header": ""}, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "inclusive_offer_tag": {"text": "View Offer", "avail_msg": "Add Torrent Pharma items worth ₹499 more to avail this offer", "pre_mov_text": "Offer will be auto-applied on the bill", "post_mov_text": "Offer auto-applied on the bill"}, "discount_on_type": "CART"}], "superOffer": false, "discounts": [{"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "IM_Category/Edgewell-Personal-Care-Stayfree-Logo-_3x_2cc54d21-3bdf-4a6a-bb1b-b5cc08753eba_540x_wfhgsh%20(1).png?updatedAt=1750431171496", "title": "Buy product worth ₹499 and Get ₹49 Off on selected Stayfree products", "sub_title": "Use code STAYFREE49 & get Get ₹49 Off on orders above ₹499", "tnc": ["Valid only on selected Stayfree products", "The cart can have other items too but the discount will be valid only on Stayfree products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "STAYFREE49", "super_offer": false, "minimum_order_value": 49900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "IM_assets/philips.png", "title": "Flat 10% off on selected range of Philips Avent Items", "sub_title": "Flat 10% off on selected range of Philips Avent Items", "tnc": ["Valid only on Philips Avent Items", "The cart can have other items too but the discount will be valid only on Philips Avent Items", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "AVENT10", "super_offer": false, "minimum_order_value": 100, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "PERCENT", "logo_id": "InstamartAssets/Swiggy%20New%20(400%20x%20400)%20Logo.png", "title": "Get Flat 10% off on sunfeast Biscuits & cakes  worth Rs.149 or above", "sub_title": "Get Flat 10% off on sunfeast Biscuits & cakes  worth Rs.149 or above", "tnc": ["Valid only on Sunfeast worth ₹149 or more", "The cart can have other items too but the discount will be valid only on The Sunfeast products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "ITC10", "super_offer": false, "minimum_order_value": 14900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/MP.png?updatedAt=1748682263320", "title": "Buy product worth ₹999 and Get ₹110 Off on selected Mamypoko products", "sub_title": "Use code HAPPY110 & Get ₹110 Off on orders above ₹999 ", "tnc": ["Valid only on Mamypoko products", "The cart can have other items too but the discount will be valid only on Mamypoko products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "HAPPY110", "super_offer": false, "minimum_order_value": 99900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/Pampers.JPG?updatedAt=1748682462220", "title": "Get Flat ₹99 OFF on purchase of select range of baby care products worth ₹999 & above", "sub_title": "Use code PAMPERS99 & get Flat ₹99 OFF on orders above ₹999", "tnc": ["Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "PAMPERS99", "super_offer": false, "minimum_order_value": 99900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/bumtum.png?updatedAt=1748681661079", "title": "Buy product worth ₹699 and Get ₹69 Off on Bumtum Items", "sub_title": "Use code BUMTUM69 & Get ₹69 Off on orders above ₹699", "tnc": ["Valid only on Bumtum Items", "The cart can have other items too but the discount will be valid only on Bumtum Items", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "BUMTUM69", "super_offer": false, "minimum_order_value": 69900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/himalayalogo.png?updatedAt=1748682315961", "title": "Buy product worth ₹500 and Get ₹50 Off on selected Himalaya Products", "sub_title": "Use code HIMALAYA50 & Get ₹50 Off on orders above ₹500", "tnc": ["Valid only on Himalaya Products", "The cart can have other items too but the discount will be valid only on Himalaya Products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "HIMALAYA50", "super_offer": false, "minimum_order_value": 50000, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}, {"description": "", "short_description": "", "discount_type": "FLAT", "logo_id": "merch_bau/<PERSON>_baby.jpg?updatedAt=1748682409764", "title": "Buy product worth ₹499 and Get ₹49 Off on Johnson & Johnson Products", "sub_title": "Use code JJ49 & get Get ₹49 Off on orders above ₹499", "tnc": ["Valid only on Johnson & Johnson Products", "The cart can have other items too but the discount will be valid only on Johnson & Johnson Products", "Limited period offer", "Offer valid once per user", "Valid only on Instamart", "Other T&Cs may apply"], "coupon_code": "JJ49", "super_offer": false, "minimum_order_value": 49900, "prev_mov_text": "", "post_mov_text": "", "groups_info": {"exclusionGroupsInfo": {}, "message": ""}, "pre_display_threshold_text": "", "discount_on_type": "CART"}]}, "priority": "PRIORITY_PRIMARY"}]}, "statusCode": 0}