#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Generating frontend package-lock.json...');

const frontendDir = path.join(__dirname, 'frontend');

// Check if frontend directory exists
if (!fs.existsSync(frontendDir)) {
  console.error('❌ Frontend directory not found');
  process.exit(1);
}

// Check if package.json exists
const packageJsonPath = path.join(frontendDir, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Frontend package.json not found');
  process.exit(1);
}

try {
  // Change to frontend directory and run npm install
  process.chdir(frontendDir);
  
  // Remove existing node_modules and package-lock.json
  if (fs.existsSync('node_modules')) {
    console.log('🧹 Removing existing node_modules...');
    execSync('rmdir /s /q node_modules', { stdio: 'inherit' });
  }
  
  if (fs.existsSync('package-lock.json')) {
    console.log('🧹 Removing existing package-lock.json...');
    fs.unlinkSync('package-lock.json');
  }
  
  // Install dependencies to generate package-lock.json
  console.log('📦 Installing frontend dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  
  // Check if package-lock.json was created
  if (fs.existsSync('package-lock.json')) {
    console.log('✅ Frontend package-lock.json generated successfully!');
  } else {
    console.error('❌ Failed to generate package-lock.json');
    process.exit(1);
  }
  
} catch (error) {
  console.error('❌ Error generating frontend package-lock.json:', error.message);
  process.exit(1);
}
