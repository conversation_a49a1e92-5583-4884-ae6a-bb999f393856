const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const DatabaseSingleton = require('../dbSingleton');
const { runCleanup } = require('../logCleanup');
const { deriveInStockFromProduct } = require('../src/services/api/utils/stockUtils');
// Start the Telegram bot within the same process so cron jobs run in this server
let startTelegramBot = null;
try {
  ({ startBot: startTelegramBot } = require('../telegramBot'));
} catch (_) {
  startTelegramBot = null;
}

const app = express();
const PORT = process.env.PORT || process.env.API_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Serve static files from React build in production
if (process.env.NODE_ENV === 'production' || process.env.RAILWAY_ENVIRONMENT) {
  const buildPath = path.join(__dirname, '..', 'frontend', 'build');
  if (fs.existsSync(buildPath)) {
    app.use(express.static(buildPath));
    console.log('✅ Serving React frontend from:', buildPath);
  }
}

// Ensure logs directory exists
const logsDir = path.join(__dirname, '..', 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Initialize database before starting server
async function initializeDatabase() {
  try {
    await DatabaseSingleton.initialize();
    console.log('✅ Database singleton initialized successfully');
  } catch (error) {
    console.error('❌ Database singleton initialization failed:', error.message);
  }
}

/**
 * Normalize product data to ensure consistent property names
 */
function normalizeProductData(product) {
  return {
    ...product,
    // Ensure both url and productUrl are available for compatibility
    productUrl: product.url || product.productUrl,
    url: product.url || product.productUrl,
    // Normalize other common properties
    originalPrice: product.originalPrice || product.storePrice,
    storePrice: product.storePrice || product.originalPrice,
    // Ensure image is available
    image: product.images && product.images.length > 0 ? product.images[0] : null,
    // Ensure stock status
    inStock: product.inStock !== undefined ? product.inStock : deriveInStockFromProduct(product)
  };
}

// Override console.log to add IST timestamp (same as main app)
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

function getISTTimestamp() {
  const now = new Date();
  const istTime = new Date(now.getTime() + (5.5 * 60 * 60 * 1000)); // UTC + 5:30
  return istTime.toISOString().substr(11, 8); // Extract HH:MM:SS
}

console.log = function(...args) {
  originalConsoleLog(`[${getISTTimestamp()}]`, ...args);
};

console.error = function(...args) {
  originalConsoleError(`[${getISTTimestamp()}]`, ...args);
};

/**
 * Get all cron jobs with their current status
 */
app.get('/api/cron-jobs', async (req, res) => {
  try {
    const cronPersistence = await DatabaseSingleton.getInstance();
    const jobs = await cronPersistence.loadCronJobs();

    // Import the function to check active jobs
    const { getActiveCronJobs } = require('../telegramBot');
    const activeCronJobs = getActiveCronJobs();

    // Add additional computed fields
    const enrichedJobs = jobs.map(job => {
      const isActive = activeCronJobs.has(job.jobId);
      const now = Date.now();
      const lastRun = job.lastRun;

      // Determine actual status
      let status = 'stopped';
      let nextRun = 'Not scheduled';

      if (isActive) {
        // Check if job is overdue
        if (lastRun && job.intervalMinutes) {
          const intervalMs = job.intervalMinutes * 60 * 1000;
          const expectedNextRun = lastRun + intervalMs;

          if (now > expectedNextRun + (5 * 60 * 1000)) { // 5 minute grace period
            status = 'overdue';
            nextRun = 'Overdue';
          } else {
            status = 'running';
            nextRun = calculateNextRun(job);
          }
        } else {
          status = 'running';
          nextRun = calculateNextRun(job);
        }
      }

      return {
        ...job,
        status,
        nextRun,
        runningFor: Math.floor((now - job.startTime) / 1000 / 60), // minutes
        successRate: job.executionCount > 0 ?
          Math.round(((job.executionCount - (job.failedExecutions || 0)) / job.executionCount) * 100) : 0,
        isActive
      };
    });
    
    res.json({
      success: true,
      data: enrichedJobs,
      total: enrichedJobs.length
    });
  } catch (error) {
    console.error('Error fetching cron jobs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch cron jobs'
    });
  }
});

/**
 * Get cron job execution history from database
 */
app.get('/api/cron-jobs/:jobId/executions', async (req, res) => {
  try {
    const { jobId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    const cronPersistence = await DatabaseSingleton.getInstance();
    const result = await cronPersistence.getCronExecutionRuns(jobId, parseInt(limit), parseInt(offset));

    res.json({
      success: true,
      data: result.runs,
      total: result.total,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: result.total > parseInt(offset) + parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error getting cron execution history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get execution history'
    });
  }
});

/**
 * Get specific execution results (items found)
 */
app.get('/api/cron-jobs/executions/:executionId/results', async (req, res) => {
  try {
    const { executionId } = req.params;
    const { limit = 100, offset = 0 } = req.query;

    const cronPersistence = await DatabaseSingleton.getInstance();
    const result = await cronPersistence.getCronExecutionResults(executionId, parseInt(limit), parseInt(offset));

    // Map DB rows to UI-friendly objects, keeping is_new flag
    const items = result.results.map(row => ({
      id: row.product_id,
      name: row.item_name,
      variation: row.variation_name,
      storePrice: row.store_price,
      offerPrice: row.offer_price,
      discountPercentage: row.discount_percentage,
      quantity: row.quantity,
      unit: row.unit,
      url: row.item_url,
      appliedThreshold: row.applied_threshold,
      isNew: row.is_new === true
    }));

    res.json({
      success: true,
      data: items,
      total: result.total,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: result.total > parseInt(offset) + parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Error getting execution results:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get execution results'
    });
  }
});

/**
 * Get cron job execution history and results (legacy file-based)
 */
app.get('/api/cron-jobs/:jobId/results', async (req, res) => {
  try {
    const { jobId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    // Get results from logs directory
    const logsDir = path.join(__dirname, '..', 'logs');

    // Check if logs directory exists, create if it doesn't
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    // Extract codes from jobId for filtering
    const jobParts = jobId.split('_');
    const codesPart = jobParts.length > 1 ? jobParts[1] : '';

    const logFiles = fs.readdirSync(logsDir)
      .filter(file => {
        // Match files that start with the codes part and end with .json
        return file.startsWith(codesPart) && file.endsWith('.json');
      })
      .sort((a, b) => {
        const timeA = extractTimestamp(a);
        const timeB = extractTimestamp(b);
        return timeB - timeA; // Most recent first
      })
      .slice(offset, offset + parseInt(limit));
    
    const results = [];

    // If no log files found, return empty results
    if (logFiles.length === 0) {
      return res.json({
        success: true,
        data: [],
        total: 0,
        hasMore: false
      });
    }

    for (const file of logFiles) {
      try {
        const filePath = path.join(logsDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));

        // Handle new execution log format
        if (data.jobId && data.timestamp) {
          results.push({
            timestamp: data.timestamp,
            jobId: data.jobId,
            codes: data.codes || [],
            threshold: data.threshold,
            totalItems: data.totalItems || 0,
            newItems: data.newItems || 0,
            skippedItems: data.skippedItems || 0,
            keywordFilteredItems: data.keywordFilteredItems || 0,
            success: data.success,
            error: data.error,
            products: data.items || [],
            productsFound: (data.items || []).length,
            executionId: data.executionId
          });
        }
        // Handle old log format for backward compatibility
        else if (data.response && data.response.data && data.response.data.widgets) {
          const products = extractProductsFromResponse(data.response);
          results.push({
            timestamp: data.metadata.timestamp,
            categoryName: data.metadata.categoryName,
            filterName: data.metadata.filterName,
            productsFound: products.length,
            products: products,
            success: true,
            error: null
          });
        }
      } catch (error) {
        console.error(`Error reading log file ${file}:`, error);
      }
    }
    
    res.json({
      success: true,
      data: results,
      total: results.length,
      hasMore: logFiles.length === parseInt(limit)
    });
  } catch (error) {
    console.error('Error fetching cron job results:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch cron job results'
    });
  }
});

/**
 * Get detailed product information with price history
 */
app.get('/api/products/:productId/history', async (req, res) => {
  try {
    const { productId } = req.params;
    const logsDir = path.join(__dirname, '..', 'logs');
    
    // Search through all log files for this product
    const logFiles = fs.readdirSync(logsDir)
      .filter(file => file.endsWith('.json'))
      .sort((a, b) => {
        const timeA = extractTimestamp(a);
        const timeB = extractTimestamp(b);
        return timeB - timeA;
      });
    
    const priceHistory = [];
    let productDetails = null;
    
    for (const file of logFiles) {
      try {
        const filePath = path.join(logsDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        
        if (data.response && data.response.data && data.response.data.widgets) {
          const products = extractProductsFromResponse(data.response);
          const product = products.find(p => p.id === productId);
          
          if (product) {
            if (!productDetails) {
              productDetails = normalizeProductData(product);
            }
            
            priceHistory.push({
              timestamp: data.metadata.timestamp,
              price: product.offerPrice,
              originalPrice: product.originalPrice,
              discountPercentage: product.discountPercentage,
              inStock: product.inStock
            });
          }
        }
      } catch (error) {
        console.error(`Error reading log file ${file}:`, error);
      }
    }
    
    // Get last 5 distinct prices
    const distinctPrices = [];
    const seenPrices = new Set();
    
    for (const entry of priceHistory) {
      if (!seenPrices.has(entry.price) && distinctPrices.length < 5) {
        distinctPrices.push(entry);
        seenPrices.add(entry.price);
      }
    }
    
    res.json({
      success: true,
      data: {
        product: productDetails,
        priceHistory: distinctPrices,
        totalHistoryEntries: priceHistory.length
      }
    });
  } catch (error) {
    console.error('Error fetching product history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch product history'
    });
  }
});

/**
 * Manually trigger a cron job
 */
app.post('/api/cron-jobs/:jobId/trigger', async (req, res) => {
  try {
    const { jobId } = req.params;

    // Import the executeCronJob function from telegramBot.js
    const { executeCronJob, executeCronJobById, getActiveCronJobs } = require('../telegramBot');

    // Check if job exists in active jobs
    const activeCronJobs = getActiveCronJobs();
    if (activeCronJobs.has(jobId)) {
      // Trigger directly if active
      console.log(`🔄 Manually triggering active cron job: ${jobId}`);
      await executeCronJob(jobId);
    } else {
      // Attempt one-off execution from DB if not active
      console.log(`🔄 Manually triggering non-active cron job from DB: ${jobId}`);
      const ok = await executeCronJobById(jobId);
      if (!ok) {
        return res.status(404).json({
          success: false,
          error: 'Cron job not found'
        });
      }
    }

    res.json({
      success: true,
      message: 'Cron job triggered successfully'
    });
  } catch (error) {
    console.error('Error triggering cron job:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to trigger cron job'
    });
  }
});

/**
 * Get bot status and active jobs count
 */
app.get('/api/bot/status', async (req, res) => {
  try {
    const { isBotRunning, getActiveCronJobs } = require('../telegramBot');
    const activeCronJobs = getActiveCronJobs();

    res.json({
      success: true,
      data: {
        botRunning: isBotRunning(),
        activeJobsCount: activeCronJobs.size,
        activeJobIds: Array.from(activeCronJobs.keys())
      }
    });
  } catch (error) {
    console.error('Error getting bot status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get bot status'
    });
  }
});

/**
 * Reload cron jobs from database (restart stuck jobs)
 */
app.post('/api/bot/reload-jobs', async (req, res) => {
  try {
    // This would require adding a reload function to telegramBot.js
    // For now, return a message about manual restart
    res.json({
      success: true,
      message: 'To reload jobs, restart the bot process. This will reload all jobs from the database.'
    });
  } catch (error) {
    console.error('Error reloading jobs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reload jobs'
    });
  }
});

/**
 * Debug endpoint to check job status in detail
 */
app.get('/api/debug/jobs/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    const { getActiveCronJobs, getUserCronJobsExternal } = require('../telegramBot');
    const cronPersistence = await DatabaseSingleton.getInstance();

    // Get job from database
    const dbJobs = await cronPersistence.loadCronJobs();
    const dbJob = dbJobs.find(j => j.jobId === jobId);

    // Get job from active memory
    const activeCronJobs = getActiveCronJobs();
    const activeJob = activeCronJobs.get(jobId);

    // Get user jobs
    const userJobs = dbJob ? getUserCronJobsExternal(dbJob.chatId) : [];

    res.json({
      success: true,
      data: {
        jobId,
        inDatabase: !!dbJob,
        inActiveMemory: !!activeJob,
        dbJob: dbJob || null,
        activeJob: activeJob ? {
          jobId: activeJob.jobId,
          chatId: activeJob.chatId,
          codes: activeJob.codes,
          intervalMinutes: activeJob.intervalMinutes,
          lastRun: activeJob.lastRun,
          executionCount: activeJob.executionCount,
          hasTask: !!activeJob.task,
          taskRunning: activeJob.task ? activeJob.task.running : false
        } : null,
        userJobsCount: userJobs.length,
        lastRunFormatted: dbJob && dbJob.lastRun ? new Date(dbJob.lastRun).toISOString() : null,
        timeSinceLastRun: dbJob && dbJob.lastRun ? Math.floor((Date.now() - dbJob.lastRun) / 1000 / 60) : null
      }
    });
  } catch (error) {
    console.error('Error debugging job:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to debug job'
    });
  }
});

/**
 * Create a new cron job
 */
app.post('/api/cron-jobs', async (req, res) => {
  try {
    let { codes, scheduleType, intervalMinutes, scheduleTime, threshold, chatId, apiType, priceCalculation, hideRepeatedItems } = req.body;

    // Handle codes as string or array
    if (typeof codes === 'string') {
      codes = codes.split(',').map(code => code.trim().toUpperCase()).filter(code => code.length > 0);
    } else if (Array.isArray(codes)) {
      codes = codes.map(code => code.trim().toUpperCase()).filter(code => code.length > 0);
    }

    // Validate required fields
    if (!codes || codes.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'At least one code is required'
      });
    }

    if (!threshold || threshold < 10 || threshold > 95) {
      return res.status(400).json({
        success: false,
        error: 'Threshold must be between 10 and 95'
      });
    }

    if (scheduleType === 'interval' && (!intervalMinutes || intervalMinutes < 5 || intervalMinutes > 1440)) {
      return res.status(400).json({
        success: false,
        error: 'Interval must be between 5 and 1440 minutes'
      });
    }

    if (scheduleType === 'time' && !scheduleTime) {
      return res.status(400).json({
        success: false,
        error: 'Schedule time is required for time-based jobs'
      });
    }

    // Import cron job creation function
    const { createCronJobFromAPI } = require('../telegramBot');

    const jobData = {
      codes,
      scheduleType: scheduleType || 'interval',
      intervalMinutes,
      scheduleTime,
      threshold,
      chatId: chatId || 0, // Default chat ID for API-created jobs
      apiType: apiType || 'existing',
      priceCalculation: priceCalculation || 'offer',
      hideRepeatedItems: hideRepeatedItems || false
    };

    const result = await createCronJobFromAPI(jobData);

    res.json({
      success: true,
      data: result,
      message: 'Cron job created successfully'
    });
  } catch (error) {
    console.error('Error creating cron job:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create cron job'
    });
  }
});

/**
 * Delete a cron job
 */
app.delete('/api/cron-jobs/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;

    // Import cron job deletion function
    const { deleteCronJobFromAPI } = require('../telegramBot');

    const result = await deleteCronJobFromAPI(jobId);

    if (result) {
      res.json({
        success: true,
        message: 'Cron job deleted successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Cron job not found'
      });
    }
  } catch (error) {
    console.error('Error deleting cron job:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete cron job'
    });
  }
});

/**
 * Get keyword thresholds for a user
 */
app.get('/api/keywords/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;
    const cronPersistence = await DatabaseSingleton.getInstance();

    const keywords = await cronPersistence.getKeywordThresholds(parseInt(chatId));

    res.json({
      success: true,
      data: keywords
    });
  } catch (error) {
    console.error('Error fetching keywords:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch keywords'
    });
  }
});

/**
 * Add or update keyword threshold
 */
app.post('/api/keywords/:chatId', async (req, res) => {
  try {
    const { chatId } = req.params;
    const { keyword, threshold } = req.body;

    if (!keyword || !threshold) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and threshold are required'
      });
    }

    if (threshold < 10 || threshold > 95) {
      return res.status(400).json({
        success: false,
        error: 'Threshold must be between 10 and 95'
      });
    }

    const cronPersistence = await DatabaseSingleton.getInstance();
    const success = await cronPersistence.setKeywordThreshold(parseInt(chatId), keyword, threshold);

    if (success) {
      res.json({
        success: true,
        message: 'Keyword threshold set successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to set keyword threshold'
      });
    }
  } catch (error) {
    console.error('Error setting keyword threshold:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to set keyword threshold'
    });
  }
});

/**
 * Delete keyword threshold
 */
app.delete('/api/keywords/:chatId/:keyword', async (req, res) => {
  try {
    const { chatId, keyword } = req.params;

    const cronPersistence = await DatabaseSingleton.getInstance();
    const success = await cronPersistence.deleteKeywordThreshold(parseInt(chatId), keyword);

    if (success) {
      res.json({
        success: true,
        message: 'Keyword threshold deleted successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Keyword threshold not found'
      });
    }
  } catch (error) {
    console.error('Error deleting keyword threshold:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete keyword threshold'
    });
  }
});

/**
 * Find deals by codes
 */
app.post('/api/deals/find', async (req, res) => {
  try {
    const { codes, threshold = 65, apiType = 'existing', priceCalculation = 'offer' } = req.body;

    if (!codes || !Array.isArray(codes) || codes.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Codes array is required'
      });
    }

    if (threshold < 10 || threshold > 95) {
      return res.status(400).json({
        success: false,
        error: 'Threshold must be between 10 and 95'
      });
    }

    const startTime = Date.now();

    // Load config
    const config = require('../config.json');

    let allItems = [];

    // Choose discovery engine
    const useEnhanced = apiType === 'new';
    const enhancedDealDiscovery = require('../enhancedDealDiscovery');
    const discountedItemsUtil = require('../discountedItemsUtil');

    // Process each code
    for (const code of codes) {
      const baseOptions = {
        code: code.trim().toUpperCase(),
        discountThreshold: threshold,
        useCache: config.api.useCache
      };

      let items;
      if (useEnhanced) {
        items = await enhancedDealDiscovery.getHighlyDiscountedItems({
          ...baseOptions,
          apiType: 'new',
          priceCalculation
        });
      } else {
        items = await discountedItemsUtil.getHighlyDiscountedItems(baseOptions);
      }

      allItems = allItems.concat(items);
    }

    // Remove duplicates based on item ID and normalize data
    const uniqueItems = [];
    const seenIds = new Set();
    for (const item of allItems) {
      if (!seenIds.has(item.id)) {
        seenIds.add(item.id);
        uniqueItems.push(normalizeProductData(item));
      }
    }

    // Sort by discount percentage (highest first)
    uniqueItems.sort((a, b) => parseFloat(b.discountPercentage) - parseFloat(a.discountPercentage));

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);

    // Save search history
    const searchData = {
      timestamp: new Date().toISOString(),
      codes,
      threshold,
      totalItems: allItems.length,
      uniqueItems: uniqueItems.length,
      duration: parseFloat(duration),
      source: 'dashboard',
      apiType,
      priceCalculation
    };

    // Save to search history file
    const historyDir = path.join(__dirname, '..', 'search_history');
    if (!fs.existsSync(historyDir)) {
      fs.mkdirSync(historyDir, { recursive: true });
    }

    const historyFile = path.join(historyDir, `search_${Date.now()}.json`);
    fs.writeFileSync(historyFile, JSON.stringify({
      ...searchData,
      items: uniqueItems.slice(0, 50) // Save top 50 items
    }, null, 2));

    res.json({
      success: true,
      data: {
        codes,
        threshold,
        totalItems: allItems.length,
        uniqueItems: uniqueItems.length,
        duration,
        items: uniqueItems,
        apiType,
        priceCalculation
      }
    });
  } catch (error) {
    console.error('Error finding deals:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to find deals'
    });
  }
});

/**
 * Stream find deals progress (SSE)
 */
app.get('/api/deals/find/stream', async (req, res) => {
  try {
    const codesParam = (req.query.codes || '').toString();
    const threshold = Math.max(10, Math.min(95, parseInt(req.query.threshold || '65', 10)));
    const apiType = (req.query.apiType || 'existing').toString();
    const priceCalculation = (req.query.priceCalculation || 'offer').toString();

    const codes = codesParam
      .split(',')
      .map(c => (c || '').trim().toUpperCase())
      .filter(c => c.length > 0);

    if (codes.length === 0) {
      res.status(400).json({ success: false, error: 'Codes query param is required' });
      return;
    }

    // Setup SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    const send = (event, data) => {
      try {
        res.write(`event: ${event}\n`);
        res.write(`data: ${JSON.stringify(data)}\n\n`);
      } catch (_) {
        // client likely disconnected
      }
    };

    // Keep-alive ping
    const keepAlive = setInterval(() => send('ping', { t: Date.now() }), 25000);
    req.on('close', () => clearInterval(keepAlive));

    const startTime = Date.now();
    send('log', { message: 'Starting deal discovery', codes, threshold, apiType, priceCalculation });

    // Choose discovery engine
    const useEnhanced = apiType === 'new';
    const enhancedDealDiscovery = require('../enhancedDealDiscovery');
    const discountedItemsUtil = require('../discountedItemsUtil');
    const config = require('../config.json');

    let allItems = [];

    for (let i = 0; i < codes.length; i++) {
      const code = codes[i];
      send('log', { message: `Processing code ${code} (${i + 1}/${codes.length})` });

      const baseOptions = {
        code,
        discountThreshold: threshold,
        useCache: config.api.useCache
      };

      try {
        const progressCallback = (evt) => {
          // Relay filter progress to client
          if (evt && evt.type === 'filtersTotal') {
            send('log', { message: `Found ${evt.total} filters for ${evt.categoryName}` });
          } else if (evt && evt.type === 'filterProgress') {
            send('log', { message: `Fetching filter ${evt.current}/${evt.total}: ${evt.filterName}` });
          } else if (evt && evt.type === 'filterCompleted') {
            send('log', { message: `Completed filter ${evt.filterName} (${evt.found} items)` });
          }
        };

        const items = useEnhanced
          ? await enhancedDealDiscovery.getHighlyDiscountedItems({
              ...baseOptions,
              apiType: 'new',
              priceCalculation
            }, progressCallback)
          : await discountedItemsUtil.getHighlyDiscountedItems(baseOptions, progressCallback);

        send('progress', { code, found: items.length });
        allItems = allItems.concat(items);
      } catch (err) {
        send('log', { message: `Error processing ${code}: ${err.message || err}`, level: 'error' });
      }
    }

    // Deduplicate and normalize
    const uniqueItems = [];
    const seenIds = new Set();
    for (const item of allItems) {
      if (!seenIds.has(item.id)) {
        seenIds.add(item.id);
        uniqueItems.push(normalizeProductData(item));
      }
    }

    uniqueItems.sort((a, b) => parseFloat(b.discountPercentage) - parseFloat(a.discountPercentage));

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);

    // Persist history like the non-streaming route
    try {
      const searchData = {
        timestamp: new Date().toISOString(),
        codes,
        threshold,
        totalItems: allItems.length,
        uniqueItems: uniqueItems.length,
        duration: parseFloat(duration),
        source: 'dashboard-stream',
        apiType,
        priceCalculation
      };

      const historyDir = path.join(__dirname, '..', 'search_history');
      if (!fs.existsSync(historyDir)) {
        fs.mkdirSync(historyDir, { recursive: true });
      }
      const historyFile = path.join(historyDir, `search_${Date.now()}_stream.json`);
      fs.writeFileSync(historyFile, JSON.stringify({ ...searchData, items: uniqueItems.slice(0, 50) }, null, 2));
    } catch (err) {
      // ignore persistence failures for streaming
    }

    send('done', {
      success: true,
      data: {
        codes,
        threshold,
        totalItems: allItems.length,
        uniqueItems: uniqueItems.length,
        duration,
        items: uniqueItems,
        apiType,
        priceCalculation
      }
    });

    clearInterval(keepAlive);
    res.end();
  } catch (error) {
    try {
      res.write(`event: error\n`);
      res.write(`data: ${JSON.stringify({ message: error.message })}\n\n`);
    } catch (_) {}
    res.end();
  }
});

/**
 * Get search history
 */
app.get('/api/deals/history', (req, res) => {
  try {
    const { limit = 20 } = req.query;

    const historyDir = path.join(__dirname, '..', 'search_history');

    if (!fs.existsSync(historyDir)) {
      return res.json({
        success: true,
        data: []
      });
    }

    const historyFiles = fs.readdirSync(historyDir)
      .filter(file => file.startsWith('search_') && file.endsWith('.json'))
      .sort((a, b) => {
        const timeA = parseInt(a.replace('search_', '').replace('.json', ''));
        const timeB = parseInt(b.replace('search_', '').replace('.json', ''));
        return timeB - timeA; // Most recent first
      })
      .slice(0, parseInt(limit));

    const history = [];

    for (const file of historyFiles) {
      try {
        const filePath = path.join(historyDir, file);
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        history.push(data);
      } catch (error) {
        console.error(`Error reading history file ${file}:`, error);
      }
    }

    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    console.error('Error fetching search history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch search history'
    });
  }
});

/**
 * Get available mapping codes
 */
app.get('/api/mappings', (req, res) => {
  try {
    const mappingUtil = require('../categoryFilterMappingUtil');
    const categoryNames = mappingUtil.getAllCategories();
    const metadata = mappingUtil.getMappingMetadata();

    // Transform categories into the expected format
    const categories = categoryNames.map(categoryName => {
      const categoryCode = mappingUtil.namesToCode(categoryName);
      const filters = mappingUtil.getFiltersForCategory(categoryName);

      const filterData = filters.map(filterName => {
        const filterCode = mappingUtil.namesToCode(categoryName, filterName);
        return {
          name: filterName,
          code: filterCode
        };
      });

      return {
        name: categoryName,
        code: categoryCode,
        filters: filterData
      };
    });

    res.json({
      success: true,
      data: {
        categories,
        metadata
      }
    });
  } catch (error) {
    console.error('Error fetching mappings:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch mappings'
    });
  }
});

/**
 * Get dashboard statistics
 */
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const cronPersistence = await DatabaseSingleton.getInstance();
    const jobs = await cronPersistence.loadCronJobs();
    
    const stats = {
      totalCronJobs: jobs.length,
      activeCronJobs: jobs.filter(job => job.executionCount > 0).length,
      totalExecutions: jobs.reduce((sum, job) => sum + (job.executionCount || 0), 0),
      totalItemsFound: jobs.reduce((sum, job) => sum + (job.totalItemsFound || 0), 0),
      averageSuccessRate: jobs.length > 0 ? 
        Math.round(jobs.reduce((sum, job) => {
          const rate = job.executionCount > 0 ? 
            ((job.executionCount - (job.failedExecutions || 0)) / job.executionCount) * 100 : 0;
          return sum + rate;
        }, 0) / jobs.length) : 0,
      recentActivity: jobs
        .filter(job => job.lastRun)
        .sort((a, b) => b.lastRun - a.lastRun)
        .slice(0, 5)
        .map(job => ({
          jobId: job.jobId,
          codes: job.codes,
          lastRun: job.lastRun,
          itemsFound: job.totalItemsFound || 0
        }))
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard statistics'
    });
  }
});

// Helper functions
function calculateNextRun(job) {
  if (!job.lastRun) return 'Starting soon...';

  const now = Date.now();
  const lastRun = job.lastRun;

  if (job.scheduleType === 'interval') {
    const intervalMs = job.intervalMinutes * 60 * 1000;
    const nextRun = lastRun + intervalMs;
    const timeRemaining = nextRun - now;

    // If overdue by more than 5 minutes, show as overdue
    if (timeRemaining < -5 * 60 * 1000) {
      const overdueMinutes = Math.floor(Math.abs(timeRemaining) / (60 * 1000));
      const overdueHours = Math.floor(overdueMinutes / 60);

      if (overdueHours > 0) {
        return `Overdue by ${overdueHours}h ${overdueMinutes % 60}m`;
      } else {
        return `Overdue by ${overdueMinutes}m`;
      }
    }

    if (timeRemaining <= 0) return 'Running now...';

    const minutes = Math.floor(timeRemaining / (60 * 1000));
    const seconds = Math.floor((timeRemaining % (60 * 1000)) / 1000);

    return minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
  } else if (job.scheduleType === 'time') {
    const [hours, minutes] = job.scheduleTime.split(':').map(Number);
    const now = new Date();
    const nextRun = new Date();
    nextRun.setHours(hours, minutes, 0, 0);

    if (nextRun <= now) {
      nextRun.setDate(nextRun.getDate() + 1);
    }

    const timeRemaining = nextRun.getTime() - now.getTime();
    const hoursRemaining = Math.floor(timeRemaining / (60 * 60 * 1000));
    const minutesRemaining = Math.floor((timeRemaining % (60 * 60 * 1000)) / (60 * 1000));

    return hoursRemaining > 0 ? `${hoursRemaining}h ${minutesRemaining}m` : `${minutesRemaining}m`;
  }

  return 'Unknown';
}

function extractTimestamp(filename) {
  const match = filename.match(/(\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.\d{3}Z)/);
  return match ? new Date(match[1].replace(/-/g, ':')).getTime() : 0;
}

function extractProductsFromResponse(response) {
  const products = [];
  
  if (response.data && response.data.widgets) {
    response.data.widgets.forEach(widget => {
      if (widget.data && widget.data.length) {
        widget.data.forEach(item => {
          if (item.display_type === 'ITEM') {
            const product = {
              id: item.id,
              name: item.display.name,
              image: item.display.image_id ? 
                `https://media-assets.swiggy.com/swiggy/image/upload/fl_lossy,f_auto,q_auto,w_300,h_300,c_fit/${item.display.image_id}` : null,
              offerPrice: parseFloat(item.pricing.price.offer_price) / 100,
              originalPrice: parseFloat(item.pricing.price.base_price) / 100,
              discountPercentage: Math.round(((parseFloat(item.pricing.price.base_price) - parseFloat(item.pricing.price.offer_price)) / parseFloat(item.pricing.price.base_price)) * 100),
              inStock: item.inventory.in_stock,
              productUrl: `https://www.swiggy.com/instamart/item/${item.id}`,
              brand: item.display.brand || 'Unknown',
              category: item.category || 'Unknown',
              unit: item.pricing.price.unit || 'piece'
            };
            products.push(product);
          }
        });
      }
    });
  }
  
  return products;
}

// Max Saver Gap Finder API endpoints
app.post('/api/max-saver-gap-finder/start', async (req, res) => {
  try {
    const { codes, categories, threshold, baseline, limit } = req.body;

    // Validate input - prefer codes over categories
    if ((!codes || !codes.trim()) && (!categories || !categories.trim())) {
      return res.status(400).json({ error: 'Either codes or categories are required' });
    }

    // Generate unique run ID
    const runId = `gap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Save initial "running" status to database
    const cronPersistence = await DatabaseSingleton.getInstance();
    // Determine total categories from provided input
    const codesCount = (codes && String(codes).trim())
      ? String(codes).split(',').map(s => s.trim()).filter(Boolean).length
      : 0;
    const categoriesCount = (categories && String(categories).trim())
      ? String(categories).split(',').map(s => s.trim()).filter(Boolean).length
      : 0;
    const totalCategories = codesCount > 0 ? codesCount : categoriesCount;

    const initialRunData = {
      runId,
      baseline: baseline || 'offer',
      threshold: parseFloat(threshold) || 10,
      limit: parseInt(limit) || 20,
      totalCategories,
      totalItemsFound: 0,
      executionTimeMs: 0,
      status: 'running',
      completedAt: null
    };

    await cronPersistence.saveMaxSaverGapRun(initialRunData);
    console.log(`💾 Saved initial run status for: ${runId}`);

    // Start the gap finder process asynchronously
    const { spawn } = require('child_process');
    const scriptPath = path.join(__dirname, '..', 'findMaxSaverGaps.js');

    // Build arguments based on input type
    const args = [];

    if (codes && codes.trim()) {
      // Use codes (preferred method)
      args.push('--codes', codes);
    } else if (categories && categories.trim()) {
      // Use categories (legacy method)
      args.push('--cases', categories.split(',').map(c => `${c.trim()}|`).join(','));
    }

    args.push(
      '--threshold', threshold?.toString() || '10',
      '--baseline', baseline || 'offer',
      '--limit', limit?.toString() || '20'
    );

    const child = spawn('node', [scriptPath, ...args], {
      detached: true,
      stdio: 'ignore'
    });

    child.unref();

    res.json({ success: true, runId });
  } catch (error) {
    console.error('Error starting gap finder:', error);
    res.status(500).json({ error: 'Failed to start gap finder' });
  }
});

app.get('/api/max-saver-gap-runs', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    const cronPersistence = await DatabaseSingleton.getInstance();
    const result = await cronPersistence.getMaxSaverGapRuns(limit, offset);
    res.json(result);
  } catch (error) {
    console.error('Error getting gap runs:', error);
    res.status(500).json({ error: 'Failed to get gap runs' });
  }
});

app.get('/api/max-saver-gap-runs/:runId/status', async (req, res) => {
  try {
    const { runId } = req.params;
    console.log(`🔍 Checking status for run ID: ${runId}`);

    const cronPersistence = await DatabaseSingleton.getInstance();
    const run = await cronPersistence.getMaxSaverGapRunById(runId);

    if (!run) {
      console.log(`❌ Run not found in database: ${runId}`);

      // Check if it's a recently started run that might not be in DB yet
      // Return a default "running" status for new runs
      if (runId.startsWith('gap_')) {
        const timestamp = runId.split('_')[1];
        const runTime = parseInt(timestamp);
        const now = Date.now();

        // If run was started less than 30 minutes ago, assume it's still running
        if (now - runTime < 30 * 60 * 1000) {
          console.log(`⏳ Assuming recent run is still running: ${runId}`);
          return res.json({
            status: 'running',
            runId,
            message: 'Run is in progress, not yet saved to database'
          });
        }
      }

      return res.status(404).json({ error: 'Run not found' });
    }

    console.log(`✅ Found run: ${runId}, status: ${run.status}`);

    res.json({
      status: run.status,
      runId: run.run_id,
      createdAt: run.created_at,
      completedAt: run.completed_at,
      totalItemsFound: run.total_items_found,
      executionTimeMs: run.execution_time_ms
    });
  } catch (error) {
    console.error('Error getting run status:', error);
    res.status(500).json({ error: 'Failed to get run status' });
  }
});

app.get('/api/max-saver-gap-runs/:runId/results', async (req, res) => {
  try {
    const { runId } = req.params;
    const limit = parseInt(req.query.limit) || 100;
    const offset = parseInt(req.query.offset) || 0;

    const cronPersistence = await DatabaseSingleton.getInstance();
    const result = await cronPersistence.getMaxSaverGapResults(runId, limit, offset);
    res.json(result);
  } catch (error) {
    console.error('Error getting gap results:', error);
    res.status(500).json({ error: 'Failed to get gap results' });
  }
});

app.get('/api/deal-finder-runs', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    const cronPersistence = await DatabaseSingleton.getInstance();
    const result = await cronPersistence.getDealFinderRuns(limit, offset);
    res.json(result);
  } catch (error) {
    console.error('Error getting deal finder runs:', error);
    res.status(500).json({ error: 'Failed to get deal finder runs' });
  }
});

app.get('/api/deal-finder-runs/:runId/results', async (req, res) => {
  try {
    const { runId } = req.params;
    const limit = parseInt(req.query.limit) || 100;
    const offset = parseInt(req.query.offset) || 0;

    const cronPersistence = await DatabaseSingleton.getInstance();
    const result = await cronPersistence.getDealFinderResults(runId, limit, offset);
    res.json(result);
  } catch (error) {
    console.error('Error getting deal finder results:', error);
    res.status(500).json({ error: 'Failed to get deal finder results' });
  }
});

/**
 * Aggregated products across sources (cron results, deal history, price gap)
 * Query params: q (search), sort (low|high|discount), limit, offset, sources (comma-separated)
 */
app.get('/api/products', async (req, res) => {
  try {
    const { q = '', sort = 'discount', limit = 100, offset = 0, sources = 'cron,history,pricegap' } = req.query;
    const searchQuery = String(q).toLowerCase();
    const sourceSet = new Set(String(sources).split(',').map(s => s.trim().toLowerCase()));

    const cronPersistence = await DatabaseSingleton.getInstance();

    let aggregated = [];
    let total = 0;

    // 1) Fetch latest cron execution items from DB
    if (sourceSet.has('cron')) {
      const cronRes = await cronPersistence.getLatestCronResults(1000, 0);
      const cronItems = (cronRes.results || []).map(row => ({
        id: row.product_id,
        name: row.item_name,
        variation: row.variation_name,
        storePrice: Number(row.store_price),
        offerPrice: Number(row.offer_price),
        discountPercentage: Number(row.discount_percentage),
        quantity: row.quantity,
        unit: row.unit,
        url: row.item_url,
        appliedThreshold: Number(row.applied_threshold),
        inStock: row.in_stock === true,
        source: 'cron',
        createdAt: row.created_at
      }));
      aggregated.push(...cronItems);
      total += cronRes.total || cronItems.length;
    }

    // 2) Fetch deal search history items from filesystem
    if (sourceSet.has('history')) {
      try {
        const historyDir = path.join(__dirname, '..', 'search_history');
        if (fs.existsSync(historyDir)) {
          const historyFiles = fs.readdirSync(historyDir)
            .filter(file => file.startsWith('search_') && file.endsWith('.json'))
            .sort((a, b) => parseInt(b.replace('search_', '').replace('.json', '')) - parseInt(a.replace('search_', '').replace('.json', '')))
            .slice(0, 50);
          for (const file of historyFiles) {
            try {
              const filePath = path.join(historyDir, file);
              const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
              const items = (data.items || []).map(item => normalizeProductData({ ...item, source: 'history' }));
              aggregated.push(...items);
            } catch (_) {}
          }
        }
      } catch (_) {}
    }

    // 3) Fetch latest Price Gap results from DB
    if (sourceSet.has('pricegap')) {
      const gapRes = await cronPersistence.getLatestMaxSaverGapResults(1000, 0);
      const gapItems = (gapRes.results || []).map(row => ({
        id: row.product_id,
        name: row.item_name,
        storePrice: Number(row.baseline_price),
        offerPrice: Number(row.max_saver_price),
        maxSaverPrice: Number(row.max_saver_price),
        discountPercentage: Number(row.gap_percentage),
        quantity: row.quantity,
        unit: row.unit,
        url: row.item_url,
        inStock: true,
        source: 'pricegap',
        createdAt: row.created_at
      }));
      aggregated.push(...gapItems);
      total += gapRes.total || gapItems.length;
    }

    // Deduplicate by product id + name + url combo if id missing
    const seenKeys = new Set();
    const deduped = [];
    for (const item of aggregated) {
      const key = String(item.id || '') + '|' + String(item.name || '') + '|' + String(item.url || '');
      if (!seenKeys.has(key)) {
        seenKeys.add(key);
        deduped.push(item);
      }
    }

    // Filter by search term
    const filtered = searchQuery
      ? deduped.filter(p =>
          (p.name && p.name.toLowerCase().includes(searchQuery)) ||
          (p.variation && p.variation.toLowerCase().includes(searchQuery)) ||
          (p.unit && p.unit.toLowerCase().includes(searchQuery))
        )
      : deduped;

    // Sort
    let sorted = filtered;
    if (sort === 'low') {
      sorted = [...filtered].sort((a, b) => (Number(a.offerPrice || a.maxSaverPrice || Infinity) - Number(b.offerPrice || b.maxSaverPrice || Infinity)));
    } else if (sort === 'high') {
      sorted = [...filtered].sort((a, b) => (Number(b.offerPrice || b.maxSaverPrice || -Infinity) - Number(a.offerPrice || a.maxSaverPrice || -Infinity)));
    } else if (sort === 'discount') {
      sorted = [...filtered].sort((a, b) => Number(b.discountPercentage || 0) - Number(a.discountPercentage || 0));
    }

    // Paginate
    const lim = parseInt(limit);
    const off = parseInt(offset);
    const pageItems = sorted.slice(off, off + lim);

    res.json({
      success: true,
      data: pageItems,
      total: sorted.length,
      pagination: {
        limit: lim,
        offset: off,
        hasMore: sorted.length > off + lim
      }
    });
  } catch (error) {
    console.error('Error fetching aggregated products:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch products' });
  }
});

// Catch-all handler: send back React's index.html file for SPA routing
if (process.env.NODE_ENV === 'production' || process.env.RAILWAY_ENVIRONMENT) {
  app.get('*', (req, res) => {
    const buildPath = path.join(__dirname, '..', 'frontend', 'build', 'index.html');
    if (fs.existsSync(buildPath)) {
      res.sendFile(buildPath);
    } else {
      res.status(404).send('Frontend build not found');
    }
  });
}

/**
 * Manual cleanup endpoint
 */
app.post('/api/system/cleanup', async (req, res) => {
  try {
    const { jsonLogsDays = 7, databaseDays = 30 } = req.body;

    console.log('🧹 Manual cleanup requested via API');
    const results = await runCleanup({
      jsonLogsDays,
      databaseDays,
      verbose: true
    });

    res.json({
      success: true,
      message: 'Cleanup completed successfully',
      data: results
    });
  } catch (error) {
    console.error('❌ Error during manual cleanup:', error);
    res.status(500).json({
      success: false,
      error: 'Cleanup failed',
      message: error.message
    });
  }
});

/**
 * Get cleanup statistics
 */
app.get('/api/system/cleanup/stats', async (req, res) => {
  try {
    const logsDir = path.join(__dirname, '..', 'logs');
    let logStats = { fileCount: 0, totalSize: 0, oldestFile: null, newestFile: null };

    try {
      const files = await fs.promises.readdir(logsDir);
      const jsonFiles = files.filter(f => f.endsWith('.json'));

      if (jsonFiles.length > 0) {
        let oldestTime = Date.now();
        let newestTime = 0;
        let totalSize = 0;

        for (const file of jsonFiles) {
          const filePath = path.join(logsDir, file);
          const stats = await fs.promises.stat(filePath);
          totalSize += stats.size;

          if (stats.mtime.getTime() < oldestTime) {
            oldestTime = stats.mtime.getTime();
            logStats.oldestFile = { name: file, date: stats.mtime };
          }

          if (stats.mtime.getTime() > newestTime) {
            newestTime = stats.mtime.getTime();
            logStats.newestFile = { name: file, date: stats.mtime };
          }
        }

        logStats.fileCount = jsonFiles.length;
        logStats.totalSize = totalSize;
      }
    } catch (error) {
      // Logs directory might not exist
    }

    // Get database stats
    const dbPersistence = await DatabaseSingleton.getInstance();
    const displayedItemsStats = await dbPersistence.getDisplayedItemsStats('', 0);

    res.json({
      success: true,
      data: {
        logs: logStats,
        database: {
          displayedItems: displayedItemsStats
        }
      }
    });
  } catch (error) {
    console.error('❌ Error getting cleanup stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get cleanup statistics'
    });
  }
});

// Start server with database initialization
async function startServer() {
  // Initialize database first
  await initializeDatabase();

  // Start Telegram bot (cron scheduler) in the same process
  if (typeof startTelegramBot === 'function') {
    try {
      startTelegramBot().catch(err => {
        console.error('⚠️ Failed to start Telegram bot (async):', err && err.message ? err.message : err);
      });
      console.log('🤖 Telegram bot start initiated in API process');
    } catch (err) {
      console.error('⚠️ Failed to start Telegram bot:', err && err.message ? err.message : err);
    }
  } else {
    console.warn('⚠️ Telegram bot module not available; cron scheduler will not start in this process');
  }

  // Then start the server
  app.listen(PORT, () => {
    console.log(`🚀 API Server running on port ${PORT}`);

    if (process.env.NODE_ENV === 'production' || process.env.RAILWAY_ENVIRONMENT) {
      console.log(`📊 Dashboard available at http://localhost:${PORT}`);
      console.log(`🔗 API endpoints available at http://localhost:${PORT}/api`);
    } else {
      console.log(`📊 Dashboard will be available at http://localhost:3000`);
      console.log(`🔗 API endpoints available at http://localhost:${PORT}/api`);
    }
  });
}

// Start the server
startServer().catch(error => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

module.exports = app;