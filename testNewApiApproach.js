/**
 * Test file to use the new API approach from swgy-androdi.js
 * with our existing category/filter logic
 */

const http2 = require('http2');
const zlib = require('zlib');
const fs = require('fs');
const path = require('path');

// Load our existing mapping utility
const mappingUtil = require('./categoryFilterMappingUtil');

// ====== CONFIG FROM swgy-androdi.js ======
const COOKIES = {
    'strId': '1403687',
    'tid': 's%3A016361bf-b274-4c48-992d-570b76c96758.YNpWsQzZ%2FvlEFx1A5aNPXKelEIJSWM8kLFyQS2y8RMI',
    'token': 's%3A2a3f520a-e106-4509-adb2-c9ac6364c832a4190643-8227-46c1-b2d8-e779b8093f50.FnJD4yYQ%2BXvVbXzLVEX%2BIkyredczGA%2BUewVFgN36uOk',
    'platform': 'android',
    'userLocation': '%7B%22address%22%3A%22%22%2C%22lat%22%3A30.6569642%2C%22lng%22%3A76.6820179%2C%22id%22%3A%22d1d3ptshdil0jkpphm30%22%7D'
};

const HEADERS = {
    'host': 'stores.swiggy.com',
    'matcher': '8ed778ecbf97ec8fe8gebe8',
    'sec-ch-ua-platform': '"Android"',
    'x-build-version': '2.291.0',
    'user-agent': 'Mozilla/5.0 (Linux; Android 10; Poco X3 Pro Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.179 Mobile Safari/537.36',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Android WebView";v="138"',
    'content-type': 'application/json',
    'sec-ch-ua-mobile': '?1',
    'accept': '*/*',
    'x-requested-with': 'in.swiggy.android',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'accept-encoding': 'gzip, deflate, br',
    'accept-language': 'en-US,en;q=0.9',
    'priority': 'u=1, i'
};

// ====== VALIDATION: Check if response contains max_saver_price ======
function hasMaxSaverPrice(payload) {
  const get = (obj, path) => path.reduce((o, k) => (o && k in o ? o[k] : undefined), obj);
  const widgets = get(payload, ['data', 'widgets']);
  if (!Array.isArray(widgets)) return false;
  const hasItOnPrice = (obj) => {
    const p = obj && obj.price;
    return !!(p && (p.max_saver_price || p.maxx_saver_price));
  };
  const hasIt = (item) => {
    if (hasItOnPrice(item)) return true;
    const vars = item && item.variations;
    if (Array.isArray(vars)) {
      for (const v of vars) {
        if (hasItOnPrice(v)) return true;
        const vp = v && v.price;
        if (vp && (vp.max_saver_price || vp.maxx_saver_price)) return true;
      }
    }
    if (Array.isArray(item && item.data)) {
      for (const inner of item.data) if (hasIt(inner)) return true;
    }
    return false;
  };
  for (const w of widgets) {
    const dataArr = w && w.data;
    if (!Array.isArray(dataArr)) continue;
    for (const item of dataArr) {
      if (hasIt(item)) return true;
    }
  }
  return false;
}


// ====== UTILITY FUNCTIONS ======
function cookieHeaderFromMap(map) {
    return Object.entries(map).map(([k, v]) => `${k}=${v}`).join('; ');
}

function decompressBody(buf, enc) {
    enc = (enc || '').toLowerCase();
    if (enc.includes('br')) return zlib.brotliDecompressSync(buf);
    if (enc.includes('gzip')) return zlib.gunzipSync(buf);
    if (enc.includes('deflate')) return zlib.inflateSync(buf);
    return buf;
}

/**
 * Make HTTP/2 GET request to Swiggy API
 */
async function makeHttp2Request(urlStr) {
    const url = new URL(urlStr);
    const client = http2.connect(url.origin);

    const headers = {
        ':method': 'GET',
        ':path': url.pathname + url.search,
        ...HEADERS,
        cookie: cookieHeaderFromMap(COOKIES),
        'referer': `https://stores.swiggy.com/instamart/category-listing?${url.search.substring(1)}`
    };

    const chunks = [];
    let respHeaders = {};
    let status = 0;

    try {
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Request timeout after 30 seconds'));
            }, 30000);
            
            const req = client.request(headers);

            req.on('response', (h) => {
                respHeaders = h;
                status = Number(h[':status'] || 0);
            });

            req.on('data', (chunk) => chunks.push(chunk));

            req.on('end', () => {
                clearTimeout(timeout);
                resolve();
            });
            req.on('error', (err) => {
                clearTimeout(timeout);
                reject(err);
            });
            req.end();
        });

        const raw = Buffer.concat(chunks);
        
        // Check if we got any data
        if (raw.length === 0) {
            throw new Error('Empty response body');
        }
        
        let text = '';
        try {
            const dec = decompressBody(raw, String(respHeaders['content-encoding'] || ''));
            text = dec.toString('utf8');
        } catch (e) {
            // If decompression fails, try raw text
            try {
                text = raw.toString('utf8');
            } catch (e2) {
                throw new Error(`Failed to decode response: ${e.message}`);
            }
        }

        // Check if we got meaningful text
        if (!text || text.trim().length === 0) {
            throw new Error('Empty response text');
        }
        
        let json = null;
        try {
            json = JSON.parse(text);
        } catch (e) {
            throw new Error(`Failed to parse JSON: ${e.message}. Response text length: ${text.length}, First 200 chars: ${text.substring(0, 200)}`);
        }

        return {
            ok: status >= 200 && status < 300,
            status,
            headers: respHeaders,
            text,
            json
        };
    } catch (err) {
        return { ok: false, status: 0, error: err };
    } finally {
        try { client.close(); } catch {}
    }
}

/**
 * Build category listing URL
 */
function buildCategoryListingUrl(categoryName, filterName = '') {
    const baseUrl = 'https://stores.swiggy.com/api/instamart/category-listing';
    // Resolve taxonomy type from mapping file
    const taxonomyType = getTaxonomyType(categoryName, filterName) || getTaxonomyType(categoryName) || 'Speciality taxonomy 1';
    const params = new URLSearchParams({
        categoryName: categoryName,
        storeId: COOKIES.strId,
        offset: '0',
        filterName: filterName,
        primaryStoreId: COOKIES.strId || '',
        secondaryStoreId: '',
        taxonomyType
    });

    return `${baseUrl}?${params.toString()}`;
}


/**
 * Get taxonomy type from category_filters_data.json for a given category/filter
 */
function getTaxonomyType(categoryName, filterName = '') {
  try {
    const dataPath = path.join(__dirname, 'category_filters_data.json');
    if (!fs.existsSync(dataPath)) return null;
    const raw = fs.readFileSync(dataPath, 'utf8');
    const data = JSON.parse(raw);
    const sections = data && data.sections ? data.sections : {};

    // Find category by name (case-insensitive)
    for (const sectionName of Object.keys(sections)) {
      const section = sections[sectionName];
      const categories = section && section.categories ? section.categories : [];
      for (const cat of categories) {
        if ((cat.name || '').toLowerCase() === String(categoryName).toLowerCase()) {
          // If filterName provided, try to find its type
          if (filterName) {
            const filters = Array.isArray(cat.filters) ? cat.filters : [];
            const match = filters.find(f => (f.name || '').toLowerCase() === String(filterName).toLowerCase());
            if (match && match.type) return match.type;
          }
          // Fallback: extract taxonomyType from category apiUrl if present
          if (cat.apiUrl) {
            try {
              const u = new URL(cat.apiUrl);
              const tax = u.searchParams.get('taxonomyType');
              if (tax) return tax;
            } catch {}
          }
          // As last resort, check first filter's type
          const firstType = (cat.filters && cat.filters[0] && cat.filters[0].type) || null;
          if (firstType) return firstType;
        }
      }
    }
  } catch (e) {
    // ignore and fallback
  }
  return null;
}

/**
 * Extract items from the new API response format
 */
function extractItemsFromResponse(response) {
    try {
        if (!response.data || !response.data.widgets) {
            console.warn('Response does not have expected widgets structure');
            return [];
        }

        const items = [];

        for (const widget of response.data.widgets) {
            if (!widget.data || !Array.isArray(widget.data)) continue;

            for (const item of widget.data) {
                // Check if item has price information
                if (item.price || (item.variations && item.variations.length > 0)) {
                    items.push(item);
                }
            }
        }

        return items;
    } catch (error) {
        console.error('Error extracting items:', error.message);
        return [];
    }
}

/**
 * Test function to fetch data for a specific category/filter
 */
async function testCategoryFetch(categoryName, filterName = '') {
    try {
        console.log(`\n🧪 Testing: ${categoryName}${filterName ? ` / ${filterName}` : ''}`);

        const url = buildCategoryListingUrl(categoryName, filterName);
        console.log(`📡 URL: ${url}`);

        const response = await makeHttp2Request(url);

        if (!response.ok) {
            console.error(`❌ Request failed with status: ${response.status}`);
            if (response.error) {
                console.error(`Error: ${response.error.message}`);
            }
            return null;
        }

        console.log(`✅ Request successful (${response.status})`);

        const hasDiscounts = hasMaxSaverPrice(response.json);
        console.log(`🔎 hasMaxSaverPrice: ${hasDiscounts ? '✅ YES' : '❌ NO'}`);

        const items = extractItemsFromResponse(response.json);
        console.log(`📦 Found ${items.length} items`);

        // Show sample of first few items
        if (items.length > 0) {
            console.log('\n📋 Sample items:');
            items.slice(0, 3).forEach((item, index) => {
                console.log(`  ${index + 1}. ${item.display_name || item.name || 'Unknown'}`);
                if (item.price) {
                    console.log(`     Price: ₹${item.price.offer_price || item.price.store_price || 'N/A'}`);
                }
                if (item.variations && item.variations[0] && item.variations[0].price) {
                    const price = item.variations[0].price;
                    console.log(`     Price: ₹${price.offer_price || price.store_price || 'N/A'}`);
                }
            });
        }

        // Save response for analysis
        const timestamp = new Date().toISOString().replace(/:/g, '-');
        const filename = `new_api_test_${categoryName.replace(/[^a-z0-9]/gi, '_')}_${timestamp}.json`;
        const filepath = path.join(__dirname, 'logs', filename);

        // Ensure logs directory exists
        const logsDir = path.join(__dirname, 'logs');
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir);
        }

        fs.writeFileSync(filepath, JSON.stringify({
            metadata: {
                categoryName,
                filterName,
                url,
                timestamp: new Date().toISOString(),
                itemCount: items.length
            },
            response: response.json
        }, null, 2));

        console.log(`💾 Response saved to: ${filename}`);

        return {
            items,
            response: response.json,
            url
        };

    } catch (error) {
        console.error(`❌ Test failed: ${error.message}`);
        return null;
    }
}

/**
 * Main test function
 */
async function runTests() {
    console.log('🚀 Starting New API Approach Tests');
    console.log('=====================================');

    // Test with a few different categories
    const testCases = [
        // { category: 'Dry Fruits and Seeds Mix', filter: '' },
        // { category: 'Cold Drinks and Juices', filter: '' },
        // { category: 'Fresh Fruits', filter: '' },
        // { category: 'Home and Furnishing', filter: '' },
        // { category: 'Fresh Fruits', filter: 'Seasonal Fruits' },
        // { category: 'Fresh Vegetables', filter: '' },
        { category: 'Appliances', filter: 'Juicer Mixer Grinders' }
    ];

    for (const testCase of testCases) {
        await testCategoryFetch(testCase.category, testCase.filter);

        // Wait 3 seconds between requests
        if (testCases.indexOf(testCase) < testCases.length - 1) {
            console.log('\n⏳ Waiting 3 seconds...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }

    console.log('\n✅ All tests completed!');
    console.log('Check the logs/ directory for saved responses.');
}

// Export functions for use in other files
module.exports = {
    makeHttp2Request,
    buildCategoryListingUrl,
    extractItemsFromResponse,
    testCategoryFetch
};

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}
