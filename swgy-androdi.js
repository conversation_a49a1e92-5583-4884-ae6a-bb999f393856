/**
 * Find minimal cookie set for Swiggy Instamart category-listing endpoint.
 * Strategy: start with all cookies, then greedily try removing one cookie
 * at a time. If the request still succeeds (HTTP 200), we keep it removed.
 * Repeat passes until no more cookies can be removed.
 *
 * Gap between requests: 3 seconds.
 */

const http2 = require('http2');
const zlib = require('zlib');

// ====== CONFIG ======
const URL_STR = 'https://stores.swiggy.com/api/instamart/category-listing?categoryName=Dry%20Fruits%20and%20Seeds%20Mix&storeId=&offset=0&filterName=&primaryStoreId=&secondaryStoreId=&taxonomyType=Speciality%20taxonomy%201';

// Pin cookies that must never be removed (e.g., strId is your store mapping)
const PINNED_COOKIES = new Set(['strId']);

// All cookies from your request (key -> value)
const ALL_COOKIES = {
    // 'ally-on': 'false',
    'strId': '1403687',
    // 'LocSrc': 's%3Ahr.9V1dbBhtlBzcnd%2Bn90IxsXyXw67WhvvaDVmokba%2BDP4',
    // 'deviceId': 's%3Af5546a3d273221fe.gYWwRt1SiAqSzGqCerEL1raI%2Bc9y6YoIMchv%2Byv1XA4',
    'tid': 's%3A016361bf-b274-4c48-992d-570b76c96758.YNpWsQzZ%2FvlEFx1A5aNPXKelEIJSWM8kLFyQS2y8RMI',
    'token': 's%3A2a3f520a-e106-4509-adb2-c9ac6364c832a4190643-8227-46c1-b2d8-e779b8093f50.FnJD4yYQ%2BXvVbXzLVEX%2BIkyredczGA%2BUewVFgN36uOk',
    // 'swuid': 's%3Af5546a3d273221fe.gYWwRt1SiAqSzGqCerEL1raI%2Bc9y6YoIMchv%2Byv1XA4',
    // 'versionCode': '1426',
    'platform': 'android',
    // 'subplatform': '',
    // 'statusBarHeight': '0',
    // 'bottomOffset': '0',
    // 'genieTrackOn': 'false',
    // 'isNative': 'true',
    // 'openIMHP': 'false',
    // 'lat': 's%3A30.6554637.M40LjPWU9mq1ZClY1wdKT1TWtDsAscKMfagUk0uxzZQ',
    // 'lng': 's%3A76.6796649.FRKYTJ1U%2FCZzXYahWIHbF3tL2JqiLSSsJw9Uipko88g',
    // 'address': 's%3A.4Wx2Am9WLolnmzVcU32g6YaFDw0QbIBFRj2nkO7P25s',
    // 'webBottomBarHeight': '0',
    // 'sid': 's%3Am7ra1a2b-08dd-4a09-b93a-ff9fd6c60aae.xjNvxBr%2FHURK0KiN7uRXzO4KQqOpwDpAZJW%2FCG%2F3wZU',
    // 'addressId': 's%3Ad1d3ptshdil0jkpphm30.CSDuXRvylDfRiymOhbl7OViLcic2RjDC0Eb5JkQcMvY',
    // 'userLocation': '%7B%22address%22%3A%22%22%2C%22lat%22%3A30.6554637%2C%22lng%22%3A76.6796649%2C%22id%22%3A%22d1d3ptshdil0jkpphm30%22%7D'
    'userLocation': '%7B%22address%22%3A%22%22%2C%22lat%22%3A30.6569642%2C%22lng%22%3A76.6820179%2C%22id%22%3A%22d1d3ptshdil0jkpphm30%22%7D'
};

// Headers (same as before; we’ll keep accept-encoding limited to gzip/deflate/br)
const COMMON_HEADERS = {
    'host': 'stores.swiggy.com',
    // 'matcher': '8ed778ecbf97ec8fe8gebe8',
    'sec-ch-ua-platform': '"Android"',
    'x-build-version': '2.291.0',
    'user-agent': 'Mozilla/5.0 (Linux; Android 10; Poco X3 Pro Build/QKQ1.190716.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.179 Mobile Safari/537.36',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Android WebView";v="138"',
    'content-type': 'application/json',
    'sec-ch-ua-mobile': '?1',
    'accept': '*/*',
    'x-requested-with': 'in.swiggy.android',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    // 'referer': 'https://stores.swiggy.com/instamart/category-listing?categoryName=Dry%20Fruits%20and%20Seeds%20Mix&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality%20taxonomy%201&showAgeConsent=false&lat=30.655464&lng=76.679665',
    'accept-encoding': 'gzip, deflate, br',
    'accept-language': 'en-US,en;q=0.9',
    'priority': 'u=1, i'
};

// ====== UTIL ======
const sleep = ms => new Promise(res => setTimeout(res, ms));

// Returns true if ANY item has a max_saver_price (or maxx_saver_price) object
function hasMaxSaverPrice(payload) {
    const get = (obj, path) => path.reduce((o, k) => (o && k in o ? o[k] : undefined), obj);

    const widgets = get(payload, ['data', 'widgets']);
    if (!Array.isArray(widgets)) return false;

    const hasItOnPrice = (obj) => {
        const p = obj && obj.price;
        return !!(p && (p.max_saver_price || p.maxx_saver_price));
    };

    const hasIt = (item) => {
        // 1) direct on item.price
        if (hasItOnPrice(item)) return true;

        // 2) inside variations[].price
        const vars = item && item.variations;
        if (Array.isArray(vars)) {
            for (const v of vars) {
                if (hasItOnPrice(v)) return true;        // variation itself has price
                if (hasItOnPrice(v && v)) return true;   // (defensive)
                const vp = v && v.price;
                if (vp && (vp.max_saver_price || vp.maxx_saver_price)) return true;
            }
        }

        // 3) in case some schemas put "data" inside items again
        if (Array.isArray(item && item.data)) {
            for (const inner of item.data) if (hasIt(inner)) return true;
        }

        return false;
    };

    for (const w of widgets) {
        const dataArr = w && w.data;
        if (!Array.isArray(dataArr)) continue;
        for (const item of dataArr) {
            if (hasIt(item)) {
                console.log("matched")
                return true;
            }
        }
    }
    return false;
}


function cookieHeaderFromMap(map) {
    // Join as "k=v" pairs separated by "; "
    return Object.entries(map).map(([k, v]) => `${k}=${v}`).join('; ');
}

function decompressBody(buf, enc) {
    enc = (enc || '').toLowerCase();
    if (enc.includes('br')) return zlib.brotliDecompressSync(buf);
    if (enc.includes('gzip')) return zlib.gunzipSync(buf);
    if (enc.includes('deflate')) return zlib.inflateSync(buf);
    return buf;
}

async function http2Get(urlStr, cookieMap) {
    const url = new URL(urlStr);
    const client = http2.connect(url.origin);
    const headers = {
        ':method': 'GET',
        ':path': url.pathname + url.search,
        ...COMMON_HEADERS,
        cookie: cookieHeaderFromMap(cookieMap),
    };

    const chunks = [];
    let respHeaders = {};
    let status = 0;

    try {
        await new Promise((resolve, reject) => {
            const req = client.request(headers);

            req.on('response', (h) => {
                respHeaders = h;
                status = Number(h[':status'] || 0);
            });

            req.on('data', (chunk) => chunks.push(chunk));
            req.on('end', () => resolve());
            req.on('error', reject);
            req.end();
        });

        const raw = Buffer.concat(chunks);
        let text = '';
        try {
            const dec = decompressBody(raw, String(respHeaders['content-encoding'] || ''));
            text = dec.toString('utf8');
        } catch (e) {
            // fall back to raw text if decompression fails
            text = raw.toString('utf8');
        }

        // Try to parse JSON for convenience
        let json = null;
        try { json = JSON.parse(text); } catch {}

        return { ok: status >= 200 && status < 300 && hasMaxSaverPrice(json), status, headers: respHeaders, text, json };
    } catch (err) {
        return { ok: false, status: 0, error: err };
    } finally {
        try { client.close(); } catch {}
    }
}

// ====== MINIMIZATION LOOP ======
(async () => {
    let current = { ...ALL_COOKIES };
    let attempt = 0;

    console.log(`Starting with ${Object.keys(current).length} cookies.`);
    console.log('Pinned cookies:', [...PINNED_COOKIES].join(', ') || '(none)');

    // First, verify baseline works
    console.log('Baseline request (with all cookies)...');
    let res = await http2Get(URL_STR, current);
    console.log(`→ Status: ${res.status}`);
    if (!res.ok) {
        console.error('Baseline failed. The endpoint may be rejecting these cookies or environment.');
        if (res.text) console.error('Body (truncated 500 chars):', res.text.slice(0, 500));
        process.exit(1);
    }
    console.log('Baseline OK.\n');
    await sleep(3000);

    let removedSomething = true;
    while (removedSomething) {
        removedSomething = false;

        // Try to remove each non-pinned cookie once per pass
        for (const key of Object.keys(current)) {
            if (PINNED_COOKIES.has(key)) continue; // skip pinned

            // Tentatively remove this cookie
            const testMap = { ...current };
            delete testMap[key];

            attempt++;
            console.log(`[Attempt ${attempt}] Trying without cookie: ${key} (remaining: ${Object.keys(testMap).length})`);

            const result = await http2Get(URL_STR, testMap);
            console.log(`→ Status: ${result.status}`);

            if (result.ok) {
                // Success without this cookie — keep it removed
                console.log(`✅ Removed "${key}" permanently.`);
                current = testMap;
                removedSomething = true;
            } else {
                console.log(`❌ "${key}" seems required (keeping it).`);
            }

            // Respect 3s delay between requests
            await sleep(3000);
        }
    }

    console.log('\n=== MINIMIZATION COMPLETE ===');
    console.log(`Attempts: ${attempt}`);
    console.log(`Final cookie count: ${Object.keys(current).length}`);

    // Print minimal cookie header
    const minimalCookieHeader = cookieHeaderFromMap(current);
    console.log('\nMinimal Cookie header value:\n');
    console.log(minimalCookieHeader);

    // Final verification request with minimal set
    console.log('\nVerifying minimal set...');
    const finalRes = await http2Get(URL_STR, current);
    console.log(`→ Status: ${finalRes.status}`);
    if (finalRes.ok && finalRes.json) {
        console.log('Sample of response JSON:', JSON.stringify(finalRes.json).slice(0, 500));
    } else if (finalRes.ok) {
        console.log('Response text (truncated 500):', finalRes.text.slice(0, 500));
    } else {
        console.log('Verification failed; consider pinning more cookies.');
    }
})();