#!/usr/bin/env node
/**
 * Script: findMaxSaverGaps.js
 * Purpose: Use the new Instamart category-listing API to find items where
 *          max_saver_price offers an additional X% savings over the normal price
 * Usage:
 *   node findMaxSaverGaps.js --codes "A1,B2,C3" --threshold 10 --baseline offer
 *   node findMaxSaverGaps.js --categories "Dry Fruits and Seeds Mix,Fresh Fruits" --threshold 10 --baseline offer
 *   Options:
 *     --codes       Comma-separated category codes (e.g., A1,B2,C3) - preferred method
 *     --categories  Comma-separated category names (legacy support)
 *     --threshold   Minimum percent gap to include (default: 10)
 *     --baseline    Baseline for comparison: 'offer' (default) or 'store'
 *     --limit       Max items to print per category (default: 50)
 */

const fs = require('fs');
const path = require('path');
const { makeHttp2Request, buildCategoryListingUrl } = require('./testNewApiApproach');
const DatabaseSingleton = require('./dbSingleton');
const mappingUtil = require('./categoryFilterMappingUtil');

// Load config for URL generation
const config = JSON.parse(fs.readFileSync(path.join(__dirname, 'config.json'), 'utf8'));


// Embedded test array for easy testing without CLI
const EMBEDDED_TEST = {
  enabled: true,
  // Preferred: category codes (will be converted to category + filter pairs)
  codes: [
    'J', // Cleaning Essentials
    // 'A1', // Example code
    // 'B2', // Example code
  ],
  // Legacy: explicit category + filter pairs
  cases: [
    // { category: 'Dry Fruits and Seeds Mix', filter: '' },
    // { category: 'Cleaning Essentials', filter: '' },
    // { category: 'Appliances', filter: '' },
    // { category: 'Fresh Fruits', filter: 'Seasonal Fruits' },
  ],
  // Legacy: categories only (will run with empty filter)
  categories: [
    // 'Dry Fruits and Seeds Mix',
  ],
  threshold: 10,     // percent
  baseline: 'offer',// 'offer' or 'store'
  limit: 10
};

// Helper to build URL for category + optional filter using the new API
function buildCategoryFilterUrl(categoryName, filterName = '') {
  // Leverage existing builder which will inject taxonomyType via getTaxonomyType
  return buildCategoryListingUrl(categoryName, filterName);
}

// Fetch all filter names for a given category using the new API with extended retry logic
async function fetchFiltersForCategory(categoryName) {
  const url = buildCategoryFilterUrl(categoryName, '');

  // First 3 attempts with 10-second gaps
  for (let attempt = 1; attempt <= 3; attempt++) {
    const resp = await makeHttp2Request(url);
    if (resp.ok) {
      const data = resp.json && resp.json.data;
      const filters = data && Array.isArray(data.filters) ? data.filters : [];
      const names = filters.map(f => (f && f.name ? String(f.name).trim() : '')).filter(Boolean);
      // Deduplicate while preserving order
      const seen = new Set();
      const unique = [];
      for (const n of names) { if (!seen.has(n)) { seen.add(n); unique.push(n); } }
      return unique;
    }

    console.error(`❌ Attempt ${attempt}/5 failed for category "${categoryName}" (status ${resp.status})`);
    if (resp.json) {
      console.error('Error response:', JSON.stringify(resp.json, null, 2));
    } else if (resp.error) {
      console.error('Error details:', resp.error.message || resp.error);
    }

    if (attempt < 3) {
      console.log('⏳ Retrying in 10 seconds...');
      await sleep(10000);
    }
  }

  // Additional 2 attempts with 1-minute gaps
  for (let attempt = 4; attempt <= 5; attempt++) {
    console.log('⏳ Extended retry - waiting 1 minute...');
    await sleep(60000);

    const resp = await makeHttp2Request(url);
    if (resp.ok) {
      const data = resp.json && resp.json.data;
      const filters = data && Array.isArray(data.filters) ? data.filters : [];
      const names = filters.map(f => (f && f.name ? String(f.name).trim() : '')).filter(Boolean);
      // Deduplicate while preserving order
      const seen = new Set();
      const unique = [];
      for (const n of names) { if (!seen.has(n)) { seen.add(n); unique.push(n); } }
      return unique;
    }

    console.error(`❌ Attempt ${attempt}/5 failed for category "${categoryName}" (status ${resp.status})`);
    if (resp.json) {
      console.error('Error response:', JSON.stringify(resp.json, null, 2));
    } else if (resp.error) {
      console.error('Error details:', resp.error.message || resp.error);
    }
  }

  console.error(`❌ All 5 attempts failed for category "${categoryName}"`);
  return [];
}

function generateItemUrl(itemId) {
  const baseUrl = `${config.urls.instamartItemBase}${itemId}${config.urls.shareParam}`;
  const storeId = config.urls.storeId;
  const sep = baseUrl.includes('?') ? '&' : '?';
  return `${baseUrl}${sep}storeId=${storeId}`;
}

function sleep(ms) { return new Promise(r => setTimeout(r, ms)); }

function parseArgs() {
  const args = process.argv.slice(2);
  const out = { codes: [], categories: [], cases: [], threshold: 10, baseline: 'offer', limit: 50 };
  for (let i = 0; i < args.length; i++) {
    const a = args[i];
    if (a === '--codes' && args[i+1]) {
      out.codes = args[++i].split(',').map(s => s.trim()).filter(Boolean);
    } else if (a === '--categories' && args[i+1]) {
      out.categories = args[++i].split(',').map(s => s.trim()).filter(Boolean);
    } else if (a === '--cases' && args[i+1]) {
      // Format: "Category|Filter,Category2|Filter2,Category3|" (empty filter allowed)
      const raw = args[++i];
      out.cases = raw.split(',')
        .map(s => s.trim())
        .filter(Boolean)
        .map(seg => {
          const parts = seg.split('|');
          const category = (parts[0] || '').trim();
          const filter = (parts[1] || '').trim();
          return { category, filter };
        })
        .filter(pair => pair.category);
    } else if (a === '--threshold' && args[i+1]) {
      out.threshold = Number(args[++i]);
    } else if (a === '--baseline' && args[i+1]) {
      const val = String(args[++i]).toLowerCase();
      out.baseline = (val === 'store') ? 'store' : 'offer';
    } else if (a === '--limit' && args[i+1]) {
      out.limit = Number(args[++i]);
    }
  }
  // Convert codes to cases (preferred method)
  if (out.codes && out.codes.length > 0) {
    const codesCases = [];
    for (const code of out.codes) {
      const mapping = mappingUtil.codeToNames(code);
      if (mapping) {
        codesCases.push({
          category: mapping.category,
          filter: mapping.filter || '',
          code: code // Keep track of original code
        });
      } else {
        console.warn(`⚠️ Unknown code: ${code} - skipping`);
      }
    }
    if (codesCases.length > 0) {
      out.cases = codesCases;
    }
  }

  // Backward compat: if no cases provided but categories present, map to empty filters
  if ((!out.cases || out.cases.length === 0) && out.categories && out.categories.length > 0) {
    out.cases = out.categories.map(c => ({ category: c, filter: '' }));
  }

  return out;
}

function computeGapPercent(priceObj, baseline) {
  if (!priceObj) return null;
  const maxSaver = priceObj.max_saver_price && priceObj.max_saver_price.offer_price;
  if (!maxSaver || !Number.isFinite(maxSaver)) return null;
  const offer = priceObj.offer_price;
  const store = priceObj.store_price;
  const base = baseline === 'store' ? (Number.isFinite(store) ? store : offer) : (Number.isFinite(offer) ? offer : store);
  if (!Number.isFinite(base) || base <= 0) return null;
  const diff = base - maxSaver;
  const pct = (diff / base) * 100;
  return pct;
}

function pushResult(list, itemCtx) {
  list.push(itemCtx);
}

function extractMaxSaverItems(responseJson, { baseline, threshold }) {
  const results = [];
  const widgets = responseJson && responseJson.data && Array.isArray(responseJson.data.widgets) ? responseJson.data.widgets : [];
  for (const w of widgets) {
    const dataArr = w && Array.isArray(w.data) ? w.data : [];
    for (const item of dataArr) {
      // Item-level price check
      if (item && item.price) {
        const pct = computeGapPercent(item.price, baseline);
        if (pct !== null && pct >= threshold) {
          pushResult(results, {
            name: item.display_name || item.name || 'Unknown',
            productId: item.product_id,
            baselinePrice: baseline === 'store' ? (item.price.store_price ?? item.price.offer_price) : (item.price.offer_price ?? item.price.store_price),
            maxSaverPrice: item.price.max_saver_price.offer_price,
            gapPercent: Number(pct.toFixed(2)),
            url: item.product_id ? generateItemUrl(item.product_id) : null,
            source: 'item'
          });
        }
      }
      // Variation-level price checks
      if (item && Array.isArray(item.variations)) {

        for (const v of item.variations) {
          if (!v || !v.price) continue;
          const pct = computeGapPercent(v.price, baseline);
          if (pct !== null && pct >= threshold) {
            pushResult(results, {
              name: v.display_name || item.display_name || item.name || 'Unknown',
              productId: item.product_id,
              baselinePrice: baseline === 'store' ? (v.price.store_price ?? v.price.offer_price) : (v.price.offer_price ?? v.price.store_price),
              maxSaverPrice: v.price.max_saver_price.offer_price,
              gapPercent: Number(pct.toFixed(2)),
              url: item.product_id ? generateItemUrl(item.product_id) : null,
              quantity: v.quantity,
              unit: v.unit_of_measure,
              source: 'variation'
            });
          }
        }
      }
    }
  }

  // Sort by gap desc
  results.sort((a, b) => b.gapPercent - a.gapPercent);
  return results;
}

async function processCategory(categoryName, filterName, { baseline, threshold, limit }) {
  const label = filterName ? `${categoryName} / ${filterName}` : categoryName;
  console.log(`\n🧪 Category: ${label}`);
  const url = buildCategoryFilterUrl(categoryName, filterName);
  console.log(`📡 URL: ${url}`);

  // Initial 3 attempts with 10-second gaps
  for (let attempt = 1; attempt <= 3; attempt++) {
    const resp = await makeHttp2Request(url);
    if (resp.ok) {
      const items = extractMaxSaverItems(resp.json, { baseline, threshold });
      const top = items.slice(0, limit);
      console.log(`✅ Found ${items.length} items with ≥${threshold}% extra savings over ${baseline} price`);
      for (let i = 0; i < top.length; i++) {
        const it = top[i];
        console.log(`  ${i + 1}. ${it.name}`);
        console.log(`     ${baseline} price: ₹${it.baselinePrice}  →  max saver: ₹${it.maxSaverPrice}  (${it.gapPercent}% extra off)`);
        if (it.quantity && it.unit) console.log(`     Pack: ${it.quantity} ${it.unit}`);
        if (it.url) console.log(`     URL: ${it.url}`);
      }
      return top;
    }

    console.error(`❌ Attempt ${attempt}/5 failed for ${label} (status ${resp.status})`);
    if (resp.json) {
      console.error('Error response:', JSON.stringify(resp.json, null, 2));
    } else if (resp.error) {
      console.error('Error details:', resp.error.message || resp.error);
    }

    if (attempt < 3) {
      console.log('⏳ Retrying in 10 seconds...');
      await sleep(10000);
    }
  }

  // Extended 2 attempts with 1-minute gaps
  for (let attempt = 4; attempt <= 5; attempt++) {
    console.log(`⏳ Extended retry - waiting 1 minute before attempt ${attempt}/5...`);
    await sleep(60000);

    const resp = await makeHttp2Request(url);
    if (resp.ok) {
      const items = extractMaxSaverItems(resp.json, { baseline, threshold });
      const top = items.slice(0, limit);
      console.log(`✅ Found ${items.length} items with ≥${threshold}% extra savings over ${baseline} price`);
      for (let i = 0; i < top.length; i++) {
        const it = top[i];
        console.log(`  ${i + 1}. ${it.name}`);
        console.log(`     ${baseline} price: ₹${it.baselinePrice}  →  max saver: ₹${it.maxSaverPrice}  (${it.gapPercent}% extra off)`);
        if (it.quantity && it.unit) console.log(`     Pack: ${it.quantity} ${it.unit}`);
        if (it.url) console.log(`     URL: ${it.url}`);
      }
      return top;
    }

    console.error(`❌ Attempt ${attempt}/5 failed for ${label} (status ${resp.status})`);
    if (resp.json) {
      console.error('Error response:', JSON.stringify(resp.json, null, 2));
    } else if (resp.error) {
      console.error('Error details:', resp.error.message || resp.error);
    }
  }

  console.error(`❌ All 5 attempts failed for ${label}`);
  return [];
}

async function processCategoryOrAllFilters(categoryName, filterName, opts) {
  // If a filter is provided, just run that case
  if (filterName && filterName.trim()) {
    return [{ filter: filterName, results: await processCategory(categoryName, filterName, opts) }];
  }
  // Else, fetch all filters from the API and run each
  const filters = await fetchFiltersForCategory(categoryName);
  console.log(`🔎 Found ${filters.length} filters for ${categoryName}`);
  const out = [];
  for (let i = 0; i < filters.length; i++) {
    const f = filters[i];
    const results = await processCategory(categoryName, f, opts);
    out.push({ filter: f, results });
    if (i < filters.length - 1) {
      console.log('⏳ Waiting 3 seconds...');
      await sleep(config.api && Number.isFinite(config.api.requestDelay) ? config.api.requestDelay : 3000);
    }
  }
  return out;
}

/**
 * Remove duplicate products based on product ID
 * When duplicates are found, keeps the one with the highest gap percentage
 */
function deduplicateByProductId(results) {
  const productMap = new Map();
  const itemsWithoutId = [];

  for (const result of results) {
    const productId = result.productId;

    // Items without product ID are kept as-is (can't deduplicate)
    if (!productId || productId.trim() === '') {
      itemsWithoutId.push(result);
      continue;
    }

    const existing = productMap.get(productId);

    if (!existing) {
      // First occurrence of this product
      productMap.set(productId, result);
    } else {
      // Duplicate found - keep the one with higher gap percentage
      if (result.gapPercent > existing.gapPercent) {
        console.log(`🔄 Replacing duplicate: "${existing.itemName}" (${existing.gapPercent}%) with "${result.itemName}" (${result.gapPercent}%)`);
        productMap.set(productId, result);
      } else {
        console.log(`🔄 Skipping duplicate: "${result.itemName}" (${result.gapPercent}%) - keeping "${existing.itemName}" (${existing.gapPercent}%)`);
      }
    }
  }

  // Combine deduplicated items with items that had no product ID
  const deduplicated = Array.from(productMap.values()).concat(itemsWithoutId);

  // Sort by gap percentage (highest first)
  return deduplicated.sort((a, b) => b.gapPercent - a.gapPercent);
}

async function main() {
  const startTime = Date.now();

  // Get database singleton instance
  const dbPersistence = await DatabaseSingleton.getInstance();

  // Use embedded test if enabled and no CLI cases provided
  const args = parseArgs();
  const useEmbedded = EMBEDDED_TEST.enabled && (!args.cases || args.cases.length === 0);

  let cases;
  if (useEmbedded) {
    // Priority: codes > cases > categories
    if (EMBEDDED_TEST.codes && EMBEDDED_TEST.codes.length > 0) {
      cases = [];
      for (const code of EMBEDDED_TEST.codes) {
        const mapping = mappingUtil.codeToNames(code);
        if (mapping) {
          cases.push({
            category: mapping.category,
            filter: mapping.filter || '',
            code: code
          });
        } else {
          console.warn(`⚠️ Unknown embedded test code: ${code} - skipping`);
        }
      }
    } else if (EMBEDDED_TEST.cases && EMBEDDED_TEST.cases.length > 0) {
      cases = EMBEDDED_TEST.cases;
    } else if (EMBEDDED_TEST.categories && EMBEDDED_TEST.categories.length > 0) {
      cases = EMBEDDED_TEST.categories.map(c => ({ category: c, filter: '' }));
    } else {
      cases = [];
    }
  } else {
    cases = args.cases;
  }

  const threshold = useEmbedded ? EMBEDDED_TEST.threshold : args.threshold;
  const baseline = useEmbedded ? EMBEDDED_TEST.baseline : args.baseline;
  const limit = useEmbedded ? EMBEDDED_TEST.limit : args.limit;

  if (!cases.length) {
    console.error('Please provide --codes "A1,B2,C3" or --cases "Category|Filter,..." or enable EMBEDDED_TEST');
    process.exit(1);
  }

  // Generate unique run ID
  const runId = `gap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  console.log('🚀 Finding items with extra savings from Max Saver');
  console.log(`📊 Run ID: ${runId}`);
  console.log(`Options => baseline: ${baseline}, threshold: ${threshold}%, limit: ${limit}`);

  const all = [];
  let totalItemsFound = 0;

  for (let i = 0; i < cases.length; i++) {
    const { category, filter } = cases[i];
    const perFilterResults = await processCategoryOrAllFilters(category, filter, { baseline, threshold, limit });
    all.push({ category, filter, results: perFilterResults });

    // Count total items across all filters for this category
    const categoryItemCount = perFilterResults.reduce((sum, filterResult) => sum + filterResult.results.length, 0);
    totalItemsFound += categoryItemCount;

    if (i < cases.length - 1) {
      console.log('⏳ Waiting 3 seconds...');
      await sleep(config.api && Number.isFinite(config.api.requestDelay) ? config.api.requestDelay : 3000);
    }
  }

  const endTime = Date.now();
  const executionTimeMs = endTime - startTime;

  // Flatten all results for deduplication and database storage
  const flatResults = [];
  for (const categoryData of all) {
    for (const filterData of categoryData.results) {
      for (const item of filterData.results) {
        flatResults.push({
          categoryName: categoryData.category,
          filterName: filterData.filter || null,
          itemName: item.name,
          productId: item.productId,
          baselinePrice: item.baselinePrice,
          maxSaverPrice: item.maxSaverPrice,
          gapPercent: item.gapPercent,
          quantity: item.quantity,
          unit: item.unit,
          url: item.url,
          source: item.source
        });
      }
    }
  }

  // Remove duplicates based on product ID
  const deduplicatedResults = deduplicateByProductId(flatResults);
  const duplicatesRemoved = flatResults.length - deduplicatedResults.length;

  if (duplicatesRemoved > 0) {
    console.log(`🔄 Removed ${duplicatesRemoved} duplicate products (${deduplicatedResults.length} unique products remaining)`);
  }

  // Save to database with deduplicated count
  const runData = {
    runId,
    baseline,
    threshold,
    limit,
    totalCategories: cases.length,
    totalItemsFound: deduplicatedResults.length, // Use deduplicated count
    executionTimeMs,
    status: 'completed',
    completedAt: new Date()
  };

  await dbPersistence.saveMaxSaverGapRun(runData);

  // Save deduplicated results to database
  if (deduplicatedResults.length > 0) {
    await dbPersistence.saveMaxSaverGapResults(runId, deduplicatedResults);
  }

  // Optionally write to logs
  try {
    const outPath = path.join(__dirname, 'logs', `max_saver_results_${Date.now()}.json`);
    const payload = {
      runId,
      generatedAt: new Date().toISOString(),
      baseline,
      threshold,
      cases,
      totalItemsFound,
      executionTimeMs,
      data: all
    };
    fs.writeFileSync(outPath, JSON.stringify(payload, null, 2));
    console.log(`\n💾 Saved results: ${path.basename(outPath)}`);
    console.log(`📊 Total items found: ${flatResults.length} (${deduplicatedResults.length} unique after deduplication)`);
    if (duplicatesRemoved > 0) {
      console.log(`🔄 Duplicates removed: ${duplicatesRemoved}`);
    }
    console.log(`⏱️ Execution time: ${(executionTimeMs / 1000).toFixed(2)}s`);
    console.log(`🗄️ Saved to database with run ID: ${runId}`);
  } catch (error) {
    console.error('Error saving to logs:', error.message);
  }

  await dbPersistence.close();
}

if (require.main === module) {
  main().catch(async err => {
    console.error('❌ Fatal error:', err && err.message ? err.message : err);

    // Try to mark the run as failed in the database
    try {
      const dbPersistence = await DatabaseSingleton.getInstance();

      // Try to extract run ID from the error context or generate a fallback
      const timestamp = Date.now();
      const runId = `gap_${timestamp}_failed`;

      const failedRunData = {
        runId,
        baseline: 'offer',
        threshold: 10,
        limit: 20,
        totalCategories: 0,
        totalItemsFound: 0,
        executionTimeMs: 0,
        status: 'failed',
        completedAt: new Date()
      };

      await dbPersistence.saveMaxSaverGapRun(failedRunData);
      console.log(`💾 Marked run as failed: ${runId}`);

      await dbPersistence.close();
    } catch (dbError) {
      console.error('❌ Error updating run status:', dbError.message);
    }

    process.exit(1);
  });
}