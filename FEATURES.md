# Updated Features Documentation

## Overview
The Swiggy Instamart Discounted Items Utility has been completely updated with real API data and enhanced features for better filtering and customization.

## Key Changes Made

### 1. **Real API Data Integration**
- ✅ **Fixed API Communication**: Updated to handle gzip compression and proper HTTP headers
- ✅ **Real Filter Data**: All 568 filters across 44 categories now use actual Swiggy API data
- ✅ **Accurate IDs**: Filter IDs like "6822eeeded32000001e25abd" for "Bread and Buns" are now correct
- ✅ **Live Product Counts**: Product counts reflect real-time availability

### 2. **Configuration-Based Settings**
- ✅ **Cache Disabled by Default**: `config.api.useCache: false`
- ✅ **3-Second API Delay**: `config.api.requestDelay: 3000`
- ✅ **Configurable Thresholds**: Default discount threshold is 75%

### 3. **Advanced Filtering System**

#### **Blacklist Keywords**
Items containing these keywords are automatically filtered out:
```json
"blacklistKeywords": [
  "test", "sample", "demo", "expired", 
  "unavailable", "out of stock", "not available"
]
```

#### **Custom Discount Thresholds**
Different product categories have different discount expectations:
```json
"customDiscountThresholds": {
  "premium": 50,    "organic": 40,     "imported": 30,
  "luxury": 25,     "branded": 35,     "fresh": 60,
  "frozen": 45,     "dairy": 55,       "bakery": 40,
  "electronics": 20, "appliances": 25,  "beauty": 35,
  "wellness": 40,   "baby": 45,        "toys": 35
}
```

### 4. **Enhanced Item URLs**
Every item now includes a direct Swiggy Instamart URL:
```
https://www.swiggy.com/stores/instamart/item/YOG8A0VWKV?share=true
```

## Usage Examples

### Basic Usage (No Cache)
```bash
node runner.js --category "Fresh Fruits" --filter "Seasonal Fruits" --threshold 20
```

### With Cache Enabled
```bash
node runner.js --category "Fresh Fruits" --filter "Seasonal Fruits" --threshold 20 --cache
```

### Using Mapping Codes
```bash
# Specific filter
node runner.js --code "A1" --threshold 30

# All filters in a category
node runner.js --code "A" --threshold 25
```

### List Available Options
```bash
# List all categories
node runner.js --list-categories

# List filters for a category
node runner.js --list-filters --category "Bath and Body"

# List all mapping codes
node runner.js --list-codes
```

## Output Format

Each item now shows:
```
1. Totapuri Raw Mango (Mavinahannu) (Totapuri Raw Mango (Mavinahannu))
   Original Price: ₹65
   Offer Price: ₹26
   Discount: 60.00% (Threshold: 10%)
   Quantity: 1 Piece
   ID: YOG8A0VWKV
   URL: https://www.swiggy.com/stores/instamart/item/YOG8A0VWKV?share=true
```

## Configuration File (config.json)

```json
{
  "api": {
    "requestDelay": 3000,
    "useCache": false,
    "defaultDiscountThreshold": 75
  },
  "filtering": {
    "blacklistKeywords": ["test", "sample", "demo", "expired"],
    "customDiscountThresholds": {
      "premium": 50,
      "organic": 40,
      "electronics": 20
    }
  },
  "urls": {
    "instamartItemBase": "https://www.swiggy.com/stores/instamart/item/",
    "shareParam": "?share=true"
  }
}
```

## API Integration

### Category Listing URL Format
```
https://www.swiggy.com/api/instamart/category-listing?categoryName=Home%20and%20Furnishing&storeId=1403687&offset=0&filterName=&primaryStoreId=1403687&secondaryStoreId=&taxonomyType=Health%20and%20Wellness%20Stores
```

### Filter Listing URL Format
```
https://www.swiggy.com/api/instamart/category-listing/filter?filterId=6822eeeded32000001e25abd&offset=0&storeId=1403687&primaryStoreId=1403687&secondaryStoreId=&type=Speciality%20taxonomy%201&pageNo=0&limit=20&filterName=Bread%20and%20Buns&categoryName=Dairy%2C%20Bread%20and%20Eggs
```

## Performance Improvements

- **Cache System**: Optional caching for faster repeated queries
- **Compression Support**: Handles gzip, deflate, and brotli compression
- **Smart Filtering**: Reduces irrelevant results with blacklist and custom thresholds
- **Real-time Data**: Always fetches current prices and availability

## Testing Results

✅ Successfully fetched real filter data for all 44 categories  
✅ Generated 568 real filter mappings  
✅ All mapping utilities work correctly  
✅ Discount item fetching works with real filter IDs  
✅ API requests return actual product data  
✅ Caching system functions properly  
✅ Custom thresholds apply correctly  
✅ Blacklist filtering works  
✅ Item URLs are properly generated  

## New Category-Only Mapping Feature

### **Category-Only Codes**
- ✅ **Single Letter Codes**: Use codes like "A", "B", "C" to fetch all filters for a category
- ✅ **Automatic Processing**: When using category-only codes, the system automatically processes all filters
- ✅ **Comprehensive Results**: Get discounted items from all filters in a category with one command

### **Mapping Structure**
```
A -> Appliances (ALL FILTERS)
A1 -> Appliances / Air Fryers
A2 -> Appliances / Blenders & Frothers
...
B -> Atta, Rice and Dal (ALL FILTERS)
B1 -> Atta, Rice and Dal / Atta
B2 -> Atta, Rice and Dal / Basmati Rice
...
```

### **Total Mappings**
- **44 Category-Only Codes**: A, B, C, ..., AR
- **568 Filter-Specific Codes**: A1, A2, B1, B2, etc.
- **612 Total Mappings**: Complete coverage of all categories and filters

### **Usage Examples**
```bash
# Get all discounted items from Appliances category
node runner.js --code "A" --threshold 20

# Get items from specific filter only
node runner.js --code "A1" --threshold 20

# List all category-only codes
node runner.js --list-codes | grep "ALL FILTERS"
```

The system is now production-ready with 100% real API data integration and comprehensive category-level processing capabilities.
