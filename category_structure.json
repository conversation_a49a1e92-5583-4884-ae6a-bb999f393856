{"GROCERY & KITCHEN": [{"id": "6822eeeded32000001e25aa1", "description": "Fresh Vegetables", "link": "swiggy://stores/instamart/category-listing?categoryName=Fresh Vegetables&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25aae", "description": "Fresh Fruits", "link": "swiggy://stores/instamart/category-listing?categoryName=Fresh Fruits&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25abb", "description": "Dairy, Bread and Eggs", "link": "swiggy://stores/instamart/category-listing?categoryName=Dairy, Bread and Eggs&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25aca", "description": "Cereals and Breakfast", "link": "swiggy://stores/instamart/category-listing?categoryName=Cereals and Breakfast&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25add", "description": "Atta, Rice and Dal", "link": "swiggy://stores/instamart/category-listing?categoryName=Atta, Rice and Dal&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25aec", "description": "Oils and Ghee", "link": "swiggy://stores/instamart/category-listing?categoryName=Oils and Ghee&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25af9", "description": "Masalas", "link": "swiggy://stores/instamart/category-listing?categoryName=Masalas&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25b06", "description": "Dry Fruits and Seeds Mix", "link": "swiggy://stores/instamart/category-listing?categoryName=Dry Fruits and Seeds Mix&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25b13", "description": "Biscuits and Cakes", "link": "swiggy://stores/instamart/category-listing?categoryName=Biscuits and Cakes&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25b24", "description": "Tea, Coffee and Milk drinks", "link": "swiggy://stores/instamart/category-listing?categoryName=Tea, Coffee and Milk drinks&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25b31", "description": "Sauces and Spreads", "link": "swiggy://stores/instamart/category-listing?categoryName=Sauces and Spreads&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}, {"id": "6822eeeded32000001e25b3c", "description": "Meat and Seafood", "link": "swiggy://stores/instamart/category-listing?categoryName=Meat and Seafood&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 1&showAgeConsent=false"}], "SNACKS & DRINKS": [{"id": "6822eeebed32000001e25a46", "description": "Cold Drinks and Juices", "link": "swiggy://stores/instamart/category-listing?categoryName=Cold Drinks and Juices&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false"}, {"id": "6822eeebed32000001e25a57", "description": "Ice Creams and Frozen Desserts", "link": "swiggy://stores/instamart/category-listing?categoryName=Ice Creams and Frozen Desserts&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false"}, {"id": "6822eeebed32000001e25a62", "description": "Chips and Namkeens", "link": "swiggy://stores/instamart/category-listing?categoryName=Chips and Namkeens&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false"}, {"id": "6822eeebed32000001e25a70", "description": "Chocolates", "link": "swiggy://stores/instamart/category-listing?categoryName=Chocolates&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false"}, {"id": "6822eeebed32000001e25a7c", "description": "Noodles, Pasta, <PERSON><PERSON><PERSON><PERSON>", "link": "swiggy://stores/instamart/category-listing?categoryName=Noodles, Pasta, Vermicelli&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false"}, {"id": "6822eeebed32000001e25a87", "description": "Frozen Food", "link": "swiggy://stores/instamart/category-listing?categoryName=Frozen Food&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false"}, {"id": "6822eeebed32000001e25a90", "description": "Sweets", "link": "swiggy://stores/instamart/category-listing?categoryName=Sweets&storeId=1403687&offset=0&filterName=&taxonomyType=Speciality taxonomy 3&showAgeConsent=false"}, {"id": "6822eeebed32000001e25a98", "description": "<PERSON><PERSON>", "link": "swiggy://ageConsent?url=swiggy%3A%2F%2Fstores%2Finstamart%2Fcategory-listing%3FcategoryName%3DPaan+Corner%26storeId%3D1403687%26offset%3D0%26filterName%3D%26taxonomyType%3DSpeciality+taxonomy+3%26showAgeConsent%3Dtrue"}], " BEAUTY & WELLNESS": [{"id": "6822eeefed32000001e25b4b", "description": "Bath and Body", "link": "swiggy://stores/instamart/category-listing?categoryName=Bath and Body&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25b59", "description": "Hair Care", "link": "swiggy://stores/instamart/category-listing?categoryName=Hair Care&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25b62", "description": "Skincare", "link": "swiggy://stores/instamart/category-listing?categoryName=Skincare&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25b6a", "description": "Makeup", "link": "swiggy://stores/instamart/category-listing?categoryName=Makeup&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25b71", "description": "Oral Care", "link": "swiggy://stores/instamart/category-listing?categoryName=Oral Care&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25b79", "description": "Grooming", "link": "swiggy://stores/instamart/category-listing?categoryName=Grooming&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25b89", "description": "Baby Care", "link": "swiggy://stores/instamart/category-listing?categoryName=Baby Care&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25b9a", "description": "Fragrances", "link": "swiggy://stores/instamart/category-listing?categoryName=Fragrances&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25ba2", "description": "Protein and Supplements", "link": "swiggy://stores/instamart/category-listing?categoryName=Protein and Supplements&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25baf", "description": "Feminine Hygiene", "link": "swiggy://stores/instamart/category-listing?categoryName=Feminine Hygiene&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25bb7", "description": "Sexual Wellness", "link": "swiggy://stores/instamart/category-listing?categoryName=Sexual Wellness&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}, {"id": "6822eeefed32000001e25bbf", "description": "Health and Pharma", "link": "swiggy://stores/instamart/category-listing?categoryName=Health and Pharma&storeId=1403687&offset=0&filterName=&taxonomyType=Supermarket&showAgeConsent=false"}], " HOUSEHOLD & LIFESTYLE": [{"id": "6822eef009ab2e00019aa5e2", "description": "Home and Furnishing", "link": "swiggy://stores/instamart/category-listing?categoryName=Home and Furnishing&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa5f3", "description": "Kitchen and Dining", "link": "swiggy://stores/instamart/category-listing?categoryName=Kitchen and Dining&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa601", "description": "Cleaning Essentials", "link": "swiggy://stores/instamart/category-listing?categoryName=Cleaning Essentials&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa614", "description": "Clothing", "link": "swiggy://stores/instamart/category-listing?categoryName=Clothing&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa621", "description": "Mobiles and Electronics ", "link": "swiggy://stores/instamart/category-listing?categoryName=Mobiles and Electronics &storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa630", "description": "Appliances", "link": "swiggy://stores/instamart/category-listing?categoryName=Appliances&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa63f", "description": "Books and Stationery", "link": "swiggy://stores/instamart/category-listing?categoryName=Books and Stationery&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa649", "description": "Jewellery and Accessories", "link": "swiggy://stores/instamart/category-listing?categoryName=Jewellery and Accessories&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa65d", "description": "<PERSON><PERSON><PERSON>", "link": "swiggy://stores/instamart/category-listing?categoryName=Puja&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa670", "description": "Toys and Games", "link": "swiggy://stores/instamart/category-listing?categoryName=Toys and Games&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa67f", "description": "Sports and Fitness", "link": "swiggy://stores/instamart/category-listing?categoryName=Sports and Fitness&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}, {"id": "6822eef009ab2e00019aa68e", "description": "Pet Supplies", "link": "swiggy://stores/instamart/category-listing?categoryName=Pet Supplies&storeId=1403687&offset=0&filterName=&taxonomyType=Health and Wellness Stores&showAgeConsent=false"}]}