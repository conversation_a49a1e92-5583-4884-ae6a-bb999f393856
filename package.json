{"name": "swiggy-deals-bot", "version": "1.0.0", "description": "Telegram bot for finding highly discounted items on Swiggy Instamart", "main": "startBot.js", "scripts": {"start": "node startBot.js", "bot": "node startBot.js", "api": "node api/server.js", "frontend": "cd frontend && npm start", "dev": "concurrently \"npm run api\" \"npm run frontend\" \"npm run bot\"", "build-frontend": "cd frontend && npm run build", "railway": "npm run api", "runner": "node runner.js", "test": "node runner.js --code A1 --threshold 50", "generate-mappings": "node generateCategoryFilterMapping.js", "fetch-filters": "node fetchCategoryFilters.js"}, "keywords": ["telegram", "bot", "swiggy", "deals", "discounts", "instamart", "shopping"], "author": "Your Name", "license": "MIT", "dependencies": {"concurrently": "^8.2.2", "cors": "^2.8.5", "express": "^4.18.2", "node-cron": "^4.2.1", "node-telegram-bot-api": "^0.64.0", "pg": "^8.16.3"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "your-repo-url"}}