# Node modules
node_modules/
frontend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
frontend/build/
dist/
build/

# Development files
.git/
.gitignore
README.md
FRONTEND_README.md
setup-frontend.sh

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Environment files (will be set by Railway)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Test files
coverage/
.nyc_output/

# Temporary files
tmp/
temp/
