const { Pool } = require('pg');

/**
 * Database connection for cron job persistence
 * Uses Railway's PostgreSQL database if available, otherwise falls back to in-memory storage
 */
class CronJobPersistence {
  constructor() {
    this.pool = null;
    this.isDbAvailable = false;
    this.initPromise = this.initializeDatabase();
  }

  /**
   * Wait for database initialization to complete
   */
  async waitForInitialization() {
    await this.initPromise;
  }

  /**
   * Initialize database connection
   */
  async initializeDatabase() {
    try {
      // Check if DATABASE_URL is available (Railway provides this automatically)
      if (process.env.DATABASE_URL) {
        this.pool = new Pool({
          connectionString: process.env.DATABASE_URL,
          ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
        });

        // Test connection
        const client = await this.pool.connect();
        await client.query('SELECT NOW()');
        client.release();

        // Create table if it doesn't exist
        await this.createTable();
        
        this.isDbAvailable = true;

      } else {
        console.log('⚠️ No DATABASE_URL found, using in-memory storage (cron jobs will not persist across restarts)');
      }
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      console.log('⚠️ Falling back to in-memory storage (cron jobs will not persist across restarts)');
      this.isDbAvailable = false;
    }
  }

  /**
   * Create cron jobs table and handle migrations
   */
  async createTable() {
    try {
      // Create tables one by one with proper error handling
      await this.createCronJobsTable();
      await this.createNotifiedDealsTable();
      await this.createKeywordThresholdsTable();
      await this.createMaxSaverGapTables();
      await this.createDealFinderTables();
      await this.createCronExecutionTables();
      await this.createDisplayedItemsTable();

      // Add new columns if they don't exist
      await this.addNewColumns();

      // Migrate old data to new format
      await this.migrateOldData();
    } catch (error) {
      console.error('❌ Error creating tables:', error.message);
      throw error;
    }
  }

  async createCronJobsTable() {
    const query = `
      CREATE TABLE IF NOT EXISTS cron_jobs (
        job_id VARCHAR(255) PRIMARY KEY,
        chat_id BIGINT NOT NULL,
        code VARCHAR(50),
        category_name VARCHAR(255),
        filter_name VARCHAR(255),
        is_category_only BOOLEAN DEFAULT FALSE,
        interval_minutes INTEGER,
        schedule_type VARCHAR(20) DEFAULT 'interval',
        schedule_time VARCHAR(10),
        threshold INTEGER NOT NULL,
        start_time BIGINT NOT NULL,
        total_items_found INTEGER DEFAULT 0,
        execution_count INTEGER DEFAULT 0,
        failed_executions INTEGER DEFAULT 0,
        last_run BIGINT,
        api_type VARCHAR(20) DEFAULT 'existing',
        price_calculation VARCHAR(20) DEFAULT 'offer',
        hide_repeated_items BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    await this.pool.query(query);

    // Create index separately to avoid conflicts
    try {
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_cron_jobs_chat_id ON cron_jobs(chat_id);');
    } catch (error) {
      // Index might already exist, ignore
    }
  }

  async createNotifiedDealsTable() {
    const query = `
      CREATE TABLE IF NOT EXISTS notified_deals (
        id SERIAL PRIMARY KEY,
        chat_id BIGINT NOT NULL,
        item_id VARCHAR(255) NOT NULL,
        item_name TEXT NOT NULL,
        discount_percentage DECIMAL(5,2) NOT NULL,
        offer_price DECIMAL(10,2) NOT NULL,
        first_notified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_notified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        notification_count INTEGER DEFAULT 1,
        UNIQUE(chat_id, item_id)
      );
    `;
    await this.pool.query(query);

    // Create indexes separately
    try {
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_notified_deals_chat_id ON notified_deals(chat_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_notified_deals_item_id ON notified_deals(item_id);');
    } catch (error) {
      // Indexes might already exist, ignore
    }
  }

  async createKeywordThresholdsTable() {
    const query = `
      CREATE TABLE IF NOT EXISTS keyword_thresholds (
        id SERIAL PRIMARY KEY,
        chat_id BIGINT NOT NULL,
        keyword VARCHAR(255) NOT NULL,
        threshold_percentage DECIMAL(5,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(chat_id, keyword)
      );
    `;
    await this.pool.query(query);

    try {
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_keyword_thresholds_chat_id ON keyword_thresholds(chat_id);');
    } catch (error) {
      // Index might already exist, ignore
    }
  }

  async createMaxSaverGapTables() {
    // Create runs table
    const runsQuery = `
      CREATE TABLE IF NOT EXISTS max_saver_gap_runs (
        id SERIAL PRIMARY KEY,
        run_id VARCHAR(255) UNIQUE NOT NULL,
        baseline VARCHAR(20) NOT NULL,
        threshold_percentage DECIMAL(5,2) NOT NULL,
        limit_per_category INTEGER NOT NULL,
        total_categories INTEGER DEFAULT 0,
        total_items_found INTEGER DEFAULT 0,
        execution_time_ms INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP,
        status VARCHAR(20) DEFAULT 'running'
      );
    `;
    await this.pool.query(runsQuery);

    // Create results table
    const resultsQuery = `
      CREATE TABLE IF NOT EXISTS max_saver_gap_results (
        id SERIAL PRIMARY KEY,
        run_id VARCHAR(255) NOT NULL,
        category_name VARCHAR(255) NOT NULL,
        filter_name VARCHAR(255),
        item_name TEXT NOT NULL,
        product_id VARCHAR(255),
        baseline_price DECIMAL(10,2) NOT NULL,
        max_saver_price DECIMAL(10,2) NOT NULL,
        gap_percentage DECIMAL(5,2) NOT NULL,
        quantity VARCHAR(100),
        unit VARCHAR(50),
        item_url TEXT,
        source VARCHAR(20) DEFAULT 'variation',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    await this.pool.query(resultsQuery);

    // Create indexes separately
    try {
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_max_saver_gap_runs_created_at ON max_saver_gap_runs(created_at);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_max_saver_gap_runs_status ON max_saver_gap_runs(status);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_max_saver_gap_results_run_id ON max_saver_gap_results(run_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_max_saver_gap_results_category ON max_saver_gap_results(category_name);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_max_saver_gap_results_gap_percentage ON max_saver_gap_results(gap_percentage);');
    } catch (error) {
      // Indexes might already exist, ignore
    }
  }

  async createDealFinderTables() {
    // Create runs table
    const runsQuery = `
      CREATE TABLE IF NOT EXISTS deal_finder_runs (
        id SERIAL PRIMARY KEY,
        run_id VARCHAR(255) UNIQUE NOT NULL,
        codes TEXT NOT NULL,
        threshold_percentage DECIMAL(5,2) NOT NULL,
        api_type VARCHAR(20) DEFAULT 'existing',
        price_calculation VARCHAR(20) DEFAULT 'offer',
        total_items_found INTEGER DEFAULT 0,
        execution_time_ms INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP,
        status VARCHAR(20) DEFAULT 'running'
      );
    `;
    await this.pool.query(runsQuery);

    // Create results table
    const resultsQuery = `
      CREATE TABLE IF NOT EXISTS deal_finder_results (
        id SERIAL PRIMARY KEY,
        run_id VARCHAR(255) NOT NULL,
        code VARCHAR(50) NOT NULL,
        category_name VARCHAR(255),
        filter_name VARCHAR(255),
        item_name TEXT NOT NULL,
        variation_name TEXT,
        product_id VARCHAR(255),
        store_price DECIMAL(10,2) NOT NULL,
        offer_price DECIMAL(10,2) NOT NULL,
        discount_percentage DECIMAL(5,2) NOT NULL,
        quantity VARCHAR(100),
        unit VARCHAR(50),
        item_url TEXT,
        applied_threshold DECIMAL(5,2) NOT NULL,
        in_stock BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    await this.pool.query(resultsQuery);

    // Create indexes separately
    try {
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_deal_finder_runs_created_at ON deal_finder_runs(created_at);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_deal_finder_runs_status ON deal_finder_runs(status);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_deal_finder_results_run_id ON deal_finder_results(run_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_deal_finder_results_code ON deal_finder_results(code);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_deal_finder_results_discount_percentage ON deal_finder_results(discount_percentage);');
    } catch (error) {
      // Indexes might already exist, ignore
    }
  }

  async createCronExecutionTables() {
    // Create cron execution runs table
    const executionRunsQuery = `
      CREATE TABLE IF NOT EXISTS cron_execution_runs (
        id SERIAL PRIMARY KEY,
        execution_id VARCHAR(255) UNIQUE NOT NULL,
        job_id VARCHAR(255) NOT NULL,
        chat_id BIGINT NOT NULL,
        codes TEXT NOT NULL,
        threshold_percentage DECIMAL(5,2) NOT NULL,
        api_type VARCHAR(20) DEFAULT 'existing',
        price_calculation VARCHAR(20) DEFAULT 'offer',
        total_items_found INTEGER DEFAULT 0,
        new_items_found INTEGER DEFAULT 0,
        skipped_items INTEGER DEFAULT 0,
        keyword_filtered_items INTEGER DEFAULT 0,
        execution_time_ms INTEGER DEFAULT 0,
        success BOOLEAN DEFAULT true,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP
      );
    `;
    await this.pool.query(executionRunsQuery);

    // Create cron execution results table for individual items
    const executionResultsQuery = `
      CREATE TABLE IF NOT EXISTS cron_execution_results (
        id SERIAL PRIMARY KEY,
        execution_id VARCHAR(255) NOT NULL,
        job_id VARCHAR(255) NOT NULL,
        chat_id BIGINT NOT NULL,
        item_name TEXT NOT NULL,
        variation_name TEXT,
        product_id VARCHAR(255),
        store_price DECIMAL(10,2) NOT NULL,
        offer_price DECIMAL(10,2) NOT NULL,
        discount_percentage DECIMAL(5,2) NOT NULL,
        quantity VARCHAR(100),
        unit VARCHAR(50),
        item_url TEXT,
        applied_threshold DECIMAL(5,2) NOT NULL,
        was_notified BOOLEAN DEFAULT false,
        in_stock BOOLEAN DEFAULT true,
        is_new BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `;
    await this.pool.query(executionResultsQuery);

    // Create indexes
    try {
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_cron_execution_runs_job_id ON cron_execution_runs(job_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_cron_execution_runs_chat_id ON cron_execution_runs(chat_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_cron_execution_runs_created_at ON cron_execution_runs(created_at);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_cron_execution_results_execution_id ON cron_execution_results(execution_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_cron_execution_results_job_id ON cron_execution_results(job_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_cron_execution_results_chat_id ON cron_execution_results(chat_id);');
    } catch (error) {
      // Indexes might already exist, ignore
    }
  }

  async createDisplayedItemsTable() {
    // Create displayed items tracking table
    const displayedItemsQuery = `
      CREATE TABLE IF NOT EXISTS displayed_items (
        id SERIAL PRIMARY KEY,
        job_id VARCHAR(255),
        chat_id BIGINT,
        product_id VARCHAR(255),
        item_name TEXT NOT NULL,
        last_store_price DECIMAL(10,2),
        last_offer_price DECIMAL(10,2),
        last_max_saver_price DECIMAL(10,2),
        last_discount_percentage DECIMAL(5,2),
        last_displayed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        display_count INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(job_id, product_id),
        UNIQUE(chat_id, product_id)
      );
    `;
    await this.pool.query(displayedItemsQuery);

    // Create indexes
    try {
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_displayed_items_job_id ON displayed_items(job_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_displayed_items_chat_id ON displayed_items(chat_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_displayed_items_product_id ON displayed_items(product_id);');
      await this.pool.query('CREATE INDEX IF NOT EXISTS idx_displayed_items_last_displayed_at ON displayed_items(last_displayed_at);');
    } catch (error) {
      // Indexes might already exist, ignore
    }
  }

  /**
   * Add new columns for multi-code support
   */
  async addNewColumns() {
    try {
      // Add codes column
      await this.pool.query(`
        ALTER TABLE cron_jobs
        ADD COLUMN IF NOT EXISTS codes TEXT
      `);

      // Add is_multiple_codes column
      await this.pool.query(`
        ALTER TABLE cron_jobs
        ADD COLUMN IF NOT EXISTS is_multiple_codes BOOLEAN DEFAULT FALSE
      `);

      // Add API type column
      await this.pool.query(`
        ALTER TABLE cron_jobs
        ADD COLUMN IF NOT EXISTS api_type VARCHAR(20) DEFAULT 'existing'
      `);

      // Add price calculation column
      await this.pool.query(`
        ALTER TABLE cron_jobs
        ADD COLUMN IF NOT EXISTS price_calculation VARCHAR(20) DEFAULT 'offer'
      `);

      // Add API type column to deal_finder_runs
      await this.pool.query(`
        ALTER TABLE deal_finder_runs
        ADD COLUMN IF NOT EXISTS api_type VARCHAR(20) DEFAULT 'existing'
      `);

      // Add price calculation column to deal_finder_runs
      await this.pool.query(`
        ALTER TABLE deal_finder_runs
        ADD COLUMN IF NOT EXISTS price_calculation VARCHAR(20) DEFAULT 'offer'
      `);

      // Add hide repeated items column to cron_jobs
      await this.pool.query(`
        ALTER TABLE cron_jobs
        ADD COLUMN IF NOT EXISTS hide_repeated_items BOOLEAN DEFAULT false
      `);

      // Add is_new column to cron_execution_results
      await this.pool.query(`
        ALTER TABLE cron_execution_results
        ADD COLUMN IF NOT EXISTS is_new BOOLEAN DEFAULT false
      `);

      // Add missing columns to cron_execution_runs (for backward compatibility)
      await this.pool.query(`
        ALTER TABLE cron_execution_runs
        ADD COLUMN IF NOT EXISTS api_type VARCHAR(20) DEFAULT 'existing'
      `);

      await this.pool.query(`
        ALTER TABLE cron_execution_runs
        ADD COLUMN IF NOT EXISTS price_calculation VARCHAR(20) DEFAULT 'offer'
      `);

      console.log('✅ Added new columns to cron_jobs, deal_finder_runs and cron_execution_runs tables');
    } catch (error) {
      console.error('❌ Error adding new columns:', error.message);
    }
  }

  /**
   * Migrate old single-code data to new multi-code format
   */
  async migrateOldData() {
    try {
      // Update rows where codes is null but code exists
      const migrationQuery = `
        UPDATE cron_jobs
        SET codes = code, is_multiple_codes = FALSE
        WHERE codes IS NULL AND code IS NOT NULL
      `;

      const result = await this.pool.query(migrationQuery);

    } catch (error) {
      console.error('❌ Error migrating old cron job data:', error.message);
    }
  }

  /**
   * Check if a job ID already exists
   */
  async jobExists(jobId) {
    if (!this.isDbAvailable) {
      return false;
    }

    try {
      const result = await this.pool.query('SELECT job_id FROM cron_jobs WHERE job_id = $1', [jobId]);
      return result.rows.length > 0;
    } catch (error) {
      console.error('Error checking if job exists:', error.message);
      return false;
    }
  }

  /**
   * Generate a category name from codes
   */
  generateCategoryName(codes) {
    if (!codes || codes.length === 0) {
      return 'Unknown';
    }

    try {
      const mappingUtil = require('./categoryFilterMappingUtil');

      // Try to get category names for the codes
      const categoryNames = [];

      for (const code of codes) {
        try {
          // Try to find the category name for this code
          const categories = mappingUtil.getAllCategories();

          for (const categoryName of categories) {
            const categoryCode = mappingUtil.namesToCode(categoryName);
            if (categoryCode === code) {
              categoryNames.push(categoryName);
              break;
            }

            // Check filters
            const filters = mappingUtil.getFiltersForCategory(categoryName);
            for (const filterName of filters) {
              const filterCode = mappingUtil.namesToCode(categoryName, filterName);
              if (filterCode === code) {
                categoryNames.push(`${categoryName}/${filterName}`);
                break;
              }
            }
          }
        } catch (error) {
          // If we can't resolve the code, just use the code itself
          categoryNames.push(code);
        }
      }

      return categoryNames.length > 0 ? categoryNames.join(', ') : codes.join(', ');
    } catch (error) {
      console.error('Error generating category name:', error);
      return codes.join(', ');
    }
  }

  /**
   * Save a cron job to database
   */
  async saveCronJob(jobData) {
    if (!this.isDbAvailable) {
      console.log('⚠️ Database not available, skipping cron job save');
      return;
    }

    // Check if job already exists
    const exists = await this.jobExists(jobData.jobId);

    const codesArray = Array.isArray(jobData.codes) ? jobData.codes : [jobData.codes];
    const codesString = codesArray.join(',');
    const firstCode = codesArray[0] || ''; // For legacy code column

    // Generate category name from codes
    const categoryName = this.generateCategoryName(codesArray);

    // Determine if this is a category-only job (check first code)
    const mappingUtil = require('./categoryFilterMappingUtil');
    const isCategoryOnly = firstCode ? mappingUtil.isCategoryOnlyCode(firstCode) : false;

    const query = `
      INSERT INTO cron_jobs (
        job_id, chat_id, code, codes, is_multiple_codes, category_name, is_category_only,
        interval_minutes, schedule_type, schedule_time, threshold, start_time,
        total_items_found, execution_count, failed_executions, last_run,
        api_type, price_calculation, hide_repeated_items
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
      ON CONFLICT (job_id) DO UPDATE SET
        total_items_found = EXCLUDED.total_items_found,
        execution_count = EXCLUDED.execution_count,
        failed_executions = EXCLUDED.failed_executions,
        last_run = EXCLUDED.last_run,
        api_type = EXCLUDED.api_type,
        price_calculation = EXCLUDED.price_calculation,
        hide_repeated_items = EXCLUDED.hide_repeated_items,
        updated_at = CURRENT_TIMESTAMP
    `;

    const values = [
      jobData.jobId,
      jobData.chatId,
      firstCode, // Legacy code column
      codesString, // New codes column
      jobData.isMultipleCodes || false,
      categoryName, // Generated category name
      isCategoryOnly, // Is category-only flag
      jobData.intervalMinutes || null,
      jobData.scheduleType || 'interval',
      jobData.scheduleTime || null,
      jobData.threshold,
      jobData.startTime,
      jobData.totalItemsFound || 0,
      jobData.executionCount || 0,
      jobData.failedExecutions || 0,
      jobData.lastRun || null,
      jobData.apiType || 'existing',
      jobData.priceCalculation || 'offer',
      jobData.hideRepeatedItems || false
    ];

    try {
      await this.pool.query(query, values);
    } catch (error) {
      console.error(`❌ Error saving cron job ${jobData.jobId}:`, error.message);
      console.error('Query values:', values);
    }
  }

  /**
   * Get total count of cron jobs
   */
  async getCronJobCount() {
    if (!this.isDbAvailable) {
      return 0;
    }

    try {
      const result = await this.pool.query('SELECT COUNT(*) as count FROM cron_jobs');
      return parseInt(result.rows[0].count);
    } catch (error) {
      console.error('Error getting cron job count:', error.message);
      return 0;
    }
  }

  /**
   * Load all cron jobs from database
   */
  async loadCronJobs() {
    if (!this.isDbAvailable) {
      console.log('⚠️ Database not available, no cron jobs to load');
      return [];
    }

    try {
      const result = await this.pool.query('SELECT * FROM cron_jobs ORDER BY created_at');

      return result.rows.map(row => {
        // Handle both old and new data formats
        let codes = [];
        if (row.codes) {
          codes = row.codes.split(',').map(code => code.trim()).filter(code => code.length > 0);
        } else if (row.code) {
          codes = [row.code];
        }

        return {
          jobId: row.job_id,
          chatId: parseInt(row.chat_id), // Ensure chatId is a number
          codes: codes,
          isMultipleCodes: codes.length > 1,
          categoryName: row.category_name || codes.join(', '),
          intervalMinutes: row.interval_minutes,
          scheduleType: row.schedule_type || 'interval',
          scheduleTime: row.schedule_time,
          threshold: row.threshold,
          startTime: parseInt(row.start_time),
          totalItemsFound: row.total_items_found,
          executionCount: row.execution_count,
          failedExecutions: row.failed_executions,
          lastRun: row.last_run ? parseInt(row.last_run) : null,
          apiType: row.api_type || 'existing',
          priceCalculation: row.price_calculation || 'offer',
          hideRepeatedItems: row.hide_repeated_items || false
        };
      });
    } catch (error) {
      console.error('❌ Error loading cron jobs:', error.message);
      return [];
    }
  }

  /**
   * Delete a cron job from database
   */
  async deleteCronJob(jobId) {
    if (!this.isDbAvailable) {
      console.log('⚠️ Database not available, skipping cron job deletion');
      return;
    }

    try {
      await this.pool.query('DELETE FROM cron_jobs WHERE job_id = $1', [jobId]);
    } catch (error) {
      console.error(`❌ Error deleting cron job ${jobId}:`, error.message);
    }
  }

  /**
   * Update cron job statistics
   */
  async updateCronJobStats(jobId, stats) {
    if (!this.isDbAvailable) {
      return;
    }

    const query = `
      UPDATE cron_jobs SET
        total_items_found = $2,
        execution_count = $3,
        failed_executions = $4,
        last_run = $5,
        updated_at = CURRENT_TIMESTAMP
      WHERE job_id = $1
    `;

    const values = [
      jobId,
      stats.totalItemsFound || 0,
      stats.executionCount || 0,
      stats.failedExecutions || 0,
      stats.lastRun || null
    ];

    try {
      await this.pool.query(query, values);
    } catch (error) {
      console.error(`❌ Error updating cron job stats ${jobId}:`, error.message);
    }
  }

  /**
   * Clean up invalid cron jobs (no codes)
   */
  async cleanupInvalidJobs() {
    if (!this.isDbAvailable) {
      return;
    }

    try {
      const deleteQuery = `
        DELETE FROM cron_jobs
        WHERE (codes IS NULL OR codes = '') AND (code IS NULL OR code = '')
      `;

      const result = await this.pool.query(deleteQuery);

    } catch (error) {
      console.error('❌ Error cleaning up invalid cron jobs:', error.message);
    }
  }

  /**
   * Check if a deal should be notified (new deal or discount changed)
   */
  async shouldNotifyDeal(chatId, item, threshold) {
    if (!this.isDbAvailable) {
      return true; // If no DB, always notify
    }

    try {
      const query = `
        SELECT discount_percentage, offer_price, notification_count
        FROM notified_deals
        WHERE chat_id = $1 AND item_id = $2
      `;

      const result = await this.pool.query(query, [chatId, item.id]);

      if (result.rows.length === 0) {
        // New deal, should notify
        return true;
      }

      const previousDeal = result.rows[0];
      const currentDiscount = parseFloat(item.discountPercentage);
      const previousDiscount = parseFloat(previousDeal.discount_percentage);
      const currentPrice = parseFloat(item.offerPrice);
      const previousPrice = parseFloat(previousDeal.offer_price);

      // Notify if:
      // 1. Discount percentage changed by more than 1% AND still above threshold
      // 2. Price changed significantly
      const discountChanged = Math.abs(currentDiscount - previousDiscount) >= 1;
      const priceChanged = Math.abs(currentPrice - previousPrice) >= 1;
      const stillAboveThreshold = currentDiscount >= threshold;

      return (discountChanged && stillAboveThreshold) || priceChanged;
    } catch (error) {
      console.error('❌ Error checking deal notification status:', error.message);
      return true; // On error, default to notify
    }
  }

  /**
   * Record that a deal has been notified
   */
  async recordNotifiedDeal(chatId, item) {
    if (!this.isDbAvailable) {
      return;
    }

    try {
      const query = `
        INSERT INTO notified_deals (
          chat_id, item_id, item_name, discount_percentage, offer_price
        ) VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (chat_id, item_id) DO UPDATE SET
          discount_percentage = EXCLUDED.discount_percentage,
          offer_price = EXCLUDED.offer_price,
          last_notified_at = CURRENT_TIMESTAMP,
          notification_count = notified_deals.notification_count + 1
      `;

      const values = [
        chatId,
        item.id,
        item.name,
        parseFloat(item.discountPercentage),
        parseFloat(item.offerPrice)
      ];

      await this.pool.query(query, values);
    } catch (error) {
      console.error('❌ Error recording notified deal:', error.message);
    }
  }

  /**
   * Clean up old notified deals (older than 30 days)
   */
  async cleanupOldNotifiedDeals() {
    if (!this.isDbAvailable) {
      return;
    }

    try {
      const query = `
        DELETE FROM notified_deals
        WHERE last_notified_at < NOW() - INTERVAL '30 days'
      `;

      const result = await this.pool.query(query);

    } catch (error) {
      console.error('❌ Error cleaning up old notified deals:', error.message);
    }
  }

  /**
   * Get notification statistics for a user
   */
  async getNotificationStats(chatId) {
    if (!this.isDbAvailable) {
      return null;
    }

    try {
      const query = `
        SELECT
          COUNT(*) as total_deals,
          COUNT(CASE WHEN notification_count > 1 THEN 1 END) as repeated_deals,
          AVG(notification_count) as avg_notifications_per_deal
        FROM notified_deals
        WHERE chat_id = $1
      `;

      const result = await this.pool.query(query, [chatId]);
      return result.rows[0] || null;
    } catch (error) {
      console.error('❌ Error getting notification stats:', error.message);
      return null;
    }
  }

  /**
   * Add or update keyword threshold
   */
  async setKeywordThreshold(chatId, keyword, threshold) {
    if (!this.isDbAvailable) {
      return false;
    }

    try {
      const query = `
        INSERT INTO keyword_thresholds (chat_id, keyword, threshold_percentage)
        VALUES ($1, $2, $3)
        ON CONFLICT (chat_id, keyword) DO UPDATE SET
          threshold_percentage = EXCLUDED.threshold_percentage
      `;

      await this.pool.query(query, [chatId, keyword.toLowerCase(), threshold]);
      return true;
    } catch (error) {
      console.error('❌ Error setting keyword threshold:', error.message);
      return false;
    }
  }

  /**
   * Get all keyword thresholds for a user
   */
  async getKeywordThresholds(chatId) {
    if (!this.isDbAvailable) {
      return [];
    }

    try {
      const query = `
        SELECT keyword, threshold_percentage
        FROM keyword_thresholds
        WHERE chat_id = $1
        ORDER BY keyword
      `;

      const result = await this.pool.query(query, [chatId]);
      return result.rows.map(row => ({
        keyword: row.keyword,
        threshold: parseFloat(row.threshold_percentage)
      }));
    } catch (error) {
      console.error('❌ Error getting keyword thresholds:', error.message);
      return [];
    }
  }

  /**
   * Delete keyword threshold
   */
  async deleteKeywordThreshold(chatId, keyword) {
    if (!this.isDbAvailable) {
      return false;
    }

    try {
      const query = `
        DELETE FROM keyword_thresholds
        WHERE chat_id = $1 AND keyword = $2
      `;

      const result = await this.pool.query(query, [chatId, keyword.toLowerCase()]);
      return result.rowCount > 0;
    } catch (error) {
      console.error('❌ Error deleting keyword threshold:', error.message);
      return false;
    }
  }

  /**
   * Check if item meets keyword-based threshold requirements
   */
  async checkKeywordThresholds(chatId, item) {
    if (!this.isDbAvailable) {
      return true; // If no DB, don't filter
    }

    try {
      const keywordThresholds = await this.getKeywordThresholds(chatId);
      if (keywordThresholds.length === 0) {
        return true; // No keyword rules, allow item
      }

      const itemName = item.name.toLowerCase();
      const itemDiscount = parseFloat(item.discountPercentage);

      // Check if any keyword matches and if so, verify threshold
      for (const rule of keywordThresholds) {
        if (itemName.includes(rule.keyword)) {
          if (itemDiscount < rule.threshold) {
            return false; // Keyword found but threshold not met
          }
        }
      }

      return true; // Either no keywords match or all thresholds are met
    } catch (error) {
      console.error('❌ Error checking keyword thresholds:', error.message);
      return true; // On error, don't filter
    }
  }

  /**
   * Delete a cron job
   */
  async deleteCronJob(jobId) {
    if (!this.isDbAvailable) {
      return false;
    }

    try {
      const query = `DELETE FROM cron_jobs WHERE job_id = $1`;
      const result = await this.pool.query(query, [jobId]);
      return result.rowCount > 0;
    } catch (error) {
      console.error('❌ Error deleting cron job:', error.message);
      return false;
    }
  }

  /**
   * Save max saver gap run
   */
  async saveMaxSaverGapRun(runData) {
    if (!this.isDbAvailable) {
      return false;
    }

    try {
      const query = `
        INSERT INTO max_saver_gap_runs (
          run_id, baseline, threshold_percentage, limit_per_category,
          total_categories, total_items_found, execution_time_ms, status, completed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (run_id) DO UPDATE SET
          total_categories = EXCLUDED.total_categories,
          total_items_found = EXCLUDED.total_items_found,
          execution_time_ms = EXCLUDED.execution_time_ms,
          status = EXCLUDED.status,
          completed_at = EXCLUDED.completed_at
      `;

      const values = [
        runData.runId,
        runData.baseline,
        runData.threshold,
        runData.limit,
        runData.totalCategories || 0,
        runData.totalItemsFound || 0,
        runData.executionTimeMs || 0,
        runData.status || 'completed',
        runData.completedAt || new Date()
      ];

      await this.pool.query(query, values);
      return true;
    } catch (error) {
      console.error('❌ Error saving max saver gap run:', error.message);
      return false;
    }
  }

  /**
   * Save max saver gap results
   */
  async saveMaxSaverGapResults(runId, results) {
    if (!this.isDbAvailable || !results || results.length === 0) {
      return false;
    }

    try {
      // Use batch insert for better performance
      const values = [];
      const placeholders = [];

      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        const baseIndex = i * 12;
        placeholders.push(`($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6}, $${baseIndex + 7}, $${baseIndex + 8}, $${baseIndex + 9}, $${baseIndex + 10}, $${baseIndex + 11}, $${baseIndex + 12})`);

        values.push(
          runId,
          result.categoryName,
          result.filterName || null,
          result.itemName,
          result.productId || null,
          result.baselinePrice,
          result.maxSaverPrice,
          result.gapPercent,
          result.quantity || null,
          result.unit || null,
          result.url || null,
          result.source || 'variation'
        );
      }

      const query = `
        INSERT INTO max_saver_gap_results (
          run_id, category_name, filter_name, item_name, product_id,
          baseline_price, max_saver_price, gap_percentage, quantity, unit,
          item_url, source
        ) VALUES ${placeholders.join(', ')}
      `;

      await this.pool.query(query, values);
      return true;
    } catch (error) {
      console.error('❌ Error saving max saver gap results:', error.message);
      return false;
    }
  }

  /**
   * Save deal finder run
   */
  async saveDealFinderRun(runData) {
    if (!this.isDbAvailable) {
      return false;
    }

    try {
      const query = `
        INSERT INTO deal_finder_runs (
          run_id, codes, threshold_percentage, api_type, price_calculation,
          total_items_found, execution_time_ms, status, completed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        ON CONFLICT (run_id) DO UPDATE SET
          total_items_found = EXCLUDED.total_items_found,
          execution_time_ms = EXCLUDED.execution_time_ms,
          status = EXCLUDED.status,
          completed_at = EXCLUDED.completed_at,
          api_type = EXCLUDED.api_type,
          price_calculation = EXCLUDED.price_calculation
      `;

      const values = [
        runData.runId,
        runData.codes,
        runData.threshold,
        runData.apiType || 'existing',
        runData.priceCalculation || 'offer',
        runData.totalItemsFound || 0,
        runData.executionTimeMs || 0,
        runData.status || 'completed',
        runData.completedAt || new Date()
      ];

      await this.pool.query(query, values);
      return true;
    } catch (error) {
      console.error('❌ Error saving deal finder run:', error.message);
      return false;
    }
  }

  /**
   * Save deal finder results
   */
  async saveDealFinderResults(runId, results) {
    if (!this.isDbAvailable || !results || results.length === 0) {
      return false;
    }

    try {
      // Use batch insert for better performance
      const values = [];
      const placeholders = [];

      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        const baseIndex = i * 15;
        placeholders.push(`($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6}, $${baseIndex + 7}, $${baseIndex + 8}, $${baseIndex + 9}, $${baseIndex + 10}, $${baseIndex + 11}, $${baseIndex + 12}, $${baseIndex + 13}, $${baseIndex + 14}, $${baseIndex + 15})`);

        values.push(
          runId,
          result.code || null,
          result.categoryName || null,
          result.filterName || null,
          result.name,
          result.variation || null,
          result.id || null,
          result.storePrice,
          result.offerPrice,
          result.discountPercentage,
          result.quantity || null,
          result.unit || null,
          result.url || null,
          result.appliedThreshold,
          result.inStock === true
        );
      }

      const query = `
        INSERT INTO deal_finder_results (
          run_id, code, category_name, filter_name, item_name, variation_name,
          product_id, store_price, offer_price, discount_percentage, quantity,
          unit, item_url, applied_threshold, in_stock
        ) VALUES ${placeholders.join(', ')}
      `;

      await this.pool.query(query, values);
      return true;
    } catch (error) {
      console.error('❌ Error saving deal finder results:', error.message);
      return false;
    }
  }

  /**
   * Get max saver gap runs with pagination
   */
  async getMaxSaverGapRuns(limit = 50, offset = 0) {
    if (!this.isDbAvailable) {
      return { runs: [], total: 0 };
    }

    try {
      const countQuery = 'SELECT COUNT(*) as count FROM max_saver_gap_runs';
      const countResult = await this.pool.query(countQuery);
      const total = parseInt(countResult.rows[0].count);

      const query = `
        SELECT * FROM max_saver_gap_runs
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2
      `;
      const result = await this.pool.query(query, [limit, offset]);

      return { runs: result.rows, total };
    } catch (error) {
      console.error('❌ Error getting max saver gap runs:', error.message);
      return { runs: [], total: 0 };
    }
  }

  /**
   * Get a specific max saver gap run by ID
   */
  async getMaxSaverGapRunById(runId) {
    if (!this.isDbAvailable) {
      return null;
    }

    try {
      const query = 'SELECT * FROM max_saver_gap_runs WHERE run_id = $1';
      const result = await this.pool.query(query, [runId]);

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      console.error('❌ Error getting max saver gap run by ID:', error.message);
      return null;
    }
  }

  /**
   * Get deal finder runs with pagination
   */
  async getDealFinderRuns(limit = 50, offset = 0) {
    if (!this.isDbAvailable) {
      return { runs: [], total: 0 };
    }

    try {
      const countQuery = 'SELECT COUNT(*) as count FROM deal_finder_runs';
      const countResult = await this.pool.query(countQuery);
      const total = parseInt(countResult.rows[0].count);

      const query = `
        SELECT * FROM deal_finder_runs
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2
      `;
      const result = await this.pool.query(query, [limit, offset]);

      return { runs: result.rows, total };
    } catch (error) {
      console.error('❌ Error getting deal finder runs:', error.message);
      return { runs: [], total: 0 };
    }
  }

  /**
   * Get a specific deal finder run by ID
   */
  async getDealFinderRunById(runId) {
    if (!this.isDbAvailable) {
      return null;
    }

    try {
      const query = 'SELECT * FROM deal_finder_runs WHERE run_id = $1';
      const result = await this.pool.query(query, [runId]);

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      console.error('❌ Error getting deal finder run by ID:', error.message);
      return null;
    }
  }

  /**
   * Get max saver gap results for a specific run
   */
  async getMaxSaverGapResults(runId, limit = 100, offset = 0) {
    if (!this.isDbAvailable) {
      return { results: [], total: 0 };
    }

    try {
      const countQuery = 'SELECT COUNT(*) as count FROM max_saver_gap_results WHERE run_id = $1';
      const countResult = await this.pool.query(countQuery, [runId]);
      const total = parseInt(countResult.rows[0].count);

      const query = `
        SELECT * FROM max_saver_gap_results
        WHERE run_id = $1
        ORDER BY gap_percentage DESC, created_at DESC
        LIMIT $2 OFFSET $3
      `;
      const result = await this.pool.query(query, [runId, limit, offset]);

      return { results: result.rows, total };
    } catch (error) {
      console.error('❌ Error getting max saver gap results:', error.message);
      return { results: [], total: 0 };
    }
  }

  /**
   * Get deal finder results for a specific run
   */
  async getDealFinderResults(runId, limit = 100, offset = 0) {
    if (!this.isDbAvailable) {
      return { results: [], total: 0 };
    }

    try {
      const countQuery = 'SELECT COUNT(*) as count FROM deal_finder_results WHERE run_id = $1';
      const countResult = await this.pool.query(countQuery, [runId]);
      const total = parseInt(countResult.rows[0].count);

      const query = `
        SELECT * FROM deal_finder_results
        WHERE run_id = $1
        ORDER BY discount_percentage DESC, created_at DESC
        LIMIT $2 OFFSET $3
      `;
      const result = await this.pool.query(query, [runId, limit, offset]);

      return { results: result.rows, total };
    } catch (error) {
      console.error('❌ Error getting deal finder results:', error.message);
      return { results: [], total: 0 };
    }
  }

  /**
   * Save cron job execution results to database
   */
  async saveCronJobExecution(executionData) {
    if (!this.isDbAvailable) {
      console.log('⚠️ Database not available, skipping execution save');
      return false;
    }

    try {
      // Generate execution ID if not provided
      const executionId = executionData.executionId || `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const query = `
        INSERT INTO cron_job_executions (
          execution_id, job_id, success, total_items_found, new_items_found, 
          skipped_items, threshold_percentage, codes, api_type, price_calculation,
          execution_time_ms, error_message, completed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING execution_id
      `;

      const values = [
        executionId,
        executionData.jobId,
        executionData.success !== false,
        executionData.totalItemsFound || 0,
        executionData.newItemsFound || 0,
        executionData.skippedItems || 0,
        executionData.thresholdPercentage || 0,
        Array.isArray(executionData.codes) ? executionData.codes.join(',') : (executionData.codes || ''),
        executionData.apiType || 'existing',
        executionData.priceCalculation || 'offer',
        executionData.executionTimeMs || 0,
        executionData.errorMessage || null,
        new Date()
      ];

      const result = await this.client.query(query, values);
      const savedExecutionId = result.rows[0].execution_id;
      
      // Save individual items if provided
      if (executionData.items && Array.isArray(executionData.items) && executionData.items.length > 0) {
        await this.saveCronJobExecutionResults(savedExecutionId, executionData.items);
      }
      
      console.log(`💾 Saved cron job execution for job ${executionData.jobId}`);
      return savedExecutionId;
    } catch (error) {
      console.error('❌ Error saving cron job execution:', error.message);
      return false;
    }
  }

  /**
   * Save individual items from a cron job execution
   */
  async saveCronJobExecutionResults(executionId, items) {
    if (!this.isDbAvailable) {
      console.log('⚠️ Database not available, skipping execution results save');
      return false;
    }

    try {
      // Prepare batch insert
      const values = [];
      const placeholders = [];
      let paramIndex = 1;

      for (const item of items) {
        placeholders.push(`($${paramIndex}, $${paramIndex + 1}, $${paramIndex + 2}, $${paramIndex + 3}, $${paramIndex + 4}, $${paramIndex + 5}, $${paramIndex + 6}, $${paramIndex + 7}, $${paramIndex + 8}, $${paramIndex + 9}, $${paramIndex + 10}, $${paramIndex + 11}, $${paramIndex + 12}, $${paramIndex + 13}, $${paramIndex + 14})`);
        
        values.push(
          executionId,
          item.id || item.productId,
          item.name || '',
          item.variation || '',
          parseFloat(item.storePrice) || 0,
          parseFloat(item.offerPrice) || 0,
          parseFloat(item.maxSaverPrice || item.offerPrice) || 0,
          parseFloat(item.discountPercentage) || 0,
          item.quantity || '',
          item.unit || '',
          item.url || '',
          item.imageUrl || '',
          item.category || '',
          item.filter || '',
          item.inStock === true
        );
        
        paramIndex += 15;
      }

      if (values.length > 0) {
        const query = `
          INSERT INTO cron_job_execution_results (
            execution_id, product_id, item_name, variation, store_price, offer_price,
            max_saver_price, discount_percentage, quantity, unit, url, image_url,
            category, filter, in_stock
          ) VALUES ${placeholders.join(', ')}
        `;

        await this.client.query(query, values);
        console.log(`💾 Saved ${items.length} execution results for execution ${executionId}`);
      }

      return true;
    } catch (error) {
      console.error('❌ Error saving cron job execution results:', error.message);
      return false;
    }
  }

  /**
   * Get cron job execution history
   */
  async getCronJobExecutions(jobId, limit = 20, offset = 0) {
    if (!this.isDbAvailable) {
      return [];
    }

    try {
      const query = `
        SELECT * FROM cron_job_executions 
        WHERE job_id = $1 
        ORDER BY completed_at DESC 
        LIMIT $2 OFFSET $3
      `;
      
      const result = await this.client.query(query, [jobId, limit, offset]);
      return result.rows;
    } catch (error) {
      console.error('❌ Error getting cron job executions:', error.message);
      return [];
    }
  }

  /**
   * Get execution results (items) for a specific execution
   */
  async getExecutionResults(executionId, limit = 100, offset = 0) {
    if (!this.isDbAvailable) {
      return [];
    }

    try {
      const query = `
        SELECT * FROM cron_job_execution_results 
        WHERE execution_id = $1 
        ORDER BY discount_percentage DESC
        LIMIT $2 OFFSET $3
      `;
      
      const result = await this.client.query(query, [executionId, limit, offset]);
      
      // Transform database rows to frontend format
      return result.rows.map(row => ({
        id: row.product_id,
        productId: row.product_id,
        name: row.item_name,
        variation: row.variation,
        storePrice: parseFloat(row.store_price) || 0,
        offerPrice: parseFloat(row.offer_price) || 0,
        maxSaverPrice: parseFloat(row.max_saver_price) || 0,
        discountPercentage: parseFloat(row.discount_percentage) || 0,
        quantity: row.quantity,
        unit: row.unit,
        url: row.url,
        imageUrl: row.image_url,
        category: row.category,
        filter: row.filter,
        inStock: row.in_stock === true
      }));
    } catch (error) {
      console.error('❌ Error getting execution results:', error.message);
      return [];
    }
  }

  /**
   * Save cron execution run
   */
  async saveCronExecutionRun(executionData) {
    if (!this.isDbAvailable) {
      return false;
    }

    try {
      const query = `
        INSERT INTO cron_execution_runs (
          execution_id, job_id, chat_id, codes, threshold_percentage,
          api_type, price_calculation,
          total_items_found, new_items_found, skipped_items, keyword_filtered_items,
          execution_time_ms, success, error_message, completed_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
        ON CONFLICT (execution_id) DO UPDATE SET
          total_items_found = EXCLUDED.total_items_found,
          new_items_found = EXCLUDED.new_items_found,
          skipped_items = EXCLUDED.skipped_items,
          keyword_filtered_items = EXCLUDED.keyword_filtered_items,
          execution_time_ms = EXCLUDED.execution_time_ms,
          success = EXCLUDED.success,
          error_message = EXCLUDED.error_message,
          completed_at = EXCLUDED.completed_at
      `;

      const values = [
        executionData.executionId,
        executionData.jobId,
        executionData.chatId,
        executionData.codes,
        executionData.threshold,
        executionData.apiType || 'existing',
        executionData.priceCalculation || 'offer',
        executionData.totalItemsFound || 0,
        executionData.newItemsFound || 0,
        executionData.skippedItems || 0,
        executionData.keywordFilteredItems || 0,
        executionData.executionTimeMs || 0,
        executionData.success !== false,
        executionData.errorMessage || null,
        executionData.completedAt || new Date()
      ];

      await this.pool.query(query, values);
      return true;
    } catch (error) {
      console.error('❌ Error saving cron execution run:', error.message);
      return false;
    }
  }

  /**
   * Save cron execution results (individual items)
   */
  async saveCronExecutionResults(executionId, jobId, chatId, items) {
    if (!this.isDbAvailable || !items || items.length === 0) {
      return false;
    }

    try {
      // Determine product IDs from previous run (excluding current execution)
      const previousIds = await this.getPreviousRunProductIds(jobId, executionId);

      // Use batch insert for better performance
      const values = [];
      const placeholders = [];

      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const productId = item.id || item.productId || null;
        const isNew = productId ? !previousIds.has(String(productId)) : false;
        const baseIndex = i * 14;
        placeholders.push(`($${baseIndex + 1}, $${baseIndex + 2}, $${baseIndex + 3}, $${baseIndex + 4}, $${baseIndex + 5}, $${baseIndex + 6}, $${baseIndex + 7}, $${baseIndex + 8}, $${baseIndex + 9}, $${baseIndex + 10}, $${baseIndex + 11}, $${baseIndex + 12}, $${baseIndex + 13}, $${baseIndex + 14})`);

        values.push(
          executionId,
          jobId,
          chatId,
          item.name,
          item.variation || null,
          productId,
          parseFloat(item.storePrice) || 0,
          parseFloat(item.offerPrice) || 0,
          parseFloat(item.discountPercentage) || 0,
          item.quantity || null,
          item.unit || null,
          item.url || null,
          parseFloat(item.appliedThreshold) || 0,
          isNew
        );
      }

      const query = `
        INSERT INTO cron_execution_results (
          execution_id, job_id, chat_id, item_name, variation_name,
          product_id, store_price, offer_price, discount_percentage,
          quantity, unit, item_url, applied_threshold, is_new
        ) VALUES ${placeholders.join(', ')}
      `;

      await this.pool.query(query, values);
      return true;
    } catch (error) {
      console.error('❌ Error saving cron execution results:', error.message);
      return false;
    }
  }

  /**
   * Get cron execution runs for a job with pagination
   */
  async getCronExecutionRuns(jobId, limit = 50, offset = 0) {
    if (!this.isDbAvailable) {
      return { runs: [], total: 0 };
    }

    try {
      const countQuery = 'SELECT COUNT(*) as count FROM cron_execution_runs WHERE job_id = $1';
      const countResult = await this.pool.query(countQuery, [jobId]);
      const total = parseInt(countResult.rows[0].count);

      const query = `
        SELECT * FROM cron_execution_runs
        WHERE job_id = $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `;
      const result = await this.pool.query(query, [jobId, limit, offset]);

      return { runs: result.rows, total };
    } catch (error) {
      console.error('❌ Error getting cron execution runs:', error.message);
      return { runs: [], total: 0 };
    }
  }

  /**
   * Get cron execution results for a specific execution
   */
  async getCronExecutionResults(executionId, limit = 100, offset = 0) {
    if (!this.isDbAvailable) {
      return { results: [], total: 0 };
    }

    try {
      const countQuery = 'SELECT COUNT(*) as count FROM cron_execution_results WHERE execution_id = $1';
      const countResult = await this.pool.query(countQuery, [executionId]);
      const total = parseInt(countResult.rows[0].count);

      const query = `
        SELECT * FROM cron_execution_results
        WHERE execution_id = $1
        ORDER BY discount_percentage DESC, created_at DESC
        LIMIT $2 OFFSET $3
      `;
      const result = await this.pool.query(query, [executionId, limit, offset]);

      return { results: result.rows, total };
    } catch (error) {
      console.error('❌ Error getting cron execution results:', error.message);
      return { results: [], total: 0 };
    }
  }

  /**
   * Check if item was already displayed and if pricing changed
   */
  async checkDisplayedItem(jobId, chatId, item) {
    if (!this.isDbAvailable) {
      return { wasDisplayed: false, pricingChanged: false };
    }

    try {
      const query = `
        SELECT * FROM displayed_items
        WHERE (job_id = $1 OR chat_id = $2) AND product_id = $3
        ORDER BY last_displayed_at DESC
        LIMIT 1
      `;

      const result = await this.pool.query(query, [jobId, chatId, item.id || item.productId]);

      if (result.rows.length === 0) {
        return { wasDisplayed: false, pricingChanged: false };
      }

      const displayedItem = result.rows[0];

      // Check if pricing changed significantly (more than 1% difference)
      const currentStorePrice = parseFloat(item.storePrice) || 0;
      const currentOfferPrice = parseFloat(item.offerPrice) || 0;
      const currentMaxSaverPrice = parseFloat(item.maxSaverPrice || item.offerPrice) || 0;

      const lastStorePrice = parseFloat(displayedItem.last_store_price) || 0;
      const lastOfferPrice = parseFloat(displayedItem.last_offer_price) || 0;
      const lastMaxSaverPrice = parseFloat(displayedItem.last_max_saver_price) || 0;

      const storePriceChanged = Math.abs(currentStorePrice - lastStorePrice) / Math.max(lastStorePrice, 1) > 0.01;
      const offerPriceChanged = Math.abs(currentOfferPrice - lastOfferPrice) / Math.max(lastOfferPrice, 1) > 0.01;
      const maxSaverPriceChanged = Math.abs(currentMaxSaverPrice - lastMaxSaverPrice) / Math.max(lastMaxSaverPrice, 1) > 0.01;

      const pricingChanged = storePriceChanged || offerPriceChanged || maxSaverPriceChanged;

      return {
        wasDisplayed: true,
        pricingChanged,
        lastDisplayed: displayedItem.last_displayed_at,
        displayCount: displayedItem.display_count
      };

    } catch (error) {
      console.error('❌ Error checking displayed item:', error.message);
      return { wasDisplayed: false, pricingChanged: false };
    }
  }

  /**
   * Mark item as displayed
   */
  async markItemAsDisplayed(jobId, chatId, item) {
    if (!this.isDbAvailable) {
      return false;
    }

    try {
      const query = `
        INSERT INTO displayed_items (
          job_id, chat_id, product_id, item_name,
          last_store_price, last_offer_price, last_max_saver_price,
          last_discount_percentage, last_displayed_at, display_count
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), 1)
        ON CONFLICT (job_id, product_id) DO UPDATE SET
          last_store_price = EXCLUDED.last_store_price,
          last_offer_price = EXCLUDED.last_offer_price,
          last_max_saver_price = EXCLUDED.last_max_saver_price,
          last_discount_percentage = EXCLUDED.last_discount_percentage,
          last_displayed_at = NOW(),
          display_count = displayed_items.display_count + 1,
          updated_at = NOW()
      `;

      const values = [
        jobId,
        chatId,
        item.id || item.productId,
        item.name || item.itemName,
        parseFloat(item.storePrice) || 0,
        parseFloat(item.offerPrice) || 0,
        parseFloat(item.maxSaverPrice || item.offerPrice) || 0,
        parseFloat(item.discountPercentage) || 0
      ];

      await this.pool.query(query, values);
      return true;
    } catch (error) {
      console.error('❌ Error marking item as displayed:', error.message);
      return false;
    }
  }

  /**
   * Get displayed items statistics
   */
  async getDisplayedItemsStats(jobId, chatId) {
    if (!this.isDbAvailable) {
      return { totalDisplayed: 0, uniqueItems: 0 };
    }

    try {
      const query = `
        SELECT
          COUNT(*) as total_displayed,
          COUNT(DISTINCT product_id) as unique_items,
          SUM(display_count) as total_displays
        FROM displayed_items
        WHERE job_id = $1 OR chat_id = $2
      `;

      const result = await this.pool.query(query, [jobId, chatId]);

      return {
        totalDisplayed: parseInt(result.rows[0].total_displayed) || 0,
        uniqueItems: parseInt(result.rows[0].unique_items) || 0,
        totalDisplays: parseInt(result.rows[0].total_displays) || 0
      };
    } catch (error) {
      console.error('❌ Error getting displayed items stats:', error.message);
      return { totalDisplayed: 0, uniqueItems: 0 };
    }
  }

  /**
   * Clean old displayed items (older than specified days)
   */
  async cleanOldDisplayedItems(daysOld = 30) {
    if (!this.isDbAvailable) {
      return 0;
    }

    try {
      const query = `
        DELETE FROM displayed_items
        WHERE last_displayed_at < NOW() - INTERVAL '${daysOld} days'
      `;

      const result = await this.pool.query(query);
      return result.rowCount || 0;
    } catch (error) {
      console.error('❌ Error cleaning old displayed items:', error.message);
      return 0;
    }
  }

  /**
   * Initialize database tables
   */
  async initializeTables() {
    if (!this.isDbAvailable) {
      console.log('⚠️ Database not available, skipping table initialization');
      return;
    }

    try {
      await this.pool.query(`
        -- Table for storing cron job execution history
        CREATE TABLE IF NOT EXISTS cron_job_executions (
          id SERIAL PRIMARY KEY,
          execution_id VARCHAR(100) UNIQUE NOT NULL,
          job_id VARCHAR(100) NOT NULL,
          success BOOLEAN DEFAULT true,
          total_items_found INTEGER DEFAULT 0,
          new_items_found INTEGER DEFAULT 0,
          skipped_items INTEGER DEFAULT 0,
          threshold_percentage DECIMAL(5,2) DEFAULT 0,
          codes TEXT,
          api_type VARCHAR(20) DEFAULT 'existing',
          price_calculation VARCHAR(20) DEFAULT 'offer',
          execution_time_ms INTEGER DEFAULT 0,
          error_message TEXT,
          completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_cron_executions_job_id ON cron_job_executions(job_id);
        CREATE INDEX IF NOT EXISTS idx_cron_executions_completed_at ON cron_job_executions(completed_at);
        
        -- Table for storing individual execution results (items found)
        CREATE TABLE IF NOT EXISTS cron_job_execution_results (
          id SERIAL PRIMARY KEY,
          execution_id VARCHAR(100) NOT NULL,
          product_id VARCHAR(100),
          item_name TEXT,
          variation TEXT,
          store_price DECIMAL(10,2) DEFAULT 0,
          offer_price DECIMAL(10,2) DEFAULT 0,
          max_saver_price DECIMAL(10,2) DEFAULT 0,
          discount_percentage DECIMAL(5,2) DEFAULT 0,
          quantity VARCHAR(50),
          unit VARCHAR(50),
          url TEXT,
          image_url TEXT,
          category VARCHAR(200),
          filter VARCHAR(200),
          in_stock BOOLEAN DEFAULT false,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_execution_results_execution_id ON cron_job_execution_results(execution_id);
        CREATE INDEX IF NOT EXISTS idx_execution_results_product_id ON cron_job_execution_results(product_id);
        CREATE INDEX IF NOT EXISTS idx_execution_results_discount ON cron_job_execution_results(discount_percentage);
      `);

      console.log('✅ Database tables initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing database tables:', error.message);
    }
  }

  /**
   * Close database connection
   */
  async close() {
    if (this.pool) {
      await this.pool.end();

    }
  }

  /**
   * Get previous run's product IDs for a job (excluding a given execution)
   */
  async getPreviousRunProductIds(jobId, excludeExecutionId) {
    if (!this.isDbAvailable) {
      return new Set();
    }

    try {
      const runQuery = `
        SELECT execution_id FROM cron_execution_runs
        WHERE job_id = $1 AND execution_id <> $2
        ORDER BY created_at DESC
        LIMIT 1
      `;
      const runRes = await this.pool.query(runQuery, [jobId, excludeExecutionId || '']);
      if (runRes.rows.length === 0) {
        return new Set();
      }
      const prevExecId = runRes.rows[0].execution_id;

      const idsQuery = `
        SELECT DISTINCT product_id FROM cron_execution_results
        WHERE execution_id = $1 AND product_id IS NOT NULL
      `;
      const idsRes = await this.pool.query(idsQuery, [prevExecId]);
      return new Set(idsRes.rows.map(r => String(r.product_id)));
    } catch (error) {
      console.error('❌ Error fetching previous run product IDs:', error.message);
      return new Set();
    }
  }

  /**
   * Get most recent cron execution results across all jobs
   */
  async getLatestCronResults(limit = 200, offset = 0) {
    if (!this.isDbAvailable) {
      return { results: [], total: 0 };
    }

    try {
      const countQuery = 'SELECT COUNT(*) as count FROM cron_execution_results';
      const countResult = await this.pool.query(countQuery);
      const total = parseInt(countResult.rows[0].count);

      const query = `
        SELECT * FROM cron_execution_results
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2
      `;
      const result = await this.pool.query(query, [limit, offset]);

      return { results: result.rows, total };
    } catch (error) {
      console.error('❌ Error getting latest cron execution results:', error.message);
      return { results: [], total: 0 };
    }
  }

  /**
   * Get most recent Max Saver price gap results across all runs
   */
  async getLatestMaxSaverGapResults(limit = 200, offset = 0) {
    if (!this.isDbAvailable) {
      return { results: [], total: 0 };
    }

    try {
      const countQuery = 'SELECT COUNT(*) as count FROM max_saver_gap_results';
      const countResult = await this.pool.query(countQuery);
      const total = parseInt(countResult.rows[0].count);

      const query = `
        SELECT * FROM max_saver_gap_results
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2
      `;
      const result = await this.pool.query(query, [limit, offset]);

      return { results: result.rows, total };
    } catch (error) {
      console.error('❌ Error getting latest max saver gap results:', error.message);
      return { results: [], total: 0 };
    }
  }
}

module.exports = CronJobPersistence;