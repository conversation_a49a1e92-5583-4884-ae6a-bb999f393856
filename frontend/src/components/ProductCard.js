import React from 'react';
import { Link } from 'react-router-dom';
import { ExternalLink, Package, AlertCircle, CheckCircle } from 'lucide-react';
import { formatPrice, getDiscountColor, truncateText } from '../services/api';

const ProductCard = ({ product, showHistory = false }) => {
  const discountColorClass = getDiscountColor(product.discountPercentage);

  // Get the best available image URL
  const getImageUrl = (product) => {
    // Priority order: imageUrl, image, first item from images array
    if (product.imageUrl) return product.imageUrl;
    if (product.image) return product.image;
    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
      // If it's just an image ID, construct the full URL
      const imageId = product.images[0];
      if (typeof imageId === 'string' && !imageId.startsWith('http')) {
        return `https://instamart-media-assets.swiggy.com/swiggy/image/upload/fl_lossy,f_auto,q_auto,h_600/${imageId}`;
      }
      return imageId;
    }
    return null;
  };

  const imageUrl = getImageUrl(product);

  // Determine display pricing with Max Saver preference
  const hasMaxSaver = product.maxSaverPrice && Number(product.maxSaverPrice) > 0 && Number(product.maxSaverPrice) < Number(product.offerPrice || product.finalPrice || Number.MAX_SAFE_INTEGER);
  const primaryPrice = hasMaxSaver ? Number(product.maxSaverPrice) : Number(product.finalPrice || product.offerPrice);
  const baselinePrice = Number(product.originalPrice || product.storePrice);

  // Debug: Log product data to see what image properties are available
  if (process.env.NODE_ENV === 'development') {
    console.log('Product image data:', {
      id: product.id,
      name: product.name,
      image: product.image,
      imageUrl: product.imageUrl,
      images: product.images,
      finalImageUrl: imageUrl,
      url: product.url,
      productUrl: product.productUrl
    });
  }

  return (
    <div className="product-card bg-white border border-gray-200 rounded-lg overflow-hidden">
      {/* Product Image */}
      <div className="relative">
        {imageUrl ? (
          <img
            src={imageUrl}
            alt={product.name}
            className="w-full h-48 object-cover"
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
        ) : null}
        <div
          className="w-full h-48 bg-gray-100 flex items-center justify-center"
          style={{ display: imageUrl ? 'none' : 'flex' }}
        >
          <Package className="h-12 w-12 text-gray-400" />
        </div>
        
        {/* Discount Badge */}
        <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-bold ${discountColorClass}`}>
          {product.discountPercentage}% OFF
        </div>
        
        {/* Stock Status */}
        <div className="absolute top-2 left-2">
          {product.inStock ? (
            <div className="flex items-center space-x-1 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
              <CheckCircle className="h-3 w-3" />
              <span>In Stock</span>
            </div>
          ) : (
            <div className="flex items-center space-x-1 bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">
              <AlertCircle className="h-3 w-3" />
              <span>Out of Stock</span>
            </div>
          )}
        </div>
      </div>

      {/* Product Details */}
      <div className="p-4">
        <div className="mb-2">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2" title={product.name}>
            {truncateText(product.name, 60)}
          </h3>
          {product.brand && product.brand !== 'Unknown' && (
            <p className="text-xs text-gray-500 mt-1">{product.brand}</p>
          )}
        </div>

        {/* Pricing */}
        <div className="mb-3">
          <div className="flex items-center space-x-2">
            <span className="text-lg font-bold text-gray-900">
              {formatPrice(primaryPrice)}
            </span>
            {baselinePrice > primaryPrice && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(baselinePrice)}
              </span>
            )}
          </div>
          {hasMaxSaver && (
            <p className="text-xs text-green-600">Max Saver applied</p>
          )}
          {!hasMaxSaver && product.maxSaverPrice && Number(product.maxSaverPrice) > 0 && (
            <p className="text-xs text-gray-500">Max Saver: {formatPrice(product.maxSaverPrice)}</p>
          )}
          {product.unit && (
            <p className="text-xs text-gray-500">per {product.unit}</p>
          )}
        </div>

        {/* Category */}
        {product.category && product.category !== 'Unknown' && (
          <div className="mb-3">
            <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
              {product.category}
            </span>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between">
          {showHistory ? (
            <Link
              to={`/products/${product.id}`}
              className="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              View History
            </Link>
          ) : (
            <div className="flex-1" />
          )}
          
          {(product.url || product.productUrl) ? (
            <a
              href={product.url || product.productUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-1 bg-primary-600 hover:bg-primary-700 text-white text-xs px-3 py-2 rounded-lg transition-colors duration-200"
            >
              <span>View on Swiggy</span>
              <ExternalLink className="h-3 w-3" />
            </a>
          ) : (
            <span className="inline-flex items-center space-x-1 bg-gray-400 text-white text-xs px-3 py-2 rounded-lg cursor-not-allowed">
              <span>No URL</span>
              <ExternalLink className="h-3 w-3" />
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
