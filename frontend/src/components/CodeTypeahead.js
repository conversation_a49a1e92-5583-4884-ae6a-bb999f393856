import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Code, Filter, X } from 'lucide-react';

const CodeTypeahead = ({ 
  value, 
  onChange, 
  placeholder = "Enter codes (e.g., A1, B2, C3)", 
  mappings,
  multiple = true,
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const inputRef = useRef(null);
  const dropdownRef = useRef(null);

  // Get all available codes
  const getAllCodes = () => {
    if (!mappings) return [];
    
    const allCodes = [];
    
    mappings.categories.forEach(category => {
      // Add category-only code
      allCodes.push({
        code: category.code,
        name: category.name,
        type: 'category',
        description: `${category.name} (ALL FILTERS)`,
        filterCount: category.filters ? category.filters.length : 0
      });
      
      // Add filter codes
      if (category.filters) {
        category.filters.forEach(filter => {
          allCodes.push({
            code: filter.code,
            name: filter.name,
            type: 'filter',
            description: `${category.name} / ${filter.name}`,
            categoryName: category.name
          });
        });
      }
    });
    
    return allCodes;
  };

  // Filter codes based on search term
  const filteredCodes = getAllCodes().filter(item => {
    const search = searchTerm.toLowerCase();
    return (
      item.code.toLowerCase().includes(search) ||
      item.name.toLowerCase().includes(search) ||
      item.description.toLowerCase().includes(search)
    );
  }).slice(0, 20); // Limit to 20 results

  // Handle input change
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    onChange(newValue);
    
    // Extract the last part for search (after last comma)
    const parts = newValue.split(',');
    const lastPart = parts[parts.length - 1].trim();
    setSearchTerm(lastPart);
    setIsOpen(true);
    setHighlightedIndex(-1);
  };

  // Handle code selection
  const handleCodeSelect = (selectedCode) => {
    if (multiple) {
      const parts = value.split(',').map(part => part.trim()).filter(part => part.length > 0);
      
      // Remove the last incomplete part and add the selected code
      parts.pop();
      parts.push(selectedCode.code);
      
      const newValue = parts.join(', ') + ', ';
      onChange(newValue);
      setSearchTerm('');
    } else {
      onChange(selectedCode.code);
      setSearchTerm('');
    }
    
    setIsOpen(false);
    setHighlightedIndex(-1);
    inputRef.current?.focus();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!isOpen) {
      if (e.key === 'ArrowDown') {
        setIsOpen(true);
        setHighlightedIndex(0);
        e.preventDefault();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        setHighlightedIndex(prev => 
          prev < filteredCodes.length - 1 ? prev + 1 : prev
        );
        e.preventDefault();
        break;
      case 'ArrowUp':
        setHighlightedIndex(prev => prev > 0 ? prev - 1 : prev);
        e.preventDefault();
        break;
      case 'Enter':
        if (highlightedIndex >= 0 && filteredCodes[highlightedIndex]) {
          handleCodeSelect(filteredCodes[highlightedIndex]);
        }
        e.preventDefault();
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Clear input
  const handleClear = () => {
    onChange('');
    setSearchTerm('');
    setIsOpen(false);
    inputRef.current?.focus();
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className="w-full px-3 py-2 pr-20 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
        
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          {value && (
            <button
              type="button"
              onClick={handleClear}
              className="p-1 text-gray-400 hover:text-gray-600 rounded"
            >
              <X className="h-4 w-4" />
            </button>
          )}
          <button
            type="button"
            onClick={() => setIsOpen(!isOpen)}
            className="p-1 text-gray-400 hover:text-gray-600 rounded"
          >
            <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </button>
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && filteredCodes.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {filteredCodes.map((item, index) => (
            <button
              key={`${item.code}-${index}`}
              type="button"
              onClick={() => handleCodeSelect(item)}
              className={`w-full px-3 py-2 text-left hover:bg-gray-50 flex items-center space-x-3 ${
                index === highlightedIndex ? 'bg-primary-50 border-l-2 border-primary-500' : ''
              }`}
            >
              <div className="flex items-center space-x-2">
                {item.type === 'category' ? (
                  <Code className="h-4 w-4 text-blue-600" />
                ) : (
                  <Filter className="h-4 w-4 text-green-600" />
                )}
                <code className={`px-2 py-1 rounded text-xs font-bold ${
                  item.type === 'category' 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {item.code}
                </code>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {item.name}
                </div>
                <div className="text-xs text-gray-500 truncate">
                  {item.type === 'category' 
                    ? `${item.filterCount} filters` 
                    : item.categoryName
                  }
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* No results */}
      {isOpen && searchTerm && filteredCodes.length === 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg p-3">
          <div className="text-sm text-gray-500 text-center">
            No codes found for "{searchTerm}"
          </div>
        </div>
      )}
    </div>
  );
};

export default CodeTypeahead;
