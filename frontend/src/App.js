import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import FindDeals from './pages/FindDeals';
import DealHistory from './pages/DealHistory';
import MappingCodes from './pages/MappingCodes';
import CronJobs from './pages/CronJobs';
import CreateCronJob from './pages/CreateCronJob';
import CronJobDetails from './pages/CronJobDetails';
import ProductDetails from './pages/ProductDetails';
import Keywords from './pages/Keywords';
import PriceGapFinder from './pages/PriceGapFinder';
import Products from './pages/Products';

function App() {
  return (
    <Router>
      <div className="App">
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/find-deals" element={<FindDeals />} />
            <Route path="/history" element={<DealHistory />} />
            <Route path="/codes" element={<MappingCodes />} />
            <Route path="/cron-jobs" element={<CronJobs />} />
            <Route path="/cron-jobs/create" element={<CreateCronJob />} />
            <Route path="/cron-jobs/:jobId" element={<CronJobDetails />} />
            <Route path="/keywords" element={<Keywords />} />
            <Route path="/price-gap-finder" element={<PriceGapFinder />} />
            <Route path="/products" element={<Products />} />
            <Route path="/products/:productId" element={<ProductDetails />} />
          </Routes>
        </Layout>
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#22c55e',
                secondary: '#fff',
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
      </div>
    </Router>
  );
}

export default App;
