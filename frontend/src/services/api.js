import axios from 'axios';
import toast from 'react-hot-toast';

const API_BASE_URL = process.env.REACT_APP_API_URL || '/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers here if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const message = error.response?.data?.error || error.message || 'An error occurred';
    
    // Don't show toast for certain endpoints (like polling)
    if (!error.config?.silent) {
      toast.error(message);
    }
    
    return Promise.reject(error);
  }
);

// API methods
export const cronJobsAPI = {
  // Get all cron jobs
  getAll: () => api.get('/cron-jobs'),

  // Get cron job execution history (database-based)
  getExecutions: (jobId, params = {}) =>
    api.get(`/cron-jobs/${jobId}/executions`, { params }),

  // Get execution results (items found in specific execution)
  getExecutionResults: (executionId, params = {}) =>
    api.get(`/cron-jobs/executions/${executionId}/results`, { params }),

  // Get cron job results (legacy file-based)
  getResults: (jobId, params = {}) =>
    api.get(`/cron-jobs/${jobId}/results`, { params }),

  // Get specific cron job details
  getDetails: (jobId) => api.get(`/cron-jobs/${jobId}`),

  // Create new cron job
  create: (jobData) => api.post('/cron-jobs', jobData),

  // Delete cron job
  delete: (jobId) => api.delete(`/cron-jobs/${jobId}`),

  // Manually trigger cron job
  trigger: (jobId) => api.post(`/cron-jobs/${jobId}/trigger`),
};

export const productsAPI = {
  // Get product price history
  getHistory: (productId) => api.get(`/products/${productId}/history`),
  
  // Search products
  search: (query, params = {}) => 
    api.get('/products/search', { params: { q: query, ...params } }),
  // Aggregated products list
  list: (params = {}) => api.get('/products', { params }),
};

export const dashboardAPI = {
  // Get dashboard statistics
  getStats: () => api.get('/dashboard/stats'),

  // Get recent activity
  getActivity: (limit = 10) =>
    api.get('/dashboard/activity', { params: { limit } }),
};

export const keywordsAPI = {
  // Get keyword thresholds for a user
  getAll: (chatId) => api.get(`/keywords/${chatId}`),

  // Add or update keyword threshold
  set: (chatId, keyword, threshold) =>
    api.post(`/keywords/${chatId}`, { keyword, threshold }),

  // Delete keyword threshold
  delete: (chatId, keyword) => api.delete(`/keywords/${chatId}/${keyword}`),
};

export const mappingsAPI = {
  // Get available mapping codes
  getAll: () => api.get('/mappings'),
};

// Utility functions
export const formatPrice = (price) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(price);
};

export const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'Asia/Kolkata',
  }).format(new Date(date));
};

export const formatRelativeTime = (date) => {
  const now = new Date();
  const target = new Date(date);
  const diffInSeconds = Math.floor((now - target) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  }
};

export const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'running':
      return 'success';
    case 'stopped':
      return 'warning';
    case 'error':
      return 'danger';
    default:
      return 'info';
  }
};

export const getDiscountColor = (percentage) => {
  if (percentage >= 80) return 'text-red-600 bg-red-50';
  if (percentage >= 60) return 'text-orange-600 bg-orange-50';
  if (percentage >= 40) return 'text-yellow-600 bg-yellow-50';
  return 'text-green-600 bg-green-50';
};

export const truncateText = (text, maxLength = 50) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Export the axios instance for custom requests
export default api;
