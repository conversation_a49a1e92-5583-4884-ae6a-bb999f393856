import React, { useEffect, useState, useMemo } from 'react';
import { Search, ArrowDownAZ, ArrowUpAZ, Percent, RefreshCw } from 'lucide-react';
import { productsAPI } from '../services/api';
import ProductCard from '../components/ProductCard';
import LoadingSpinner from '../components/LoadingSpinner';

const Products = () => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [query, setQuery] = useState('');
  const [sort, setSort] = useState('discount'); // 'low' | 'high' | 'discount'
  const [sources, setSources] = useState({ cron: true, history: true, pricegap: true });

  const sourceParam = useMemo(() => {
    return Object.entries(sources)
      .filter(([, enabled]) => enabled)
      .map(([name]) => name)
      .join(',');
  }, [sources]);

  const load = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      setLoading(true);
      const res = await productsAPI.list({ q: query, sort, sources: sourceParam, limit: 200 });
      const data = res.data;
      setItems(data.data || []);
    } catch (e) {
      setItems([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    load(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    load(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Products</h1>
          <p className="mt-1 text-sm text-gray-500">All deals found across cron, history, and price gap</p>
        </div>
        <button
          onClick={() => load(true)}
          disabled={refreshing}
          className="btn-secondary flex items-center space-x-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Controls */}
      <div className="card">
        <form onSubmit={handleSearch} className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search products..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-600">Sources:</label>
              <label className="text-sm text-gray-700 flex items-center space-x-1">
                <input type="checkbox" checked={sources.cron} onChange={(e) => setSources(s => ({ ...s, cron: e.target.checked }))} />
                <span>Cron</span>
              </label>
              <label className="text-sm text-gray-700 flex items-center space-x-1">
                <input type="checkbox" checked={sources.history} onChange={(e) => setSources(s => ({ ...s, history: e.target.checked }))} />
                <span>History</span>
              </label>
              <label className="text-sm text-gray-700 flex items-center space-x-1">
                <input type="checkbox" checked={sources.pricegap} onChange={(e) => setSources(s => ({ ...s, pricegap: e.target.checked }))} />
                <span>Price Gap</span>
              </label>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button type="button" onClick={() => { setSort('low'); load(false); }} className={`btn-secondary flex items-center space-x-1 ${sort === 'low' ? 'ring-2 ring-primary-300' : ''}`}>
              <ArrowDownAZ className="h-4 w-4" />
              <span>Low to High</span>
            </button>
            <button type="button" onClick={() => { setSort('high'); load(false); }} className={`btn-secondary flex items-center space-x-1 ${sort === 'high' ? 'ring-2 ring-primary-300' : ''}`}>
              <ArrowUpAZ className="h-4 w-4" />
              <span>High to Low</span>
            </button>
            <button type="button" onClick={() => { setSort('discount'); load(false); }} className={`btn-secondary flex items-center space-x-1 ${sort === 'discount' ? 'ring-2 ring-primary-300' : ''}`}>
              <Percent className="h-4 w-4" />
              <span>Discount %</span>
            </button>
            <button type="submit" className="btn-primary">Apply</button>
          </div>
        </form>
      </div>

      {/* Grid */}
      {loading ? (
        <div className="flex items-center justify-center h-64"><LoadingSpinner /></div>
      ) : items.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
          {items.map((product, index) => (
            <ProductCard key={index} product={product} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 text-gray-600">No products found</div>
      )}
    </div>
  );
};

export default Products;

