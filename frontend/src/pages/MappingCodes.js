import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Search, 
  Code,
  Copy,
  CheckCircle,
  Filter,
  Grid,
  List
} from 'lucide-react';
import { mappingsAPI } from '../services/api';
import toast from 'react-hot-toast';
import LoadingSpinner from '../components/LoadingSpinner';

const MappingCodes = () => {
  const navigate = useNavigate();
  const [mappings, setMappings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [copiedCode, setCopiedCode] = useState(null);

  useEffect(() => {
    const fetchMappings = async () => {
      try {
        const response = await mappingsAPI.getAll();
        setMappings(response.data.data);
      } catch (error) {
        console.error('Error fetching mappings:', error);
        toast.error('Failed to load mapping codes');
      } finally {
        setLoading(false);
      }
    };

    fetchMappings();
  }, []);

  const copyToClipboard = async (code) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      toast.success(`Copied ${code} to clipboard!`);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const getAllCodes = () => {
    if (!mappings) return [];
    
    const allCodes = [];
    
    mappings.categories.forEach(category => {
      // Add category-only code
      allCodes.push({
        code: category.code,
        name: category.name,
        type: 'category',
        description: `${category.name} (ALL FILTERS)`,
        filterCount: category.filters ? category.filters.length : 0
      });
      
      // Add filter codes
      if (category.filters) {
        category.filters.forEach(filter => {
          allCodes.push({
            code: filter.code,
            name: filter.name,
            type: 'filter',
            description: `${category.name} / ${filter.name}`,
            categoryName: category.name
          });
        });
      }
    });
    
    return allCodes;
  };

  const filteredCodes = getAllCodes().filter(item => 
    item.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const categoryOnlyCodes = filteredCodes.filter(item => item.type === 'category');
  const filterCodes = filteredCodes.filter(item => item.type === 'filter');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/')}
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Mapping Codes</h1>
          <p className="mt-1 text-sm text-gray-500">
            Browse all available category and filter codes
          </p>
        </div>
      </div>

      {/* Search and Controls */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search codes, categories, or filters..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-primary-100 text-primary-600' : 'text-gray-400 hover:text-gray-600'}`}
            >
              <Grid className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-primary-100 text-primary-600' : 'text-gray-400 hover:text-gray-600'}`}
            >
              <List className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{categoryOnlyCodes.length}</div>
            <div className="text-sm text-blue-700">Category Codes</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{filterCodes.length}</div>
            <div className="text-sm text-green-700">Filter Codes</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{filteredCodes.length}</div>
            <div className="text-sm text-purple-700">Total Showing</div>
          </div>
        </div>
      </div>

      {/* Category Codes */}
      {categoryOnlyCodes.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2">
            <Code className="h-5 w-5 text-blue-600" />
            <span>Category Codes ({categoryOnlyCodes.length})</span>
          </h3>
          
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : 'space-y-2'}>
            {categoryOnlyCodes.map((item, index) => (
              <div
                key={index}
                className={`border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:bg-blue-50 transition-colors ${
                  viewMode === 'list' ? 'flex items-center justify-between' : ''
                }`}
              >
                <div className={viewMode === 'list' ? 'flex-1' : ''}>
                  <div className="flex items-center space-x-2 mb-2">
                    <code className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-bold">
                      {item.code}
                    </code>
                    <span className="text-xs bg-blue-200 text-blue-700 px-2 py-1 rounded">
                      {item.filterCount} filters
                    </span>
                  </div>
                  <h4 className="font-medium text-gray-900 text-sm">{item.name}</h4>
                  <p className="text-xs text-gray-500 mt-1">All filters in this category</p>
                </div>
                
                <button
                  onClick={() => copyToClipboard(item.code)}
                  className="mt-2 flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm"
                >
                  {copiedCode === item.code ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                  <span>{copiedCode === item.code ? 'Copied!' : 'Copy'}</span>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Filter Codes */}
      {filterCodes.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2">
            <Filter className="h-5 w-5 text-green-600" />
            <span>Filter Codes ({filterCodes.length})</span>
          </h3>
          
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : 'space-y-2'}>
            {filterCodes.map((item, index) => (
              <div
                key={index}
                className={`border border-gray-200 rounded-lg p-4 hover:border-green-300 hover:bg-green-50 transition-colors ${
                  viewMode === 'list' ? 'flex items-center justify-between' : ''
                }`}
              >
                <div className={viewMode === 'list' ? 'flex-1' : ''}>
                  <div className="flex items-center space-x-2 mb-2">
                    <code className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-bold">
                      {item.code}
                    </code>
                  </div>
                  <h4 className="font-medium text-gray-900 text-sm">{item.name}</h4>
                  <p className="text-xs text-gray-500 mt-1">{item.categoryName}</p>
                </div>
                
                <button
                  onClick={() => copyToClipboard(item.code)}
                  className="mt-2 flex items-center space-x-1 text-green-600 hover:text-green-700 text-sm"
                >
                  {copiedCode === item.code ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                  <span>{copiedCode === item.code ? 'Copied!' : 'Copy'}</span>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No Results */}
      {filteredCodes.length === 0 && (
        <div className="text-center py-8">
          <Code className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No codes found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search terms.
          </p>
        </div>
      )}
    </div>
  );
};

export default MappingCodes;
