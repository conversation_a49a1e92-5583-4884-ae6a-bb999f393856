import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Search, 
  Calendar,
  Clock,
  TrendingDown,
  Filter,
  RefreshCw,
  Eye,
  Code
} from 'lucide-react';
import toast from 'react-hot-toast';
import LoadingSpinner from '../components/LoadingSpinner';
import ProductCard from '../components/ProductCard';

const DealHistory = () => {
  const navigate = useNavigate();
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedSearch, setSelectedSearch] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sourceFilter, setSourceFilter] = useState('all'); // 'all', 'dashboard', 'mobile'

  const fetchHistory = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      const response = await fetch('/api/deals/history?limit=50');
      const data = await response.json();
      
      if (data.success) {
        setHistory(data.data);
      } else {
        toast.error('Failed to fetch deal history');
      }
    } catch (error) {
      console.error('Error fetching deal history:', error);
      toast.error('Failed to fetch deal history');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchHistory();
  }, []);

  const handleRefresh = () => {
    fetchHistory(true);
  };

  const filteredHistory = history.filter(search => {
    const matchesSearch = searchTerm === '' || 
      search.codes.some(code => code.toLowerCase().includes(searchTerm.toLowerCase())) ||
      new Date(search.timestamp).toLocaleDateString().includes(searchTerm);
    
    const matchesSource = sourceFilter === 'all' || search.source === sourceFilter;
    
    return matchesSearch && matchesSource;
  });

  const formatDuration = (duration) => {
    return duration < 1 ? `${(duration * 1000).toFixed(0)}ms` : `${duration}s`;
  };

  const getSourceBadge = (source) => {
    const badges = {
      dashboard: { color: 'bg-blue-100 text-blue-800', label: 'Dashboard' },
      mobile: { color: 'bg-green-100 text-green-800', label: 'Mobile Bot' },
      api: { color: 'bg-purple-100 text-purple-800', label: 'API' }
    };
    
    const badge = badges[source] || { color: 'bg-gray-100 text-gray-800', label: source };
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${badge.color}`}>
        {badge.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/')}
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Deal Search History</h1>
          <p className="mt-1 text-sm text-gray-500">
            View all previous deal searches and their results
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className="card">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by codes or date..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
            
            <select
              value={sourceFilter}
              onChange={(e) => setSourceFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">All Sources</option>
              <option value="dashboard">Dashboard</option>
              <option value="mobile">Mobile Bot</option>
            </select>
          </div>
          
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* History List */}
      <div className="space-y-4">
        {filteredHistory.length > 0 ? (
          filteredHistory.map((search, index) => (
            <div key={index} className="card hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {new Date(search.timestamp).toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatDuration(search.duration)} search time
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {getSourceBadge(search.source)}
                  {search.items && search.items.length > 0 && (
                    <button
                      onClick={() => setSelectedSearch(selectedSearch === index ? null : index)}
                      className="btn-secondary flex items-center space-x-1 text-sm"
                    >
                      <Eye className="h-4 w-4" />
                      <span>{selectedSearch === index ? 'Hide' : 'View'} Items</span>
                    </button>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-lg font-bold text-blue-600">{search.codes.length}</div>
                  <div className="text-xs text-blue-700">Codes</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-lg font-bold text-green-600">{search.threshold}%</div>
                  <div className="text-xs text-green-700">Threshold</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-lg font-bold text-purple-600">{search.totalItems || 0}</div>
                  <div className="text-xs text-purple-700">Total Found</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <div className="text-lg font-bold text-orange-600">{search.uniqueItems || 0}</div>
                  <div className="text-xs text-orange-700">Unique Items</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 mb-3">
                <Code className="h-4 w-4 text-gray-400" />
                <div className="flex flex-wrap gap-1">
                  {search.codes.map((code, codeIndex) => (
                    <span
                      key={codeIndex}
                      className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs font-mono"
                    >
                      {code}
                    </span>
                  ))}
                </div>
              </div>
              
              {/* Items Display */}
              {selectedSearch === index && search.items && search.items.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center space-x-2">
                    <TrendingDown className="h-4 w-4 text-primary-600" />
                    <span>Found Items ({search.items.length})</span>
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {search.items.slice(0, 12).map((item, itemIndex) => (
                      <ProductCard key={itemIndex} product={item} showHistory={true} />
                    ))}
                  </div>
                  {search.items.length > 12 && (
                    <div className="mt-3 text-center">
                      <span className="text-sm text-gray-500">
                        And {search.items.length - 12} more items...
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="text-center py-12">
            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No search history found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || sourceFilter !== 'all' 
                ? 'Try adjusting your filters or search terms.'
                : 'Start searching for deals to see history here.'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DealHistory;
