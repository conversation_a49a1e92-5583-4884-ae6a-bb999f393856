import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import {
  ArrowLeft,
  Clock,
  RefreshCw,
  ExternalLink,
  Package,
  TrendingDown,
  Calendar,
  Activity,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { cronJobsAPI, formatDate, formatPrice, getDiscountColor } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import ProductCard from '../components/ProductCard';
import api from '../services/api';

const CronJobDetails = () => {
  const { jobId } = useParams();
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedResult, setSelectedResult] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);

  const fetchResults = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);

      // Try new database-based execution history first
      try {
        const response = await cronJobsAPI.getExecutions(jobId, { limit: 20 });
        const mappedRuns = (response.data.data || []).map(row => ({
          executionId: row.execution_id || row.executionId,
          success: row.success !== false,
          productsFound: row.total_items_found ?? row.productsFound ?? 0,
          newItems: row.new_items_found ?? row.newItems ?? 0,
          skippedItems: row.skipped_items ?? row.skippedItems ?? 0,
          threshold: row.threshold_percentage ?? row.threshold ?? 0,
          codes: typeof row.codes === 'string' ? row.codes.split(',').map(c => c.trim()) : (row.codes || []),
          timestamp: row.completed_at || row.created_at || row.timestamp,
        }));
        setResults(mappedRuns);
        if (mappedRuns.length > 0 && !selectedResult) {
          const first = mappedRuns[0];
          setSelectedResult(first);
          try {
            const itemsRes = await api.get(`/cron-jobs/executions/${first.executionId}/results`);
            setSelectedItems(itemsRes.data.data || []);
          } catch (e) {
            setSelectedItems([]);
          }
        }
      } catch (dbError) {
        // Fallback to file-based results if database approach fails
        console.log('Database execution history not available, falling back to file-based results');
        const response = await cronJobsAPI.getResults(jobId, { limit: 20 });
        setResults(response.data.data);
        if (response.data.data.length > 0 && !selectedResult) {
          setSelectedResult(response.data.data[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching cron job results:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchResults();
  }, [jobId]);

  const handleRefresh = () => {
    fetchResults(true);
  };

  const handleSelectResult = async (result) => {
    setSelectedResult(result);
    try {
      const itemsRes = await api.get(`/cron-jobs/executions/${result.executionId}/results`);
      setSelectedItems(itemsRes.data.data || []);
    } catch (e) {
      setSelectedItems([]);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/cron-jobs"
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <ArrowLeft className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Cron Job Details</h1>
            <p className="mt-1 text-sm text-gray-500">Job ID: {jobId}</p>
          </div>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="btn-secondary flex items-center space-x-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {results.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Execution History Sidebar */}
          <div className="lg:col-span-1">
            <div className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Execution History</h3>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {results.map((result, index) => (
                  <button
                    key={index}
                    onClick={() => handleSelectResult(result)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors duration-200 ${
                      selectedResult === result
                        ? 'border-primary-300 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Activity className={`h-4 w-4 ${result.success === false ? 'text-red-400' : 'text-gray-400'}`} />
                        <span className={`text-sm font-medium ${result.success === false ? 'text-red-600' : 'text-gray-900'}`}>
                          {result.success === false ? 'Failed' : `${result.productsFound} items`}
                        </span>
                        {result.newItems !== undefined && result.success !== false && (
                          <span className="text-xs text-green-600">
                            {result.newItems} new
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatDate(result.timestamp).split(',')[1]}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDate(result.timestamp).split(',')[0]}
                    </p>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {selectedResult && (
              <div className="space-y-6">
                {/* Execution Summary */}
                <div className="card">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Execution Summary</h3>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-500">
                        {formatDate(selectedResult.timestamp)}
                      </span>
                    </div>
                  </div>
                  
                  {selectedResult.success === false ? (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <AlertCircle className="h-5 w-5 text-red-600" />
                        <h4 className="text-sm font-medium text-red-800">Execution Failed</h4>
                      </div>
                      <p className="text-sm text-red-700 mt-2">{selectedResult.error}</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <Package className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold text-blue-900">{selectedResult.totalItems || selectedResult.productsFound}</p>
                        <p className="text-sm text-blue-600">Total Found</p>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold text-green-900">{selectedResult.newItems || selectedResult.productsFound}</p>
                        <p className="text-sm text-green-600">New Items</p>
                      </div>
                      <div className="text-center p-4 bg-yellow-50 rounded-lg">
                        <Clock className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold text-yellow-900">{selectedResult.skippedItems || 0}</p>
                        <p className="text-sm text-yellow-600">Skipped</p>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <TrendingDown className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                        <p className="text-2xl font-bold text-purple-900">
                          {selectedResult.products && selectedResult.products.length > 0
                            ? Math.round(selectedResult.products.reduce((sum, p) => sum + parseFloat(p.discountPercentage || 0), 0) / selectedResult.products.length)
                            : selectedResult.threshold || 0}%
                        </p>
                        <p className="text-sm text-purple-600">Avg Discount</p>
                      </div>
                    </div>
                  )}

                  {selectedResult.codes && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Codes Used:</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedResult.codes.map((code, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-200 text-gray-800 rounded text-xs font-mono">
                            {code}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Products Grid */}
                <div className="card">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Products Found</h3>
                  
                  {selectedItems.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                      {selectedItems.map((product, index) => (
                        <div key={index} className="relative">
                          {product.isNew && (
                            <span className="absolute -top-2 -left-2 z-10 bg-green-600 text-white text-[10px] px-2 py-1 rounded-full shadow">
                              NEW
                            </span>
                          )}
                          <ProductCard product={product} />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Package className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        This execution didn't find any products matching the criteria.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <Activity className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No execution history</h3>
          <p className="mt-1 text-sm text-gray-500">
            This cron job hasn't run yet or no results are available.
          </p>
        </div>
      )}
    </div>
  );
};

export default CronJobDetails;
