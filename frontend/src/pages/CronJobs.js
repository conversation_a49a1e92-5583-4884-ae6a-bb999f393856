import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Clock,
  Play,
  Pause,
  RefreshCw,
  Search,
  Filter,
  Eye,
  Plus,
  Trash2,
  Zap
} from 'lucide-react';
import { cronJobsAPI, formatRelativeTime, getStatusColor } from '../services/api';
import toast from 'react-hot-toast';
import LoadingSpinner from '../components/LoadingSpinner';

const CronJobs = () => {
  const [cronJobs, setCronJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const fetchCronJobs = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      const response = await cronJobsAPI.getAll();
      setCronJobs(response.data.data);
    } catch (error) {
      console.error('Error fetching cron jobs:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchCronJobs();
    
    // Auto-refresh every 10 seconds
    const interval = setInterval(() => fetchCronJobs(false), 10000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchCronJobs(true);
  };

  const handleTriggerJob = async (jobId) => {
    try {
      await cronJobsAPI.trigger(jobId);
      toast.success('Cron job triggered successfully!');
      fetchCronJobs();
    } catch (error) {
      console.error('Error triggering cron job:', error);
      toast.error(error.response?.data?.error || 'Failed to trigger cron job');
    }
  };

  const handleDeleteJob = async (jobId) => {
    if (!window.confirm('Are you sure you want to delete this cron job?')) {
      return;
    }

    try {
      await cronJobsAPI.delete(jobId);
      toast.success('Cron job deleted successfully!');
      fetchCronJobs();
    } catch (error) {
      console.error('Error deleting cron job:', error);
      toast.error(error.response?.data?.error || 'Failed to delete cron job');
    }
  };

  const filteredJobs = cronJobs.filter(job => {
    const matchesSearch = job.codes.some(code => 
      code.toLowerCase().includes(searchTerm.toLowerCase())
    ) || job.jobId.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || job.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Cron Jobs</h1>
          <p className="mt-1 text-sm text-gray-500">
            Monitor and manage your automated deal tracking jobs
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Link
            to="/cron-jobs/create"
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Create Job</span>
          </Link>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search by code or job ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none bg-white"
          >
            <option value="all">All Status</option>
            <option value="running">Running</option>
            <option value="stopped">Stopped</option>
            <option value="error">Error</option>
          </select>
        </div>
      </div>

      {/* Jobs Grid */}
      {filteredJobs.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredJobs.map((job) => (
            <div key={job.jobId} className="card hover:shadow-lg transition-shadow duration-200">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`status-indicator status-${job.status}`} />
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">
                      {Array.isArray(job.codes) ? job.codes.join(', ') : job.codes}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {job.scheduleType === 'time' 
                        ? `Daily at ${job.scheduleTime} IST`
                        : `Every ${job.intervalMinutes} minutes`
                      }
                    </p>
                  </div>
                </div>
                <span className={`badge badge-${getStatusColor(job.status)}`}>
                  {job.status}
                </span>
              </div>

              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Threshold:</span>
                  <span className="font-medium">{job.threshold}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Success Rate:</span>
                  <span className="font-medium">{job.successRate}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Items Found:</span>
                  <span className="font-medium">{job.totalItemsFound || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Next Run:</span>
                  <span className="font-medium">{job.nextRun}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Running For:</span>
                  <span className="font-medium">{job.runningFor} minutes</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">API Type:</span>
                  <span className="font-medium">
                    {job.api_type === 'new' ? (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        New API ({job.price_calculation || 'offer'})
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        Existing API
                      </span>
                    )}
                  </span>
                </div>
                {job.lastRun && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Last Run:</span>
                    <span className="font-medium">{formatRelativeTime(job.lastRun)}</span>
                  </div>
                )}
              </div>

              <div className="mt-6 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleTriggerJob(job.jobId)}
                    className="p-2 text-green-400 hover:text-green-600 rounded-lg hover:bg-green-50"
                    title="Trigger Now"
                  >
                    <Zap className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteJob(job.jobId)}
                    className="p-2 text-red-400 hover:text-red-600 rounded-lg hover:bg-red-50"
                    title="Delete Job"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
                <Link
                  to={`/cron-jobs/${job.jobId}`}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Eye className="h-4 w-4" />
                  <span>View Details</span>
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Clock className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No cron jobs found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || statusFilter !== 'all' 
              ? 'Try adjusting your search or filter criteria.'
              : 'Start by creating your first cron job in the Telegram bot.'
            }
          </p>
        </div>
      )}

      {/* Summary */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">{cronJobs.length}</p>
            <p className="text-sm text-gray-500">Total Jobs</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-success-600">
              {cronJobs.filter(job => job.status === 'running').length}
            </p>
            <p className="text-sm text-gray-500">Running</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-warning-600">
              {cronJobs.filter(job => job.status === 'stopped').length}
            </p>
            <p className="text-sm text-gray-500">Stopped</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-danger-600">
              {cronJobs.filter(job => job.status === 'error').length}
            </p>
            <p className="text-sm text-gray-500">Errors</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CronJobs;
