import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  ExternalLink, 
  TrendingDown, 
  TrendingUp,
  Package,
  Calendar,
  DollarSign,
  BarChart3
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { productsAPI, formatPrice, formatDate, getDiscountColor } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';

const ProductDetails = () => {
  const { productId } = useParams();
  const [productData, setProductData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProductHistory = async () => {
      try {
        const response = await productsAPI.getHistory(productId);
        setProductData(response.data.data);
      } catch (error) {
        console.error('Error fetching product history:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProductHistory();
  }, [productId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (!productData || !productData.product) {
    return (
      <div className="text-center py-12">
        <Package className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Product not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The requested product could not be found or has no history data.
        </p>
        <Link to="/cron-jobs" className="mt-4 btn-primary inline-flex items-center">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Cron Jobs
        </Link>
      </div>
    );
  }

  const { product, priceHistory } = productData;
  const discountColorClass = getDiscountColor(product.discountPercentage);

  // Prepare chart data
  const chartData = priceHistory.map((entry, index) => ({
    index: priceHistory.length - index,
    price: entry.price,
    originalPrice: entry.originalPrice,
    discount: entry.discountPercentage,
    date: formatDate(entry.timestamp),
    timestamp: entry.timestamp
  })).reverse();

  // Calculate price trend
  const priceTrend = priceHistory.length >= 2 
    ? priceHistory[0].price - priceHistory[1].price
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          to="/cron-jobs"
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Product Details</h1>
          <p className="mt-1 text-sm text-gray-500">Price history and tracking information</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Product Info */}
        <div className="lg:col-span-1">
          <div className="card">
            {/* Product Image */}
            <div className="relative mb-4">
              {product.image ? (
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-64 object-cover rounded-lg"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'flex';
                  }}
                />
              ) : null}
              <div 
                className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center"
                style={{ display: product.image ? 'none' : 'flex' }}
              >
                <Package className="h-16 w-16 text-gray-400" />
              </div>
              
              {/* Discount Badge */}
              <div className={`absolute top-2 right-2 px-3 py-1 rounded-full text-sm font-bold ${discountColorClass}`}>
                {product.discountPercentage}% OFF
              </div>
            </div>

            {/* Product Details */}
            <div className="space-y-3">
              <h2 className="text-lg font-semibold text-gray-900">{product.name}</h2>
              
              {product.brand && product.brand !== 'Unknown' && (
                <p className="text-sm text-gray-600">Brand: {product.brand}</p>
              )}
              
              {product.category && product.category !== 'Unknown' && (
                <p className="text-sm text-gray-600">Category: {product.category}</p>
              )}

              {/* Current Price */}
              <div className="border-t pt-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Current Price:</span>
                  <div className="text-right">
                    <p className="text-xl font-bold text-gray-900">
                      {formatPrice(product.offerPrice)}
                    </p>
                    {product.originalPrice > product.offerPrice && (
                      <p className="text-sm text-gray-500 line-through">
                        {formatPrice(product.originalPrice)}
                      </p>
                    )}
                  </div>
                </div>
                {product.unit && (
                  <p className="text-xs text-gray-500 text-right">per {product.unit}</p>
                )}
              </div>

              {/* Stock Status */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Stock Status:</span>
                <span className={`text-sm font-medium ${
                  product.inStock ? 'text-green-600' : 'text-red-600'
                }`}>
                  {product.inStock ? 'In Stock' : 'Out of Stock'}
                </span>
              </div>

              {/* Price Trend */}
              {priceTrend !== 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Price Trend:</span>
                  <div className="flex items-center space-x-1">
                    {priceTrend < 0 ? (
                      <TrendingDown className="h-4 w-4 text-green-500" />
                    ) : (
                      <TrendingUp className="h-4 w-4 text-red-500" />
                    )}
                    <span className={`text-sm font-medium ${
                      priceTrend < 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatPrice(Math.abs(priceTrend))}
                    </span>
                  </div>
                </div>
              )}

              {/* Action Button */}
              {(product.url || product.productUrl) ? (
                <a
                  href={product.url || product.productUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-full btn-primary flex items-center justify-center space-x-2"
                >
                  <span>View on Swiggy</span>
                  <ExternalLink className="h-4 w-4" />
                </a>
              ) : (
                <div className="w-full bg-gray-400 text-white text-center py-2 px-4 rounded-lg cursor-not-allowed">
                  <span>No URL Available</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Price History */}
        <div className="lg:col-span-2 space-y-6">
          {/* Price Chart */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Price History</h3>
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-500">Last {priceHistory.length} records</span>
              </div>
            </div>
            
            {chartData.length > 1 ? (
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="index" 
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis 
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => `₹${value}`}
                    />
                    <Tooltip 
                      formatter={(value, name) => [
                        `₹${value}`, 
                        name === 'price' ? 'Offer Price' : 'Original Price'
                      ]}
                      labelFormatter={(label) => `Record #${label}`}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="price" 
                      stroke="#f97316" 
                      strokeWidth={2}
                      dot={{ fill: '#f97316', strokeWidth: 2, r: 4 }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="originalPrice" 
                      stroke="#6b7280" 
                      strokeWidth={1}
                      strokeDasharray="5 5"
                      dot={{ fill: '#6b7280', strokeWidth: 1, r: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-64 flex items-center justify-center text-gray-500">
                <p>Not enough data points for chart visualization</p>
              </div>
            )}
          </div>

          {/* Price History Table */}
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Last 5 Distinct Prices</h3>
            
            {priceHistory.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="table-header px-6 py-3">Date</th>
                      <th className="table-header px-6 py-3">Offer Price</th>
                      <th className="table-header px-6 py-3">Original Price</th>
                      <th className="table-header px-6 py-3">Discount</th>
                      <th className="table-header px-6 py-3">Stock</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {priceHistory.map((entry, index) => (
                      <tr key={index} className="table-row">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(entry.timestamp)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {formatPrice(entry.price)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatPrice(entry.originalPrice)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getDiscountColor(entry.discountPercentage)}`}>
                            {entry.discountPercentage}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            entry.inStock 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {entry.inStock ? 'In Stock' : 'Out of Stock'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No price history</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Price history will appear here as the product is tracked over time.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetails;
