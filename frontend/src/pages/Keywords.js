import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Trash2, 
  Target, 
  Search,
  AlertCircle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { keywordsAPI } from '../services/api';
import toast from 'react-hot-toast';
import LoadingSpinner from '../components/LoadingSpinner';

const Keywords = () => {
  const [keywords, setKeywords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [chatId, setChatId] = useState(0);
  const [newKeyword, setNewKeyword] = useState({
    keyword: '',
    threshold: 90
  });

  const fetchKeywords = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      const response = await keywordsAPI.getAll(chatId);
      setKeywords(response.data.data);
    } catch (error) {
      console.error('Error fetching keywords:', error);
      if (!error.config?.silent) {
        toast.error('Failed to fetch keywords');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchKeywords();
  }, [chatId]);

  const handleRefresh = () => {
    fetchKeywords(true);
  };

  const handleAddKeyword = async (e) => {
    e.preventDefault();
    
    if (!newKeyword.keyword.trim()) {
      toast.error('Please enter a keyword');
      return;
    }

    if (newKeyword.threshold < 10 || newKeyword.threshold > 95) {
      toast.error('Threshold must be between 10 and 95');
      return;
    }

    try {
      await keywordsAPI.set(chatId, newKeyword.keyword.trim(), newKeyword.threshold);
      toast.success('Keyword rule added successfully!');
      setNewKeyword({ keyword: '', threshold: 90 });
      setShowAddForm(false);
      fetchKeywords();
    } catch (error) {
      console.error('Error adding keyword:', error);
      toast.error(error.response?.data?.error || 'Failed to add keyword rule');
    }
  };

  const handleDeleteKeyword = async (keyword) => {
    if (!window.confirm(`Are you sure you want to delete the rule for "${keyword}"?`)) {
      return;
    }

    try {
      await keywordsAPI.delete(chatId, keyword);
      toast.success('Keyword rule deleted successfully!');
      fetchKeywords();
    } catch (error) {
      console.error('Error deleting keyword:', error);
      toast.error(error.response?.data?.error || 'Failed to delete keyword rule');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Keyword Rules</h1>
          <p className="mt-1 text-sm text-gray-500">
            Set custom discount thresholds for specific keywords in product names
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add Rule</span>
          </button>
        </div>
      </div>

      {/* Chat ID Selector */}
      <div className="card">
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">
            Chat ID:
          </label>
          <input
            type="number"
            value={chatId}
            onChange={(e) => setChatId(parseInt(e.target.value) || 0)}
            placeholder="Enter Telegram Chat ID"
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <p className="text-sm text-gray-500">
            Enter your Telegram Chat ID to manage keyword rules (0 for global rules)
          </p>
        </div>
      </div>

      {/* Add Keyword Form */}
      {showAddForm && (
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Add New Keyword Rule</h3>
            <button
              onClick={() => setShowAddForm(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <Plus className="h-5 w-5 transform rotate-45" />
            </button>
          </div>
          
          <form onSubmit={handleAddKeyword} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Keyword
                </label>
                <input
                  type="text"
                  value={newKeyword.keyword}
                  onChange={(e) => setNewKeyword({ ...newKeyword, keyword: e.target.value })}
                  placeholder="e.g., lifelong, premium, unlimited"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Case-insensitive keyword to match in product names
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Discount (%)
                </label>
                <div className="flex items-center space-x-3">
                  <input
                    type="range"
                    min="10"
                    max="95"
                    value={newKeyword.threshold}
                    onChange={(e) => setNewKeyword({ ...newKeyword, threshold: parseInt(e.target.value) })}
                    className="flex-1"
                  />
                  <div className="w-16 text-center">
                    <span className="text-lg font-bold text-primary-600">{newKeyword.threshold}%</span>
                  </div>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Required discount for products containing this keyword
                </p>
              </div>
            </div>
            
            <div className="flex items-center justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn-primary flex items-center space-x-2"
              >
                <CheckCircle className="h-4 w-4" />
                <span>Add Rule</span>
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Keywords List */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Active Keyword Rules</h3>
          <div className="flex items-center space-x-2">
            <Target className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-500">{keywords.length} rules</span>
          </div>
        </div>

        {keywords.length > 0 ? (
          <div className="space-y-3">
            {keywords.map((rule, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4 text-gray-400" />
                    <span className="font-medium text-gray-900">"{rule.keyword}"</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Target className="h-4 w-4 text-primary-600" />
                    <span className="text-sm text-gray-600">
                      Requires <span className="font-bold text-primary-600">{rule.threshold}%</span> discount
                    </span>
                  </div>
                </div>
                
                <button
                  onClick={() => handleDeleteKeyword(rule.keyword)}
                  className="p-2 text-red-400 hover:text-red-600 rounded-lg hover:bg-red-50 transition-colors"
                  title="Delete rule"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Target className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No keyword rules</h3>
            <p className="mt-1 text-sm text-gray-500">
              Add keyword rules to set custom discount thresholds for specific product types.
            </p>
            <button
              onClick={() => setShowAddForm(true)}
              className="mt-4 btn-primary inline-flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add First Rule</span>
            </button>
          </div>
        )}
      </div>

      {/* How It Works */}
      <div className="card bg-blue-50 border-blue-200">
        <div className="flex items-start space-x-3">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="text-sm font-medium text-blue-900">How Keyword Rules Work</h3>
            <div className="mt-2 text-sm text-blue-700 space-y-1">
              <p>• Keyword rules apply to ALL your cron jobs automatically</p>
              <p>• If a product name contains any keyword, it must meet the specified discount threshold</p>
              <p>• Keywords are case-insensitive (e.g., "LIFELONG" matches "lifelong")</p>
              <p>• Products without matching keywords use the regular cron job threshold</p>
              <p>• Example: "lifelong" → 90% means any product with "lifelong" needs ≥90% discount</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Keywords;
