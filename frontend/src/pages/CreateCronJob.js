import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Plus, 
  X, 
  Clock, 
  Calendar,
  Target,
  Code,
  Save
} from 'lucide-react';
import { cronJobsAPI, mappingsAPI } from '../services/api';
import toast from 'react-hot-toast';
import LoadingSpinner from '../components/LoadingSpinner';
import CodeTypeahead from '../components/CodeTypeahead';

const CreateCronJob = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [mappings, setMappings] = useState(null);
  const [formData, setFormData] = useState({
    codes: '',
    scheduleType: 'interval',
    intervalMinutes: 10,
    scheduleTime: '09:00',
    threshold: 65,
    chatId: 0,
    apiType: 'existing', // 'existing' or 'new'
    priceCalculation: 'offer', // 'offer' or 'maxsaver' (for new API)
    hideRepeatedItems: false // Hide items that were already shown
  });

  useEffect(() => {
    const fetchMappings = async () => {
      try {
        const response = await mappingsAPI.getAll();
        setMappings(response.data.data);
      } catch (error) {
        console.error('Error fetching mappings:', error);
        toast.error('Failed to load mapping codes');
      }
    };

    fetchMappings();
  }, []);

  const handleCodesChange = (value) => {
    setFormData({ ...formData, codes: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate codes
      const validCodes = formData.codes.split(',').map(code => code.trim().toUpperCase()).filter(code => code.length > 0);
      if (validCodes.length === 0) {
        toast.error('Please enter at least one mapping code');
        setLoading(false);
        return;
      }

      // Validate schedule
      if (formData.scheduleType === 'interval' && (formData.intervalMinutes < 5 || formData.intervalMinutes > 1440)) {
        toast.error('Interval must be between 5 and 1440 minutes');
        setLoading(false);
        return;
      }

      if (formData.scheduleType === 'time' && !formData.scheduleTime) {
        toast.error('Please enter a valid time');
        setLoading(false);
        return;
      }

      // Validate threshold
      if (formData.threshold < 10 || formData.threshold > 95) {
        toast.error('Threshold must be between 10 and 95');
        setLoading(false);
        return;
      }

      const jobData = {
        codes: validCodes,
        scheduleType: formData.scheduleType,
        intervalMinutes: formData.scheduleType === 'interval' ? formData.intervalMinutes : null,
        scheduleTime: formData.scheduleType === 'time' ? formData.scheduleTime : null,
        threshold: formData.threshold,
        chatId: formData.chatId,
        apiType: formData.apiType,
        priceCalculation: formData.priceCalculation,
        hideRepeatedItems: formData.hideRepeatedItems
      };

      await cronJobsAPI.create(jobData);
      toast.success('Cron job created successfully!');
      navigate('/cron-jobs');
    } catch (error) {
      console.error('Error creating cron job:', error);
      toast.error(error.response?.data?.error || 'Failed to create cron job');
    } finally {
      setLoading(false);
    }
  };

  const getCodeSuggestions = () => {
    if (!mappings) return [];
    
    const suggestions = [];
    
    // Add category-only codes
    mappings.categories.forEach(category => {
      suggestions.push({
        code: category.code,
        description: `${category.name} (ALL FILTERS)`,
        type: 'category'
      });
      
      // Add filter codes
      if (category.filters) {
        category.filters.forEach(filter => {
          suggestions.push({
            code: filter.code,
            description: `${category.name} / ${filter.name}`,
            type: 'filter'
          });
        });
      }
    });
    
    return suggestions;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/cron-jobs')}
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Cron Job</h1>
          <p className="mt-1 text-sm text-gray-500">
            Set up automated deal monitoring with custom parameters
          </p>
        </div>
      </div>

      <div className="max-w-2xl">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Mapping Codes */}
          <div className="card">
            <div className="flex items-center space-x-2 mb-4">
              <Code className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-medium text-gray-900">Mapping Codes</h3>
            </div>
            
            <div className="space-y-3">
              <CodeTypeahead
                value={formData.codes}
                onChange={handleCodesChange}
                placeholder="Enter mapping codes (e.g., A1, B2, C3)"
                mappings={mappings}
                multiple={true}
              />
              <p className="text-sm text-gray-500">
                Use commas to separate multiple codes. Start typing to see suggestions.
              </p>
            </div>

            {/* Code Suggestions */}
            {mappings && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-700 mb-2">Available Codes:</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                  {getCodeSuggestions().slice(0, 20).map((suggestion, index) => (
                    <div key={index} className="text-xs">
                      <span className="font-mono font-bold text-primary-600">{suggestion.code}</span>
                      <span className="text-gray-600"> - {suggestion.description}</span>
                    </div>
                  ))}
                </div>
                {getCodeSuggestions().length > 20 && (
                  <p className="text-xs text-gray-500 mt-2">
                    And {getCodeSuggestions().length - 20} more codes available...
                  </p>
                )}
              </div>
            )}
          </div>

          {/* API Configuration */}
          <div className="card">
            <div className="flex items-center space-x-2 mb-4">
              <Target className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-medium text-gray-900">API Configuration</h3>
            </div>

            <div className="space-y-4">
              {/* API Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  API Type
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, apiType: 'existing', priceCalculation: 'offer' })}
                    className={`p-3 border rounded-lg text-left transition-colors ${
                      formData.apiType === 'existing'
                        ? 'border-primary-300 bg-primary-50 text-primary-900'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Code className="h-4 w-4" />
                      <span className="font-medium">Existing API</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Use current deal discovery API</p>
                  </button>

                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, apiType: 'new' })}
                    className={`p-3 border rounded-lg text-left transition-colors ${
                      formData.apiType === 'new'
                        ? 'border-primary-300 bg-primary-50 text-primary-900'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Target className="h-4 w-4" />
                      <span className="font-medium">New API</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Use enhanced API with Max Saver</p>
                  </button>
                </div>
              </div>

              {/* Price Calculation for New API */}
              {formData.apiType === 'new' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price Calculation Method
                  </label>
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      type="button"
                      onClick={() => setFormData({ ...formData, priceCalculation: 'offer' })}
                      className={`p-3 border rounded-lg text-left transition-colors ${
                        formData.priceCalculation === 'offer'
                          ? 'border-primary-300 bg-primary-50 text-primary-900'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">Offer Price</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">Calculate discounts from offer price</p>
                    </button>

                    <button
                      type="button"
                      onClick={() => setFormData({ ...formData, priceCalculation: 'maxsaver' })}
                      className={`p-3 border rounded-lg text-left transition-colors ${
                        formData.priceCalculation === 'maxsaver'
                          ? 'border-primary-300 bg-primary-50 text-primary-900'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">Max Saver Price</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">Calculate discounts from Max Saver price</p>
                    </button>
                  </div>
                </div>
              )}

              {/* API Information */}
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="text-sm">
                    <p className="text-blue-800 font-medium">API Selection Guide:</p>
                    <ul className="mt-1 text-blue-700 space-y-1">
                      <li>• <strong>Existing API:</strong> Uses current deal discovery with standard pricing</li>
                      <li>• <strong>New API:</strong> Enhanced with Max Saver pricing for better deal detection</li>
                      {formData.apiType === 'new' && (
                        <>
                          <li>• <strong>Offer Price:</strong> Standard discount calculation from regular offer price</li>
                          <li>• <strong>Max Saver Price:</strong> Enhanced discount calculation using Max Saver membership pricing</li>
                        </>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Schedule Configuration */}
          <div className="card">
            <div className="flex items-center space-x-2 mb-4">
              <Clock className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-medium text-gray-900">Schedule</h3>
            </div>

            <div className="space-y-4">
              {/* Schedule Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Schedule Type
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, scheduleType: 'interval' })}
                    className={`p-3 border rounded-lg text-left transition-colors ${
                      formData.scheduleType === 'interval'
                        ? 'border-primary-300 bg-primary-50 text-primary-900'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4" />
                      <span className="font-medium">Interval</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Run every X minutes</p>
                  </button>
                  
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, scheduleType: 'time' })}
                    className={`p-3 border rounded-lg text-left transition-colors ${
                      formData.scheduleType === 'time'
                        ? 'border-primary-300 bg-primary-50 text-primary-900'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4" />
                      <span className="font-medium">Daily Time</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">Run daily at specific time</p>
                  </button>
                </div>
              </div>

              {/* Interval Input */}
              {formData.scheduleType === 'interval' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Interval (minutes)
                  </label>
                  <input
                    type="number"
                    min="5"
                    max="1440"
                    value={formData.intervalMinutes}
                    onChange={(e) => setFormData({ ...formData, intervalMinutes: parseInt(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Between 5 and 1440 minutes (24 hours)
                  </p>
                </div>
              )}

              {/* Time Input */}
              {formData.scheduleType === 'time' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Time (IST)
                  </label>
                  <input
                    type="time"
                    value={formData.scheduleTime}
                    onChange={(e) => setFormData({ ...formData, scheduleTime: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Time in Indian Standard Time (IST)
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Threshold Configuration */}
          <div className="card">
            <div className="flex items-center space-x-2 mb-4">
              <Target className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-medium text-gray-900">Discount Threshold</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Discount Percentage
              </label>
              <div className="flex items-center space-x-4">
                <input
                  type="range"
                  min="10"
                  max="95"
                  value={formData.threshold}
                  onChange={(e) => setFormData({ ...formData, threshold: parseInt(e.target.value) })}
                  className="flex-1"
                />
                <div className="w-16 text-center">
                  <span className="text-lg font-bold text-primary-600">{formData.threshold}%</span>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Only notify for deals with at least {formData.threshold}% discount
              </p>
            </div>
          </div>

          {/* Advanced Options */}
          <div className="card">
            <div className="flex items-center space-x-2 mb-4">
              <Target className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-medium text-gray-900">Advanced Options</h3>
            </div>

            <div className="space-y-4">
              {/* Hide Repeated Items */}
              <div className="flex items-start space-x-3">
                <div className="flex items-center h-5">
                  <input
                    id="hideRepeatedItems"
                    type="checkbox"
                    checked={formData.hideRepeatedItems}
                    onChange={(e) => setFormData({ ...formData, hideRepeatedItems: e.target.checked })}
                    className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2"
                  />
                </div>
                <div className="text-sm">
                  <label htmlFor="hideRepeatedItems" className="font-medium text-gray-700">
                    Hide repeated items
                  </label>
                  <p className="text-gray-500">
                    Don't show items that were already displayed unless their pricing has changed significantly (>1% difference)
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Chat ID (Optional) */}
          <div className="card">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chat ID (Optional)
              </label>
              <input
                type="number"
                value={formData.chatId}
                onChange={(e) => setFormData({ ...formData, chatId: parseInt(e.target.value) || 0 })}
                placeholder="0 for dashboard-only jobs"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              <p className="text-sm text-gray-500 mt-1">
                Leave as 0 for dashboard-only monitoring, or enter Telegram chat ID for notifications
              </p>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-end space-x-3">
            <button
              type="button"
              onClick={() => navigate('/cron-jobs')}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center space-x-2"
            >
              {loading ? (
                <LoadingSpinner size="sm" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{loading ? 'Creating...' : 'Create Cron Job'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateCronJob;
