import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Search, 
  Target,
  Code,
  Zap,
  ShoppingCart,
  TrendingDown
} from 'lucide-react';
import { mappingsAPI } from '../services/api';
import toast from 'react-hot-toast';
import LoadingSpinner from '../components/LoadingSpinner';
import ProductCard from '../components/ProductCard';
import CodeTypeahead from '../components/CodeTypeahead';
import api from '../services/api';

const FindDeals = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [mappings, setMappings] = useState(null);
  const [results, setResults] = useState(null);
  const [logs, setLogs] = useState([]);
  const [streaming, setStreaming] = useState(true);
  const [formData, setFormData] = useState({
    codes: '',
    threshold: 65,
    apiType: 'existing',
    priceCalculation: 'offer'
  });

  useEffect(() => {
    const fetchMappings = async () => {
      try {
        const response = await mappingsAPI.getAll();
        setMappings(response.data.data);
      } catch (error) {
        console.error('Error fetching mappings:', error);
        toast.error('Failed to load mapping codes');
      }
    };

    fetchMappings();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setResults(null);
    setLogs([]);

    try {
      // Validate codes
      const codes = formData.codes.trim().split(',').map(code => code.trim().toUpperCase()).filter(code => code.length > 0);
      
      if (codes.length === 0) {
        toast.error('Please enter at least one mapping code');
        setLoading(false);
        return;
      }

      // Validate threshold
      if (formData.threshold < 10 || formData.threshold > 95) {
        toast.error('Threshold must be between 10 and 95');
        setLoading(false);
        return;
      }

      if (streaming) {
        // Use SSE streaming
        const params = new URLSearchParams({
          codes: codes.join(','),
          threshold: String(formData.threshold),
          apiType: formData.apiType,
          priceCalculation: formData.priceCalculation
        });

        const es = new EventSource(`/api/deals/find/stream?${params.toString()}`);

        es.addEventListener('log', (ev) => {
          try {
            const payload = JSON.parse(ev.data);
            setLogs(prev => [...prev, payload.message]);
          } catch {}
        });

        es.addEventListener('progress', (ev) => {
          try {
            const payload = JSON.parse(ev.data);
            setLogs(prev => [...prev, `Processed ${payload.code}: ${payload.found} items`]);
          } catch {}
        });

        es.addEventListener('done', (ev) => {
          try {
            const payload = JSON.parse(ev.data);
            if (payload.success) {
              setResults(payload.data);
              toast.success(`Found ${payload.data.totalItems} deals!`);
            } else {
              toast.error(payload.error || 'Failed to find deals');
            }
          } catch {}
          es.close();
          setLoading(false);
        });

        es.addEventListener('error', (ev) => {
          try {
            const payload = JSON.parse(ev.data);
            toast.error(payload.message || 'Stream error');
          } catch {
            toast.error('Stream error');
          }
          es.close();
          setLoading(false);
        });
      } else {
        // Classic non-streaming request
        const response = await fetch('/api/deals/find', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            codes,
            threshold: formData.threshold,
            apiType: formData.apiType,
            priceCalculation: formData.priceCalculation
          })
        });

        const data = await response.json();

        if (data.success) {
          setResults(data.data);
          toast.success(`Found ${data.data.totalItems} deals!`);
        } else {
          toast.error(data.error || 'Failed to find deals');
        }
      }
    } catch (error) {
      console.error('Error finding deals:', error);
      toast.error('Failed to find deals');
    } finally {
      if (!streaming) setLoading(false);
    }
  };

  const getCodeSuggestions = () => {
    if (!mappings) return [];
    
    const suggestions = [];
    
    // Add category-only codes
    mappings.categories.forEach(category => {
      suggestions.push({
        code: category.code,
        description: `${category.name} (ALL FILTERS)`,
        type: 'category'
      });
      
      // Add filter codes
      if (category.filters) {
        category.filters.forEach(filter => {
          suggestions.push({
            code: filter.code,
            description: `${category.name} / ${filter.name}`,
            type: 'filter'
          });
        });
      }
    });
    
    return suggestions;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/')}
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <ArrowLeft className="h-5 w-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Find Deals by Code</h1>
          <p className="mt-1 text-sm text-gray-500">
            Search for discounted items using mapping codes
          </p>
        </div>
      </div>

      {/* Search Form */}
      <div className="card">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Mapping Codes */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Code className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-medium text-gray-900">Mapping Codes</h3>
            </div>
            
            <div className="space-y-3">
              <CodeTypeahead
                value={formData.codes}
                onChange={(value) => setFormData({ ...formData, codes: value })}
                placeholder="Enter codes (e.g., A1, B2, C3 or A,B,C)"
                mappings={mappings}
                multiple={true}
              />
              <p className="text-sm text-gray-500">
                Use commas to separate multiple codes. Start typing to see suggestions.
              </p>
            </div>

            {/* Code Suggestions */}
            {mappings && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-700 mb-2">Popular Codes:</p>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-32 overflow-y-auto">
                  {getCodeSuggestions().slice(0, 12).map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => {
                        const currentCodes = formData.codes.trim();
                        const newCode = suggestion.code;
                        const updatedCodes = currentCodes 
                          ? `${currentCodes},${newCode}` 
                          : newCode;
                        setFormData({ ...formData, codes: updatedCodes });
                      }}
                      className="text-left p-2 text-xs bg-white rounded border hover:border-primary-300 hover:bg-primary-50"
                    >
                      <span className="font-mono font-bold text-primary-600">{suggestion.code}</span>
                      <div className="text-gray-600 truncate">{suggestion.description}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Threshold Configuration */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Target className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-medium text-gray-900">Discount Threshold</h3>
            </div>

            <div>
              <div className="flex items-center space-x-4">
                <input
                  type="range"
                  min="10"
                  max="95"
                  value={formData.threshold}
                  onChange={(e) => setFormData({ ...formData, threshold: parseInt(e.target.value) })}
                  className="flex-1"
                />
                <div className="w-16 text-center">
                  <span className="text-lg font-bold text-primary-600">{formData.threshold}%</span>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Only show deals with at least {formData.threshold}% discount
              </p>
            </div>
          </div>

          {/* API Options */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Zap className="h-5 w-5 text-primary-600" />
              <h3 className="text-lg font-medium text-gray-900">API Options</h3>
            </div>

            <div className="mb-4">
              <label className="inline-flex items-center space-x-2 text-sm text-gray-700">
                <input
                  type="checkbox"
                  checked={streaming}
                  onChange={(e) => setStreaming(e.target.checked)}
                />
                <span>Stream progress to this page</span>
              </label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">API Type</label>
                <select
                  value={formData.apiType}
                  onChange={(e) => setFormData({ ...formData, apiType: e.target.value })}
                  className="w-full border rounded-md p-2"
                >
                  <option value="existing">Existing API</option>
                  <option value="new">New API (Max Saver)</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Choose the data source. New API supports Max Saver pricing.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Price Calculation</label>
                <select
                  value={formData.priceCalculation}
                  onChange={(e) => setFormData({ ...formData, priceCalculation: e.target.value })}
                  className="w-full border rounded-md p-2"
                  disabled={formData.apiType !== 'new'}
                >
                  <option value="offer">Offer Price</option>
                  <option value="maxsaver">Max Saver Price</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Max Saver option applies only when using the New API.
                </p>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-end space-x-3">
            <button
              type="button"
              onClick={() => navigate('/')}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn-primary flex items-center space-x-2"
            >
              {loading ? (
                <LoadingSpinner size="sm" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              <span>{loading ? 'Searching...' : 'Find Deals'}</span>
            </button>
          </div>
        </form>
      </div>

      {/* Live Logs */}
      {streaming && logs.length > 0 && (
        <div className="card bg-gray-50">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Live Progress</h3>
          <div className="text-xs text-gray-700 space-y-1 max-h-48 overflow-y-auto">
            {logs.map((line, idx) => (
              <div key={idx}>{line}</div>
            ))}
          </div>
        </div>
      )}

      {/* Results */}
      {results && (
        <div className="space-y-6">
          {/* Results Summary */}
          <div className="card bg-green-50 border-green-200">
            <div className="flex items-center space-x-3">
              <ShoppingCart className="h-6 w-6 text-green-600" />
              <div>
                <h3 className="text-lg font-medium text-green-900">Search Results</h3>
                <div className="text-sm text-green-700 space-y-1">
                  <p>• <strong>{results.totalItems}</strong> deals found</p>
                  <p>• <strong>{results.codes.join(', ')}</strong> codes searched</p>
                  <p>• <strong>{results.threshold}%+</strong> discount threshold</p>
                  <p>• <strong>{results.duration}s</strong> search time</p>
                  {results.apiType && (
                    <p>• API: <strong>{results.apiType}</strong>{results.priceCalculation ? ` (${results.priceCalculation})` : ''}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          {results.items && results.items.length > 0 ? (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2">
                <TrendingDown className="h-5 w-5 text-primary-600" />
                <span>Discounted Items ({results.items.length})</span>
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {results.items.map((item, index) => (
                  <ProductCard key={item.id || index} product={item} />
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <ShoppingCart className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No deals found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try lowering the discount threshold or using different codes.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FindDeals;
