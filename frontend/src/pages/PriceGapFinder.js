import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { 
  Play, 
  Clock, 
  TrendingUp, 
  Package, 
  ExternalLink, 
  RefreshCw,
  Filter,
  Calendar,
  BarChart3,
  Eye
} from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';
import CodeTypeahead from '../components/CodeTypeahead';
import api from '../services/api';

const PriceGapFinder = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [currentRun, setCurrentRun] = useState(null);
  const [runHistory, setRunHistory] = useState([]);
  const [selectedRun, setSelectedRun] = useState(null);
  const [runResults, setRunResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ page: 1, limit: 20, total: 0 });
  const [mappings, setMappings] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    codes: 'J', // Default to Cleaning Essentials
    categories: '', // Legacy support
    threshold: 10,
    baseline: 'offer',
    limit: 20
  });

  useEffect(() => {
    loadRunHistory();
    loadMappings();
  }, []);

  const loadRunHistory = async () => {
    try {
      setLoading(true);
      const response = await api.get('/max-saver-gap-runs', {
        params: { limit: pagination.limit, offset: (pagination.page - 1) * pagination.limit }
      });
      setRunHistory(response.data.runs || []);
      setPagination(prev => ({ ...prev, total: response.data.total || 0 }));
    } catch (error) {
      console.error('Error loading run history:', error);
      toast.error('Failed to load run history');
    } finally {
      setLoading(false);
    }
  };

  const loadMappings = async () => {
    try {
      const response = await api.get('/mappings');
      if (response.data.success) {
        setMappings(response.data.data);
      }
    } catch (error) {
      console.error('Error loading mappings:', error);
      // Don't show error toast as this is not critical
    }
  };

  const loadRunResults = async (runId) => {
    try {
      setLoading(true);
      const response = await api.get(`/max-saver-gap-runs/${runId}/results`);
      setRunResults(response.data.results || []);
      setSelectedRun(runId);
    } catch (error) {
      console.error('Error loading run results:', error);
      toast.error('Failed to load run results');
    } finally {
      setLoading(false);
    }
  };

  const startGapFinder = async () => {
    try {
      setIsRunning(true);
      setCurrentRun(null);
      
      const response = await api.post('/max-saver-gap-finder/start', formData);
      
      if (response.data.success) {
        setCurrentRun(response.data.runId);
        toast.success('Price gap finder started!');
        
        // Poll for completion
        pollRunStatus(response.data.runId);
      } else {
        throw new Error(response.data.error || 'Failed to start gap finder');
      }
    } catch (error) {
      console.error('Error starting gap finder:', error);
      toast.error(error.response?.data?.error || 'Failed to start gap finder');
      setIsRunning(false);
    }
  };

  const pollRunStatus = async (runId) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await api.get(`/max-saver-gap-runs/${runId}/status`);
        
        if (response.data.status === 'completed') {
          clearInterval(pollInterval);
          setIsRunning(false);
          setCurrentRun(null);
          toast.success('Price gap finder completed!');
          loadRunHistory(); // Refresh history
        } else if (response.data.status === 'failed') {
          clearInterval(pollInterval);
          setIsRunning(false);
          setCurrentRun(null);
          toast.error('Price gap finder failed');
        }
      } catch (error) {
        console.error('Error polling run status:', error);
        clearInterval(pollInterval);
        setIsRunning(false);
        setCurrentRun(null);
      }
    }, 5000); // Poll every 5 seconds

    // Stop polling after 30 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      if (isRunning) {
        setIsRunning(false);
        setCurrentRun(null);
        toast.error('Run timeout - please check manually');
      }
    }, 30 * 60 * 1000);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Price Gap Finder</h1>
            <p className="mt-1 text-sm text-gray-500">
              Find items where Max Saver offers additional savings over regular prices
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={loadRunHistory}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Configuration Form */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Configuration</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Codes
            </label>
            <CodeTypeahead
              value={formData.codes}
              onChange={(value) => setFormData(prev => ({ ...prev, codes: value }))}
              placeholder="Type to search category codes..."
              mappings={mappings}
              disabled={isRunning}
              multiple={true}
            />

            {/* Code Suggestions */}
            {mappings && (
              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-700 mb-2">Popular Codes:</p>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {mappings.categories.slice(0, 6).map((category, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => {
                        const currentCodes = formData.codes ? formData.codes.split(',').map(c => c.trim()).filter(Boolean) : [];
                        if (!currentCodes.includes(category.code)) {
                          const newCodes = [...currentCodes, category.code];
                          setFormData(prev => ({ ...prev, codes: newCodes.join(', ') }));
                        }
                      }}
                      disabled={isRunning}
                      className="text-left p-2 text-xs bg-white border border-gray-200 rounded hover:bg-gray-50 disabled:opacity-50"
                    >
                      <span className="font-mono font-bold text-primary-600">{category.code}</span>
                      <div className="text-gray-600 truncate">{category.name}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Threshold (%)
            </label>
            <input
              type="number"
              value={formData.threshold}
              onChange={(e) => setFormData(prev => ({ ...prev, threshold: parseInt(e.target.value) || 0 }))}
              min="1"
              max="100"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              disabled={isRunning}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Baseline Price
            </label>
            <select
              value={formData.baseline}
              onChange={(e) => setFormData(prev => ({ ...prev, baseline: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              disabled={isRunning}
            >
              <option value="offer">Offer Price</option>
              <option value="store">Store Price</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Limit per Category
            </label>
            <input
              type="number"
              value={formData.limit}
              onChange={(e) => setFormData(prev => ({ ...prev, limit: parseInt(e.target.value) || 0 }))}
              min="1"
              max="100"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              disabled={isRunning}
            />
          </div>
        </div>

        {/* Legacy Categories Input */}
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <details className="group">
            <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
              Advanced: Use Category Names (Legacy)
            </summary>
            <div className="mt-3">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category Names
              </label>
              <input
                type="text"
                value={formData.categories}
                onChange={(e) => setFormData(prev => ({ ...prev, categories: e.target.value }))}
                placeholder="e.g., Cleaning Essentials,Fresh Fruits"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                disabled={isRunning}
              />
              <p className="mt-1 text-xs text-gray-500">
                Comma-separated category names (use only if codes are not available)
              </p>
            </div>
          </details>
        </div>

        <div className="mt-6">
          <button
            onClick={startGapFinder}
            disabled={isRunning || (!formData.codes.trim() && !formData.categories.trim())}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Running...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Start Gap Finder
              </>
            )}
          </button>
        </div>

        {currentRun && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-blue-400 mr-2" />
              <span className="text-sm text-blue-800">
                Running gap finder... (Run ID: {currentRun})
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Run History */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Run History</h2>
        </div>
        
        {loading && !runHistory.length ? (
          <div className="p-6 text-center">
            <LoadingSpinner />
          </div>
        ) : runHistory.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            No runs found. Start your first gap finder run above.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Run Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Configuration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Results
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Performance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {runHistory.map((run) => (
                  <tr key={run.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {run.run_id}
                      </div>
                      <div className="text-sm text-gray-500">
                        <Calendar className="inline h-4 w-4 mr-1" />
                        {formatDate(run.created_at)}
                      </div>
                      <div className="mt-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          run.status === 'completed' 
                            ? 'bg-green-100 text-green-800'
                            : run.status === 'running'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {run.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>Baseline: {run.baseline}</div>
                      <div>Threshold: {run.threshold_percentage}%</div>
                      <div>Limit: {run.limit_per_category}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Package className="h-4 w-4 mr-1" />
                        {run.total_items_found} items
                      </div>
                      <div className="flex items-center">
                        <Filter className="h-4 w-4 mr-1" />
                        {run.total_categories} categories
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {formatDuration(run.execution_time_ms)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => loadRunResults(run.run_id)}
                        className="text-primary-600 hover:text-primary-900 inline-flex items-center"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Results
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Results Modal/Section */}
      {selectedRun && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">
              Results for {selectedRun}
            </h2>
            <button
              onClick={() => setSelectedRun(null)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          
          {loading ? (
            <div className="p-6 text-center">
              <LoadingSpinner />
            </div>
          ) : runResults.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No results found for this run.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category/Filter
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pricing
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Gap
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {runResults.map((result, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-gray-900">
                          {result.item_name}
                        </div>
                        {result.quantity && result.unit && (
                          <div className="text-sm text-gray-500">
                            {result.quantity} {result.unit}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>{result.category_name}</div>
                        {result.filter_name && (
                          <div className="text-xs text-gray-400">{result.filter_name}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>Baseline: {formatCurrency(result.baseline_price)}</div>
                        <div>Max Saver: {formatCurrency(result.max_saver_price)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          {result.gap_percentage}% extra off
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {result.item_url && (
                          <a
                            href={result.item_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary-600 hover:text-primary-900 inline-flex items-center"
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            View
                          </a>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PriceGapFinder;
