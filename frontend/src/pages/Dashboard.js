import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Clock,
  TrendingUp,
  Activity,
  ShoppingCart,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Code,
  History
} from 'lucide-react';
import { dashboardAPI, formatRelativeTime } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import StatCard from '../components/StatCard';

const Dashboard = () => {
  const [stats, setStats] = useState(null);
  const [searchHistory, setSearchHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchStats = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true);
      const response = await dashboardAPI.getStats();
      setStats(response.data.data);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchSearchHistory = async () => {
    try {
      const response = await fetch('/api/deals/history?limit=10');
      const data = await response.json();
      if (data.success) {
        setSearchHistory(data.data);
      }
    } catch (error) {
      console.error('Error fetching search history:', error);
    }
  };

  useEffect(() => {
    fetchStats();
    fetchSearchHistory();

    // Auto-refresh every 30 seconds
    const interval = setInterval(() => fetchStats(false), 30000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchStats(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Monitor your Swiggy deals cron jobs and track performance
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="btn-secondary flex items-center space-x-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Cron Jobs"
          value={stats?.totalCronJobs || 0}
          icon={Clock}
          color="blue"
          trend={stats?.totalCronJobs > 0 ? 'up' : 'neutral'}
        />
        <StatCard
          title="Active Jobs"
          value={stats?.activeCronJobs || 0}
          icon={Activity}
          color="green"
          trend={stats?.activeCronJobs > 0 ? 'up' : 'neutral'}
        />
        <StatCard
          title="Total Executions"
          value={stats?.totalExecutions || 0}
          icon={TrendingUp}
          color="purple"
          trend="up"
        />
        <StatCard
          title="Items Found"
          value={stats?.totalItemsFound || 0}
          icon={ShoppingCart}
          color="orange"
          trend="up"
        />
      </div>

      {/* Success Rate */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Success Rate</h3>
            <div className="flex items-center space-x-2">
              {stats?.averageSuccessRate >= 80 ? (
                <CheckCircle className="h-5 w-5 text-success-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-warning-500" />
              )}
              <span className="text-2xl font-bold text-gray-900">
                {stats?.averageSuccessRate || 0}%
              </span>
            </div>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${
                  stats?.averageSuccessRate >= 80 
                    ? 'bg-success-500' 
                    : stats?.averageSuccessRate >= 60 
                    ? 'bg-warning-500' 
                    : 'bg-danger-500'
                }`}
                style={{ width: `${stats?.averageSuccessRate || 0}%` }}
              />
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Average success rate across all cron jobs
            </p>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="lg:col-span-2 card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
            <Link to="/cron-jobs" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              View all
            </Link>
          </div>
          
          {stats?.recentActivity?.length > 0 ? (
            <div className="space-y-3">
              {stats.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {Array.isArray(activity.codes) ? activity.codes.join(', ') : activity.codes}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatRelativeTime(activity.lastRun)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.itemsFound} items
                    </p>
                    <Link 
                      to={`/cron-jobs/${activity.jobId}`}
                      className="text-xs text-primary-600 hover:text-primary-700"
                    >
                      View details
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Activity className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
              <p className="mt-1 text-sm text-gray-500">
                Start some cron jobs to see activity here.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            to="/cron-jobs"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200"
          >
            <Clock className="h-8 w-8 text-primary-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">View Cron Jobs</p>
              <p className="text-sm text-gray-500">Monitor all active jobs</p>
            </div>
          </Link>
          
          <Link
            to="/products"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200"
          >
            <ShoppingCart className="h-8 w-8 text-primary-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Browse Products</p>
              <p className="text-sm text-gray-500">View found deals</p>
            </div>
          </Link>
          
          <Link
            to="/find-deals"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200"
          >
            <TrendingUp className="h-8 w-8 text-primary-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Find Deals</p>
              <p className="text-sm text-gray-500">Search by codes</p>
            </div>
          </Link>

          <Link
            to="/codes"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200"
          >
            <Code className="h-8 w-8 text-primary-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Mapping Codes</p>
              <p className="text-sm text-gray-500">Browse all codes</p>
            </div>
          </Link>

          <Link
            to="/history"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200"
          >
            <History className="h-8 w-8 text-primary-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Deal History</p>
              <p className="text-sm text-gray-500">View past searches</p>
            </div>
          </Link>
        </div>
      </div>

      {/* Search History */}
      {searchHistory.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Deal Searches</h3>
          <div className="space-y-3">
            {searchHistory.slice(0, 5).map((search, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">
                      {search.codes.join(', ')}
                    </span>
                    <span className="text-xs text-gray-500">
                      {search.threshold}%+ discount
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                    <span>{search.uniqueItems} deals found</span>
                    <span>{search.duration}s</span>
                    <span>{new Date(search.timestamp).toLocaleString()}</span>
                    <span className="capitalize">{search.source}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-primary-600">
                    {search.uniqueItems} deals
                  </div>
                </div>
              </div>
            ))}
          </div>
          {searchHistory.length > 5 && (
            <div className="mt-3 text-center">
              <Link
                to="/history"
                className="text-sm text-primary-600 hover:text-primary-700"
              >
                View all {searchHistory.length} searches
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Dashboard;
