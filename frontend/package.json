{"name": "swiggy-deals-dashboard", "version": "1.0.0", "description": "React dashboard for <PERSON><PERSON><PERSON> deals cron job monitoring", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.3.4", "recharts": "^2.5.0", "lucide-react": "^0.263.1", "date-fns": "^2.29.3", "react-hot-toast": "^2.4.1", "tailwindcss": "^3.2.7", "autoprefixer": "^10.4.14", "postcss": "^8.4.21"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}