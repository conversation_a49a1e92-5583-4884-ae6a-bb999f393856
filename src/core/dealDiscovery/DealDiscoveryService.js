/**
 * Deal Discovery Service
 * 
 * Core business logic for discovering deals across different APIs
 * Single responsibility: Orchestrate deal discovery operations
 */

class DealDiscoveryService {
  constructor(apiService, filterService, validationService) {
    this.apiService = apiService;
    this.filterService = filterService;
    this.validationService = validationService;
  }

  /**
   * Discover deals based on configuration
   */
  async discoverDeals(config) {
    // Validate configuration
    const validationResult = this.validationService.validateDiscoveryConfig(config);
    if (!validationResult.isValid) {
      throw new Error(`Invalid configuration: ${validationResult.errors.join(', ')}`);
    }

    const {
      codes,
      apiType = 'existing',
      priceCalculation = 'offer',
      discountThreshold = 60,
      hideRepeatedItems = false,
      useCache = false
    } = config;

    const results = {
      items: [],
      metadata: {
        totalFound: 0,
        newItems: 0,
        skippedItems: 0,
        filteredItems: 0,
        executionTime: 0,
        apiType,
        priceCalculation
      }
    };

    const startTime = Date.now();

    try {
      // Process each code
      for (const code of codes) {
        const codeResults = await this.processCode({
          code,
          apiType,
          priceCalculation,
          discountThreshold,
          useCache
        });

        results.items.push(...codeResults.items);
        results.metadata.totalFound += codeResults.metadata.totalFound;
      }

      // Apply filters
      const filteredResults = await this.filterService.applyFilters(results.items, {
        hideRepeatedItems,
        discountThreshold,
        jobId: config.jobId,
        chatId: config.chatId
      });

      results.items = filteredResults.items;
      results.metadata.newItems = filteredResults.newItems;
      results.metadata.skippedItems = filteredResults.skippedItems;
      results.metadata.filteredItems = filteredResults.filteredItems;

      results.metadata.executionTime = Date.now() - startTime;

      return results;
    } catch (error) {
      results.metadata.executionTime = Date.now() - startTime;
      results.metadata.error = error.message;
      throw error;
    }
  }

  /**
   * Process a single code
   */
  async processCode(config) {
    const { code, apiType, priceCalculation, discountThreshold, useCache } = config;

    try {
      const items = await this.apiService.fetchItems({
        code,
        apiType,
        priceCalculation,
        discountThreshold,
        useCache
      });

      return {
        items,
        metadata: {
          totalFound: items.length,
          code,
          apiType,
          priceCalculation
        }
      };
    } catch (error) {
      console.error(`Error processing code ${code}:`, error.message);
      return {
        items: [],
        metadata: {
          totalFound: 0,
          code,
          error: error.message
        }
      };
    }
  }

  /**
   * Get discovery statistics
   */
  async getDiscoveryStats(timeRange = '24h') {
    // This would integrate with analytics service
    return {
      totalDiscoveries: 0,
      successRate: 0,
      averageExecutionTime: 0,
      topCodes: [],
      apiUsage: {
        existing: 0,
        new: 0
      }
    };
  }
}

module.exports = DealDiscoveryService;
