/**
 * Filter Service
 * 
 * Single responsibility: Apply various filters to deal results
 */

class FilterService {
  constructor(displayedItemsRepository, notificationRepository, keywordRepository) {
    this.displayedItemsRepository = displayedItemsRepository;
    this.notificationRepository = notificationRepository;
    this.keywordRepository = keywordRepository;
  }

  /**
   * Apply all configured filters to items
   */
  async applyFilters(items, config) {
    const {
      hideRepeatedItems = false,
      discountThreshold = 60,
      jobId,
      chatId,
      keywordFiltering = true,
      duplicateFiltering = true
    } = config;

    let filteredItems = [...items];
    const stats = {
      originalCount: items.length,
      newItems: 0,
      skippedItems: 0,
      filteredItems: 0,
      duplicateItems: 0,
      keywordFilteredItems: 0,
      repeatedItems: 0
    };

    console.log(`🔍 Applying filters to ${items.length} items`);

    // 1. Remove duplicates based on product ID
    if (duplicateFiltering) {
      const beforeDuplicates = filteredItems.length;
      filteredItems = this.removeDuplicates(filteredItems);
      stats.duplicateItems = beforeDuplicates - filteredItems.length;
      
      if (stats.duplicateItems > 0) {
        console.log(`🔄 Removed ${stats.duplicateItems} duplicate items`);
      }
    }

    // 2. Apply keyword-based filtering
    if (keywordFiltering && chatId) {
      const beforeKeywords = filteredItems.length;
      filteredItems = await this.applyKeywordFiltering(filteredItems, chatId);
      stats.keywordFilteredItems = beforeKeywords - filteredItems.length;
      
      if (stats.keywordFilteredItems > 0) {
        console.log(`🔤 Filtered ${stats.keywordFilteredItems} items by keywords`);
      }
    }

    // 3. Filter repeated items if enabled
    if (hideRepeatedItems && (jobId || chatId)) {
      const beforeRepeated = filteredItems.length;
      filteredItems = await this.filterRepeatedItems(filteredItems, jobId, chatId);
      stats.repeatedItems = beforeRepeated - filteredItems.length;
      
      if (stats.repeatedItems > 0) {
        console.log(`🔄 Filtered ${stats.repeatedItems} repeated items`);
      }
    }

    // 4. Filter already notified items
    if (chatId) {
      const beforeNotified = filteredItems.length;
      filteredItems = await this.filterNotifiedItems(filteredItems, chatId, discountThreshold);
      stats.skippedItems = beforeNotified - filteredItems.length;
      
      if (stats.skippedItems > 0) {
        console.log(`📢 Filtered ${stats.skippedItems} already notified items`);
      }
    }

    stats.newItems = filteredItems.length;
    stats.filteredItems = stats.originalCount - stats.newItems;

    console.log(`✅ Filtering complete: ${stats.newItems}/${stats.originalCount} items remaining`);

    return {
      items: filteredItems,
      ...stats
    };
  }

  /**
   * Remove duplicate items based on product ID
   */
  removeDuplicates(items) {
    const seen = new Map();
    const duplicates = [];

    for (const item of items) {
      const productId = item.productId || item.id;
      
      if (!productId) {
        continue; // Keep items without product ID
      }

      const existing = seen.get(productId);
      
      if (!existing) {
        seen.set(productId, item);
      } else {
        // Keep the one with higher discount percentage
        if (item.discountPercentage > existing.discountPercentage) {
          console.log(`🔄 Replacing duplicate: "${existing.name}" (${existing.discountPercentage}%) with "${item.name}" (${item.discountPercentage}%)`);
          seen.set(productId, item);
        } else {
          console.log(`🔄 Skipping duplicate: "${item.name}" (${item.discountPercentage}%) - keeping "${existing.name}" (${existing.discountPercentage}%)`);
        }
        duplicates.push(item);
      }
    }

    return Array.from(seen.values()).sort((a, b) => b.discountPercentage - a.discountPercentage);
  }

  /**
   * Apply keyword-based filtering
   */
  async applyKeywordFiltering(items, chatId) {
    if (!this.keywordRepository) {
      return items;
    }

    const filteredItems = [];

    for (const item of items) {
      try {
        const meetsKeywordThresholds = await this.keywordRepository.checkKeywordThresholds(chatId, item);
        if (meetsKeywordThresholds) {
          filteredItems.push(item);
        } else {
          console.log(`🔤 Keyword filtered: ${item.name}`);
        }
      } catch (error) {
        console.error(`❌ Error checking keywords for ${item.name}:`, error.message);
        // Include item if keyword check fails
        filteredItems.push(item);
      }
    }

    return filteredItems;
  }

  /**
   * Filter repeated items
   */
  async filterRepeatedItems(items, jobId, chatId) {
    if (!this.displayedItemsRepository) {
      return items;
    }

    const filteredItems = [];

    for (const item of items) {
      try {
        const displayCheck = await this.displayedItemsRepository.checkDisplayedItem(jobId, chatId, item);
        
        if (displayCheck.wasDisplayed && !displayCheck.pricingChanged) {
          console.log(`🔄 Skipping repeated item: ${item.name} (last shown: ${displayCheck.lastDisplayed})`);
          continue;
        }
        
        if (displayCheck.wasDisplayed && displayCheck.pricingChanged) {
          console.log(`💰 Showing item with price change: ${item.name} (displayed ${displayCheck.displayCount} times)`);
        }
        
        filteredItems.push(item);
      } catch (error) {
        console.error(`❌ Error checking repeated item ${item.name}:`, error.message);
        // Include item if check fails
        filteredItems.push(item);
      }
    }

    return filteredItems;
  }

  /**
   * Filter already notified items
   */
  async filterNotifiedItems(items, chatId, discountThreshold) {
    if (!this.notificationRepository) {
      return items;
    }

    const filteredItems = [];

    for (const item of items) {
      try {
        const shouldNotify = await this.notificationRepository.shouldNotifyDeal(chatId, item, discountThreshold);
        if (shouldNotify) {
          filteredItems.push(item);
        } else {
          console.log(`📢 Already notified: ${item.name}`);
        }
      } catch (error) {
        console.error(`❌ Error checking notification status for ${item.name}:`, error.message);
        // Include item if check fails
        filteredItems.push(item);
      }
    }

    return filteredItems;
  }

  /**
   * Get filter statistics
   */
  getFilterStats() {
    return {
      totalFiltered: 0,
      duplicatesRemoved: 0,
      keywordFiltered: 0,
      repeatedFiltered: 0,
      notificationFiltered: 0
    };
  }
}

module.exports = FilterService;
