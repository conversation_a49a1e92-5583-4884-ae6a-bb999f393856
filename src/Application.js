/**
 * Application Bootstrap
 * 
 * Single responsibility: Initialize and coordinate all application components
 */

const { getContainer } = require('./infrastructure/container/Container');

class Application {
  constructor() {
    this.container = getContainer();
    this.isInitialized = false;
    this.isShuttingDown = false;
  }

  /**
   * Initialize the application
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('⚠️ Application already initialized');
      return;
    }

    console.log('🚀 Starting application initialization...');

    try {
      // Initialize container and services
      await this.container.initialize();

      // Validate configuration
      const config = this.container.resolve('config');
      const validation = config.validate();
      
      if (!validation.isValid) {
        throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`);
      }

      // Start core services
      await this.startCoreServices();

      // Setup graceful shutdown
      this.setupGracefulShutdown();

      this.isInitialized = true;
      console.log('✅ Application initialized successfully');

    } catch (error) {
      console.error('❌ Application initialization failed:', error.message);
      throw error;
    }
  }

  /**
   * Start core services
   */
  async startCoreServices() {
    console.log('🔧 Starting core services...');

    // Start cleanup service
    const cleanupService = this.container.resolve('cleanupService');
    await cleanupService.start();

    // Start cron job service
    const cronJobService = this.container.resolve('cronJobService');
    await cronJobService.start();

    // Start API server
    await this.startApiServer();

    // Start Telegram bot
    await this.startTelegramBot();

    console.log('✅ Core services started');
  }

  /**
   * Start API server
   */
  async startApiServer() {
    const config = this.container.resolve('config');
    const port = config.get('server.port', 3001);
    const host = config.get('server.host', 'localhost');

    // Import and start the API server with dependency injection
    const { createApiServer } = require('./infrastructure/http/ApiServer');
    const server = createApiServer(this.container);

    return new Promise((resolve, reject) => {
      server.listen(port, host, (error) => {
        if (error) {
          reject(error);
        } else {
          console.log(`🌐 API Server running on http://${host}:${port}`);
          resolve(server);
        }
      });
    });
  }

  /**
   * Start Telegram bot
   */
  async startTelegramBot() {
    const telegramService = this.container.resolve('telegramService');
    await telegramService.start();
    console.log('🤖 Telegram bot started');
  }

  /**
   * Setup graceful shutdown
   */
  setupGracefulShutdown() {
    const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

    signals.forEach(signal => {
      process.on(signal, async () => {
        if (this.isShuttingDown) {
          console.log('⚠️ Force shutdown');
          process.exit(1);
        }

        console.log(`📡 Received ${signal}, starting graceful shutdown...`);
        await this.shutdown();
      });
    });

    process.on('uncaughtException', async (error) => {
      console.error('💥 Uncaught Exception:', error);
      await this.shutdown();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason, promise) => {
      console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
      await this.shutdown();
      process.exit(1);
    });
  }

  /**
   * Shutdown the application
   */
  async shutdown() {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    console.log('🛑 Shutting down application...');

    try {
      // Shutdown all services
      await this.container.shutdown();
      
      console.log('✅ Application shut down successfully');
    } catch (error) {
      console.error('❌ Error during shutdown:', error.message);
    }

    process.exit(0);
  }

  /**
   * Get service from container
   */
  getService(name) {
    return this.container.resolve(name);
  }

  /**
   * Check if application is healthy
   */
  async healthCheck() {
    if (!this.isInitialized) {
      return { status: 'unhealthy', reason: 'Not initialized' };
    }

    try {
      // Check database connection
      const database = this.container.resolve('database');
      await database.query('SELECT 1');

      // Check API health
      const dealApiService = this.container.resolve('dealApiService');
      const apiHealth = await dealApiService.getApiHealth();

      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          database: 'healthy',
          api: apiHealth
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        reason: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get application statistics
   */
  async getStats() {
    try {
      const cronJobService = this.container.resolve('cronJobService');
      const dealDiscoveryService = this.container.resolve('dealDiscoveryService');

      const [cronStats, discoveryStats] = await Promise.all([
        cronJobService.getStats(),
        dealDiscoveryService.getDiscoveryStats()
      ]);

      return {
        cronJobs: cronStats,
        dealDiscovery: discoveryStats,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error getting application stats:', error.message);
      return {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
let applicationInstance = null;

module.exports = {
  Application,
  
  getInstance() {
    if (!applicationInstance) {
      applicationInstance = new Application();
    }
    return applicationInstance;
  }
};
