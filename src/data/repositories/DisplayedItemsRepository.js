/**
 * Displayed Items Repository
 * 
 * Single responsibility: <PERSON><PERSON> displayed items database operations
 */

class DisplayedItemsRepository {
  constructor(database) {
    this.db = database;
  }

  /**
   * Check if item was already displayed and if pricing changed
   */
  async checkDisplayedItem(jobId, chatId, item) {
    try {
      const query = `
        SELECT * FROM displayed_items 
        WHERE (job_id = $1 OR chat_id = $2) AND product_id = $3
        ORDER BY last_displayed_at DESC
        LIMIT 1
      `;
      
      const result = await this.db.query(query, [jobId, chatId, item.id || item.productId]);
      
      if (result.rows.length === 0) {
        return { wasDisplayed: false, pricingChanged: false };
      }

      const displayedItem = result.rows[0];
      
      // Check if pricing changed significantly (more than 1% difference)
      const currentStorePrice = parseFloat(item.storePrice) || 0;
      const currentOfferPrice = parseFloat(item.offerPrice) || 0;
      const currentMaxSaverPrice = parseFloat(item.maxSaverPrice || item.offerPrice) || 0;
      
      const lastStorePrice = parseFloat(displayedItem.last_store_price) || 0;
      const lastOfferPrice = parseFloat(displayedItem.last_offer_price) || 0;
      const lastMaxSaverPrice = parseFloat(displayedItem.last_max_saver_price) || 0;
      
      const storePriceChanged = Math.abs(currentStorePrice - lastStorePrice) / Math.max(lastStorePrice, 1) > 0.01;
      const offerPriceChanged = Math.abs(currentOfferPrice - lastOfferPrice) / Math.max(lastOfferPrice, 1) > 0.01;
      const maxSaverPriceChanged = Math.abs(currentMaxSaverPrice - lastMaxSaverPrice) / Math.max(lastMaxSaverPrice, 1) > 0.01;
      
      const pricingChanged = storePriceChanged || offerPriceChanged || maxSaverPriceChanged;
      
      return { 
        wasDisplayed: true, 
        pricingChanged,
        lastDisplayed: displayedItem.last_displayed_at,
        displayCount: displayedItem.display_count
      };
      
    } catch (error) {
      console.error('❌ Error checking displayed item:', error.message);
      return { wasDisplayed: false, pricingChanged: false };
    }
  }

  /**
   * Mark item as displayed
   */
  async markItemAsDisplayed(jobId, chatId, item) {
    try {
      const query = `
        INSERT INTO displayed_items (
          job_id, chat_id, product_id, item_name,
          last_store_price, last_offer_price, last_max_saver_price,
          last_discount_percentage, last_displayed_at, display_count
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), 1)
        ON CONFLICT (job_id, product_id) DO UPDATE SET
          last_store_price = EXCLUDED.last_store_price,
          last_offer_price = EXCLUDED.last_offer_price,
          last_max_saver_price = EXCLUDED.last_max_saver_price,
          last_discount_percentage = EXCLUDED.last_discount_percentage,
          last_displayed_at = NOW(),
          display_count = displayed_items.display_count + 1,
          updated_at = NOW()
      `;

      const values = [
        jobId,
        chatId,
        item.id || item.productId,
        item.name || item.itemName,
        parseFloat(item.storePrice) || 0,
        parseFloat(item.offerPrice) || 0,
        parseFloat(item.maxSaverPrice || item.offerPrice) || 0,
        parseFloat(item.discountPercentage) || 0
      ];

      await this.db.query(query, values);
      return true;
    } catch (error) {
      console.error('❌ Error marking item as displayed:', error.message);
      return false;
    }
  }

  /**
   * Get displayed items statistics
   */
  async getStats(jobId, chatId) {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_displayed,
          COUNT(DISTINCT product_id) as unique_items,
          SUM(display_count) as total_displays
        FROM displayed_items 
        WHERE job_id = $1 OR chat_id = $2
      `;
      
      const result = await this.db.query(query, [jobId, chatId]);
      
      return {
        totalDisplayed: parseInt(result.rows[0].total_displayed) || 0,
        uniqueItems: parseInt(result.rows[0].unique_items) || 0,
        totalDisplays: parseInt(result.rows[0].total_displays) || 0
      };
    } catch (error) {
      console.error('❌ Error getting displayed items stats:', error.message);
      return { totalDisplayed: 0, uniqueItems: 0 };
    }
  }

  /**
   * Clean old displayed items
   */
  async cleanOld(daysOld = 30) {
    try {
      const query = `
        DELETE FROM displayed_items 
        WHERE last_displayed_at < NOW() - INTERVAL '${daysOld} days'
      `;
      
      const result = await this.db.query(query);
      return result.rowCount || 0;
    } catch (error) {
      console.error('❌ Error cleaning old displayed items:', error.message);
      return 0;
    }
  }

  /**
   * Get displayed items for a job
   */
  async getByJob(jobId, limit = 100, offset = 0) {
    try {
      const query = `
        SELECT * FROM displayed_items 
        WHERE job_id = $1 
        ORDER BY last_displayed_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      const result = await this.db.query(query, [jobId, limit, offset]);
      return result.rows;
    } catch (error) {
      console.error('❌ Error getting displayed items by job:', error.message);
      return [];
    }
  }

  /**
   * Get displayed items for a chat
   */
  async getByChat(chatId, limit = 100, offset = 0) {
    try {
      const query = `
        SELECT * FROM displayed_items 
        WHERE chat_id = $1 
        ORDER BY last_displayed_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      const result = await this.db.query(query, [chatId, limit, offset]);
      return result.rows;
    } catch (error) {
      console.error('❌ Error getting displayed items by chat:', error.message);
      return [];
    }
  }
}

module.exports = DisplayedItemsRepository;
