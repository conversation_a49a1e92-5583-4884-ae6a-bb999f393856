/**
 * Cron Job Repository
 * 
 * Single responsibility: Handle all cron job database operations
 */

class CronJobRepository {
  constructor(database) {
    this.db = database;
  }

  /**
   * Create a new cron job
   */
  async create(jobData) {
    const {
      jobId,
      chatId,
      codes,
      scheduleType,
      intervalMinutes,
      scheduleTime,
      threshold,
      apiType = 'existing',
      priceCalculation = 'offer',
      hideRepeatedItems = false
    } = jobData;

    const query = `
      INSERT INTO cron_jobs (
        job_id, chat_id, codes, is_multiple_codes, schedule_type,
        interval_minutes, schedule_time, threshold, start_time,
        api_type, price_calculation, hide_repeated_items
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `;

    const values = [
      jobId,
      chatId,
      Array.isArray(codes) ? codes.join(',') : codes,
      Array.isArray(codes) && codes.length > 1,
      scheduleType,
      intervalMinutes,
      scheduleTime,
      threshold,
      Date.now(),
      apiType,
      priceCalculation,
      hideRepeatedItems
    ];

    const result = await this.db.query(query, values);
    return this.mapDbRowToJob(result.rows[0]);
  }

  /**
   * Find job by ID
   */
  async findById(jobId) {
    const query = 'SELECT * FROM cron_jobs WHERE job_id = $1';
    const result = await this.db.query(query, [jobId]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return this.mapDbRowToJob(result.rows[0]);
  }

  /**
   * Find jobs by chat ID
   */
  async findByChatId(chatId) {
    const query = 'SELECT * FROM cron_jobs WHERE chat_id = $1 ORDER BY created_at DESC';
    const result = await this.db.query(query, [chatId]);
    
    return result.rows.map(row => this.mapDbRowToJob(row));
  }

  /**
   * Find all active jobs
   */
  async findActive() {
    const query = `
      SELECT * FROM cron_jobs 
      WHERE start_time IS NOT NULL 
      ORDER BY created_at DESC
    `;
    const result = await this.db.query(query);
    
    return result.rows.map(row => this.mapDbRowToJob(row));
  }

  /**
   * Update job
   */
  async update(jobId, updates) {
    const allowedFields = [
      'threshold', 'interval_minutes', 'schedule_time', 'schedule_type',
      'api_type', 'price_calculation', 'hide_repeated_items',
      'total_items_found', 'execution_count', 'failed_executions', 'last_run'
    ];

    const setClause = [];
    const values = [];
    let paramIndex = 1;

    for (const [field, value] of Object.entries(updates)) {
      if (allowedFields.includes(field)) {
        setClause.push(`${field} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    }

    if (setClause.length === 0) {
      throw new Error('No valid fields to update');
    }

    setClause.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(jobId);

    const query = `
      UPDATE cron_jobs 
      SET ${setClause.join(', ')}
      WHERE job_id = $${paramIndex}
      RETURNING *
    `;

    const result = await this.db.query(query, values);
    
    if (result.rows.length === 0) {
      throw new Error(`Job ${jobId} not found`);
    }
    
    return this.mapDbRowToJob(result.rows[0]);
  }

  /**
   * Delete job
   */
  async delete(jobId) {
    const query = 'DELETE FROM cron_jobs WHERE job_id = $1 RETURNING *';
    const result = await this.db.query(query, [jobId]);
    
    if (result.rows.length === 0) {
      throw new Error(`Job ${jobId} not found`);
    }
    
    return this.mapDbRowToJob(result.rows[0]);
  }

  /**
   * Update job statistics
   */
  async updateStats(jobId, stats) {
    const {
      totalItemsFound,
      executionCount,
      failedExecutions,
      lastRun
    } = stats;

    const query = `
      UPDATE cron_jobs 
      SET 
        total_items_found = COALESCE($2, total_items_found),
        execution_count = COALESCE($3, execution_count),
        failed_executions = COALESCE($4, failed_executions),
        last_run = COALESCE($5, last_run),
        updated_at = CURRENT_TIMESTAMP
      WHERE job_id = $1
      RETURNING *
    `;

    const values = [jobId, totalItemsFound, executionCount, failedExecutions, lastRun];
    const result = await this.db.query(query, values);
    
    if (result.rows.length === 0) {
      throw new Error(`Job ${jobId} not found`);
    }
    
    return this.mapDbRowToJob(result.rows[0]);
  }

  /**
   * Get job statistics
   */
  async getStats(timeRange = '24h') {
    const query = `
      SELECT 
        COUNT(*) as total_jobs,
        COUNT(CASE WHEN last_run > $1 THEN 1 END) as active_jobs,
        AVG(execution_count) as avg_executions,
        SUM(total_items_found) as total_items_found
      FROM cron_jobs
    `;

    const cutoffTime = this.getTimeRangeCutoff(timeRange);
    const result = await this.db.query(query, [cutoffTime]);
    
    return {
      totalJobs: parseInt(result.rows[0].total_jobs) || 0,
      activeJobs: parseInt(result.rows[0].active_jobs) || 0,
      avgExecutions: parseFloat(result.rows[0].avg_executions) || 0,
      totalItemsFound: parseInt(result.rows[0].total_items_found) || 0
    };
  }

  /**
   * Map database row to job object
   */
  mapDbRowToJob(row) {
    if (!row) return null;

    return {
      jobId: row.job_id,
      chatId: row.chat_id,
      codes: row.codes ? row.codes.split(',') : [],
      isMultipleCodes: row.is_multiple_codes || false,
      scheduleType: row.schedule_type,
      intervalMinutes: row.interval_minutes,
      scheduleTime: row.schedule_time,
      threshold: row.threshold,
      apiType: row.api_type || 'existing',
      priceCalculation: row.price_calculation || 'offer',
      hideRepeatedItems: row.hide_repeated_items || false,
      startTime: row.start_time,
      totalItemsFound: row.total_items_found || 0,
      executionCount: row.execution_count || 0,
      failedExecutions: row.failed_executions || 0,
      lastRun: row.last_run,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  /**
   * Get time range cutoff
   */
  getTimeRangeCutoff(timeRange) {
    const now = Date.now();
    const ranges = {
      '1h': 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000
    };

    const duration = ranges[timeRange] || ranges['24h'];
    return now - duration;
  }
}

module.exports = CronJobRepository;
