const toBoolean = (value) => {
  if (value === true) return true;
  if (value === false) return false;
  if (typeof value === 'number') return value > 0;
  if (typeof value === 'string') {
    const v = value.trim().toLowerCase();
    if (v === 'true' || v === 'yes') return true;
    if (v === 'false' || v === 'no') return false;
    const n = Number(v);
    if (!Number.isNaN(n)) return n > 0;
  }
  return undefined;
};

const get = (obj, path) => {
  try {
    return path.reduce((acc, key) => (acc == null ? undefined : acc[key]), obj);
  } catch (_) {
    return undefined;
  }
};

const candidatePaths = [
  ['in_stock'],
  ['inStock'],
  ['is_avail'],
  ['isAvail'],
  ['avail'],
  ['inventory', 'in_stock'],
  ['inventory', 'inStock'],
  ['slotInfo', 'isAvail'],
  ['slot_info', 'is_avail'],
  // Quantity-derived hints (fallback)
  ['cartAllowedQuantity', 'allowedQuantity'],
  ['cart_allowed_quantity', 'total']
];

function deriveBooleanFromObject(obj) {
  if (!obj || typeof obj !== 'object') return undefined;

  let anyTrue = false;
  let anyFalse = false;

  for (const path of candidatePaths) {
    const raw = get(obj, path);
    if (raw === undefined) continue;

    const asBool = toBoolean(raw);
    if (asBool === true) anyTrue = true;
    if (asBool === false) anyFalse = true;
  }

  if (anyTrue) return true;
  if (anyFalse) return false;
  return undefined;
}

function deriveInStockFromVariation(variation) {
  // Prefer explicit boolean from variation-level fields
  const variationLevel = deriveBooleanFromObject(variation);
  if (variationLevel !== undefined) return variationLevel;

  // Try inventory.remaining or inventory.total as a last resort
  const remaining = get(variation, ['inventory', 'remaining']);
  const total = get(variation, ['inventory', 'total']);
  const remainingBool = toBoolean(remaining);
  const totalBool = toBoolean(total);

  if (remainingBool !== undefined) return remainingBool;
  if (totalBool !== undefined) return totalBool;

  return false; // default safe fallback
}

function deriveInStockFromProduct(product) {
  if (!product || typeof product !== 'object') return false;

  // If there are variations, consider product in stock if any variation is in stock
  if (Array.isArray(product.variations) && product.variations.length > 0) {
    let sawExplicitFalse = false;
    for (const variation of product.variations) {
      const v = deriveInStockFromVariation(variation);
      if (v === true) return true;
      if (v === false) sawExplicitFalse = true;
    }
    // If we saw explicit false but no true, treat as out of stock
    if (sawExplicitFalse) return false;
  }

  // Fall back to product-level fields
  const productLevel = deriveBooleanFromObject(product);
  if (productLevel !== undefined) return productLevel;

  return false; // safe default
}

module.exports = {
  deriveInStockFromProduct,
  deriveInStockFromVariation
};