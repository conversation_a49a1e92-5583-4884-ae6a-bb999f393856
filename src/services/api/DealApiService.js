/**
 * Deal API Service
 * 
 * Single responsibility: Handle all external API communications for deal discovery
 */

const ExistingApiAdapter = require('./adapters/ExistingApiAdapter');
const NewApiAdapter = require('./adapters/NewApiAdapter');

class DealApiService {
  constructor(cacheService, mappingService) {
    this.cacheService = cacheService;
    this.mappingService = mappingService;
    this.existingApi = new ExistingApiAdapter();
    this.newApi = new NewApiAdapter();
  }

  /**
   * Fetch items using specified API configuration
   */
  async fetchItems(config) {
    const {
      code,
      apiType = 'existing',
      priceCalculation = 'offer',
      discountThreshold = 60,
      useCache = false,
      retryAttempts = 3
    } = config;

    // Get mapping information
    const mapping = this.mappingService.getMapping(code);
    if (!mapping) {
      throw new Error(`Unknown code: ${code}`);
    }

    // Check cache first
    if (useCache) {
      const cachedItems = await this.getCachedItems(config);
      if (cachedItems) {
        console.log(`📦 Using cached results for ${code}`);
        return cachedItems;
      }
    }

    // Select appropriate API adapter
    const adapter = this.getApiAdapter(apiType);
    
    try {
      const items = await this.fetchWithRetry(adapter, {
        ...config,
        mapping
      }, retryAttempts);

      // Cache results if enabled
      if (useCache && items.length > 0) {
        await this.cacheItems(config, items);
      }

      return items;
    } catch (error) {
      console.error(`❌ API fetch failed for ${code}:`, error.message);
      
      // Fallback to alternative API if available
      if (apiType === 'new') {
        console.log(`🔄 Falling back to existing API for ${code}`);
        return await this.fetchItems({
          ...config,
          apiType: 'existing'
        });
      }
      
      throw error;
    }
  }

  /**
   * Get appropriate API adapter
   */
  getApiAdapter(apiType) {
    switch (apiType) {
      case 'new':
        return this.newApi;
      case 'existing':
      default:
        return this.existingApi;
    }
  }

  /**
   * Fetch with retry logic
   */
  async fetchWithRetry(adapter, config, maxAttempts) {
    const INITIAL_RETRIES = 3;
    const EXTENDED_RETRIES = 2;
    let lastError;
    
    // Initial attempts with shorter delays
    for (let attempt = 1; attempt <= INITIAL_RETRIES; attempt++) {
      try {
        console.log(`🔄 Attempt ${attempt}/${INITIAL_RETRIES} for ${config.code}`);
        
        const items = await adapter.fetchItems(config);
        
        if (items && items.length >= 0) {
          console.log(`✅ Success on attempt ${attempt} for ${config.code}: ${items.length} items`);
          return items;
        }
        
        throw new Error('No items returned');
      } catch (error) {
        lastError = error;
        console.log(`❌ Attempt ${attempt}/${INITIAL_RETRIES} failed for ${config.code}:`, error.message);
        
        if (attempt < INITIAL_RETRIES) {
          const delay = 10000; // 10 seconds for initial attempts
          console.log(`⏳ Waiting ${delay}ms before retry...`);
          await this.sleep(delay);
        }
      }
    }
    
    // Extended attempts with longer delays
    for (let attempt = 1; attempt <= EXTENDED_RETRIES; attempt++) {
      try {
        console.log(`⏳ Extended retry - waiting 1 minute before attempt ${attempt}/${EXTENDED_RETRIES}...`);
        await this.sleep(60000); // 1 minute delay
        
        console.log(`🔄 Extended attempt ${attempt}/${EXTENDED_RETRIES} for ${config.code}`);
        
        const items = await adapter.fetchItems(config);
        
        if (items && items.length >= 0) {
          console.log(`✅ Success on extended attempt ${attempt} for ${config.code}: ${items.length} items`);
          return items;
        }
        
        throw new Error('No items returned');
      } catch (error) {
        lastError = error;
        console.log(`❌ Extended attempt ${attempt}/${EXTENDED_RETRIES} failed for ${config.code}:`, error.message);
      }
    }
    
    throw lastError;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay(attempt) {
    const baseDelay = 3000; // 3 seconds
    const maxDelay = 60000; // 1 minute
    
    if (attempt <= 3) {
      return baseDelay;
    } else {
      return Math.min(maxDelay, baseDelay * Math.pow(2, attempt - 3));
    }
  }

  /**
   * Get cached items
   */
  async getCachedItems(config) {
    if (!this.cacheService) {
      return null;
    }
    
    const cacheKey = this.generateCacheKey(config);
    return await this.cacheService.get(cacheKey);
  }

  /**
   * Cache items
   */
  async cacheItems(config, items) {
    if (!this.cacheService) {
      return;
    }
    
    const cacheKey = this.generateCacheKey(config);
    const ttl = 300; // 5 minutes
    
    await this.cacheService.set(cacheKey, items, ttl);
  }

  /**
   * Generate cache key
   */
  generateCacheKey(config) {
    const { code, apiType, priceCalculation, discountThreshold } = config;
    return `deals:${code}:${apiType}:${priceCalculation}:${discountThreshold}`;
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get API health status
   */
  async getApiHealth() {
    const health = {
      existing: { status: 'unknown', responseTime: 0 },
      new: { status: 'unknown', responseTime: 0 }
    };

    // Test existing API
    try {
      const start = Date.now();
      await this.existingApi.healthCheck();
      health.existing = {
        status: 'healthy',
        responseTime: Date.now() - start
      };
    } catch (error) {
      health.existing = {
        status: 'unhealthy',
        error: error.message,
        responseTime: 0
      };
    }

    // Test new API
    try {
      const start = Date.now();
      await this.newApi.healthCheck();
      health.new = {
        status: 'healthy',
        responseTime: Date.now() - start
      };
    } catch (error) {
      health.new = {
        status: 'unhealthy',
        error: error.message,
        responseTime: 0
      };
    }

    return health;
  }
}

module.exports = DealApiService;
