/**
 * Existing API Adapter
 * 
 * Single responsibility: Adapt existing discountedItemsUtil to common interface
 */

const discountedItemsUtil = require('../../../discountedItemsUtil');
const { deriveInStockFromProduct } = require('../utils/stockUtils');

class ExistingApiAdapter {
  constructor() {
    this.name = 'existing';
  }

  /**
   * Fetch items using existing API
   */
  async fetchItems(config) {
    const { code, mapping, discountThreshold, useCache } = config;

    try {
      const options = {
        code,
        category: mapping.category,
        filter: mapping.filter,
        discountThreshold,
        useCache
      };

      const items = await discountedItemsUtil.getHighlyDiscountedItems(options);
      
      return this.normalizeItems(items, config);
    } catch (error) {
      console.error(`❌ Existing API error for ${code}:`, error.message);
      throw error;
    }
  }

  /**
   * Normalize items to common format
   */
  normalizeItems(items, config) {
    return items.map(item => ({
      id: item.id,
      productId: item.id,
      name: item.name,
      variation: item.variation || '',
      storePrice: parseFloat(item.storePrice) || 0,
      offerPrice: parseFloat(item.offerPrice) || 0,
      maxSaverPrice: parseFloat(item.offerPrice) || 0, // Same as offer for existing API
      finalPrice: parseFloat(item.offerPrice) || 0,
      discountPercentage: parseFloat(item.discountPercentage) || 0,
      quantity: item.quantity || '',
      unit: item.unit || '',
      url: item.url || '',
      imageUrl: this.extractImageUrl(item),
      inStock: deriveInStockFromProduct(item),
      appliedThreshold: config.discountThreshold,
      priceCalculationMethod: 'offer',
      apiType: 'existing',
      category: item.category || '',
      filter: item.filter || '',
      source: 'existing-api'
    }));
  }

  /**
   * Extract image URL from item data
   */
  extractImageUrl(item) {
    const baseUrl = 'https://instamart-media-assets.swiggy.com/swiggy/image/upload/fl_lossy,f_auto,q_auto,h_600';

    // Try to get image from variations first
    if (item.variations && Array.isArray(item.variations)) {
      for (const variation of item.variations) {
        if (variation.images && Array.isArray(variation.images) && variation.images.length > 0) {
          const imageId = variation.images[0];
          return `${baseUrl}/${imageId}`;
        }
      }
    }

    // Fallback to direct image property
    if (item.image) {
      return `${baseUrl}/${item.image}`;
    }

    // Fallback to images array
    if (item.images && Array.isArray(item.images) && item.images.length > 0) {
      return `${baseUrl}/${item.images[0]}`;
    }

    return null;
  }

  /**
   * Health check for existing API
   */
  async healthCheck() {
    try {
      // Try a simple request with a known code
      const testOptions = {
        code: 'J', // Cleaning Essentials
        discountThreshold: 90, // High threshold to get minimal results
        useCache: false
      };

      await discountedItemsUtil.getHighlyDiscountedItems(testOptions);
      return { status: 'healthy' };
    } catch (error) {
      throw new Error(`Existing API health check failed: ${error.message}`);
    }
  }

  /**
   * Get adapter capabilities
   */
  getCapabilities() {
    return {
      supportsPriceCalculation: false,
      supportsMaxSaver: false,
      supportsCaching: true,
      supportsRetry: true,
      maxRetryAttempts: 3
    };
  }
}

module.exports = ExistingApiAdapter;
