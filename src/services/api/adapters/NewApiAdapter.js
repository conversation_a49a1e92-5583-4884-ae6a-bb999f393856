/**
 * New API Adapter
 * 
 * Single responsibility: Adapt new API with Max Saver support to common interface
 */

const { makeHttp2Request, buildCategoryListingUrl } = require('../../../testNewApiApproach');
const { deriveInStockFromProduct } = require('../utils/stockUtils');

class NewApiAdapter {
  constructor() {
    this.name = 'new';
  }

  /**
   * Fetch items using new API with Max Saver support
   */
  async fetchItems(config) {
    const { 
      code, 
      mapping, 
      discountThreshold, 
      priceCalculation = 'offer',
      limit = 50 
    } = config;

    try {
      const url = buildCategoryListingUrl(mapping.category, mapping.filter);
      console.log(`🌐 New API request: ${url}`);

      // Extended retry logic for new API
      const response = await this.makeRequestWithExtendedRetry(url);
      
      if (!response || !response.data) {
        throw new Error('No data received from new API');
      }

      const items = this.parseItemsFromResponse(
        response.data, 
        priceCalculation, 
        discountThreshold,
        config
      );

      return items.slice(0, limit);
    } catch (error) {
      console.error(`❌ New API error for ${code}:`, error.message);
      throw error;
    }
  }

  /**
   * Make request with extended retry logic (3 + 2 attempts)
   */
  async makeRequestWithExtendedRetry(url) {
    const INITIAL_RETRIES = 3;
    const EXTENDED_RETRIES = 2;
    const INITIAL_DELAY = 10000; // 10 seconds
    const EXTENDED_DELAY = 60000; // 1 minute

    // Initial 3 attempts with 10-second delays
    for (let attempt = 1; attempt <= INITIAL_RETRIES; attempt++) {
      try {
        console.log(`🔄 New API attempt ${attempt}/${INITIAL_RETRIES}`);
        const response = await makeHttp2Request(url);
        
        if (response.ok && response.json) {
          console.log(`✅ New API successful on attempt ${attempt}`);
          return response;
        }
        
        throw new Error(`Request failed with status ${response.status}`);
      } catch (error) {
        console.log(`❌ New API attempt ${attempt}/${INITIAL_RETRIES} failed:`, error.message);
        
        if (attempt < INITIAL_RETRIES) {
          console.log(`⏳ Waiting ${INITIAL_DELAY/1000} seconds before retry...`);
          await this.sleep(INITIAL_DELAY);
        }
      }
    }

    // Extended 2 attempts with 1-minute delays
    for (let attempt = 1; attempt <= EXTENDED_RETRIES; attempt++) {
      try {
        console.log(`⏳ Extended retry - waiting 1 minute before attempt ${attempt}/${EXTENDED_RETRIES}...`);
        await this.sleep(EXTENDED_DELAY);
        
        console.log(`🔄 New API extended attempt ${attempt}/${EXTENDED_RETRIES}`);
        const response = await makeHttp2Request(url);
        
        if (response.ok && response.json) {
          console.log(`✅ New API successful on extended attempt ${attempt}`);
          return response;
        }
        
        throw new Error(`Request failed with status ${response.status}`);
      } catch (error) {
        console.log(`❌ New API extended attempt ${attempt}/${EXTENDED_RETRIES} failed:`, error.message);
      }
    }

    throw new Error(`All ${INITIAL_RETRIES + EXTENDED_RETRIES} attempts failed for new API`);
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Parse items from API response
   */
  parseItemsFromResponse(data, priceCalculation, discountThreshold, config) {
    const items = [];
    
    try {
      const products = data.products || data.items || [];
      
      for (const product of products) {
        try {
          const item = this.parseIndividualItem(product, priceCalculation, discountThreshold, config);
          if (item) {
            items.push(item);
          }
        } catch (error) {
          console.log(`⚠️ Error parsing item:`, error.message);
          continue;
        }
      }
      
    } catch (error) {
      console.error('❌ Error parsing response data:', error.message);
    }
    
    return items.sort((a, b) => b.discountPercentage - a.discountPercentage);
  }

  /**
   * Parse individual item with Max Saver pricing
   */
  parseIndividualItem(product, priceCalculation, discountThreshold, config) {
    const name = product.name || product.product_name || '';
    const id = product.id || product.product_id || '';
    
    // Get pricing information
    const storePrice = parseFloat(product.store_price || product.mrp || 0);
    const offerPrice = parseFloat(product.offer_price || product.price || 0);
    const maxSaverPrice = parseFloat(product.max_saver_price || product.member_price || offerPrice);
    
    if (!storePrice || !offerPrice) {
      return null;
    }
    
    // Calculate discount based on price calculation method
    let finalPrice, discountPercentage;
    
    if (priceCalculation === 'maxsaver' && maxSaverPrice && maxSaverPrice < offerPrice) {
      finalPrice = maxSaverPrice;
      discountPercentage = ((storePrice - maxSaverPrice) / storePrice) * 100;
    } else {
      finalPrice = offerPrice;
      discountPercentage = ((storePrice - offerPrice) / storePrice) * 100;
    }
    
    // Check if discount meets threshold
    if (discountPercentage < discountThreshold) {
      return null;
    }
    
    return {
      id,
      productId: id,
      name,
      variation: product.variation || product.variant || '',
      storePrice,
      offerPrice,
      maxSaverPrice,
      finalPrice,
      discountPercentage: Math.round(discountPercentage * 100) / 100,
      quantity: product.quantity || '',
      unit: product.unit || '',
      url: product.url || product.product_url || '',
      imageUrl: this.extractImageUrl(product),
      inStock: deriveInStockFromProduct(product),
      appliedThreshold: discountThreshold,
      priceCalculationMethod: priceCalculation,
      apiType: 'new',
      category: config.mapping?.category || '',
      filter: config.mapping?.filter || '',
      source: 'new-api'
    };
  }

  /**
   * Extract image URL from product data
   */
  extractImageUrl(product) {
    const baseUrl = 'https://instamart-media-assets.swiggy.com/swiggy/image/upload/fl_lossy,f_auto,q_auto,h_600';

    // Try to get image from variations first (preferred method)
    if (product.variations && Array.isArray(product.variations)) {
      for (const variation of product.variations) {
        if (variation.images && Array.isArray(variation.images) && variation.images.length > 0) {
          const imageId = variation.images[0];
          return `${baseUrl}/${imageId}`;
        }
      }
    }

    // Fallback to direct image property
    if (product.image) {
      return `${baseUrl}/${product.image}`;
    }

    // Fallback to images array
    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
      return `${baseUrl}/${product.images[0]}`;
    }

    // Fallback to image_url property
    if (product.image_url) {
      return `${baseUrl}/${product.image_url}`;
    }

    return null;
  }

  /**
   * Health check for new API
   */
  async healthCheck() {
    try {
      // Try a simple request
      const testUrl = buildCategoryListingUrl('Cleaning Essentials', '');
      const response = await makeHttp2Request(testUrl);
      
      if (!response || !response.data) {
        throw new Error('No response data');
      }
      
      return { status: 'healthy' };
    } catch (error) {
      throw new Error(`New API health check failed: ${error.message}`);
    }
  }

  /**
   * Get adapter capabilities
   */
  getCapabilities() {
    return {
      supportsPriceCalculation: true,
      supportsMaxSaver: true,
      supportsCaching: true,
      supportsRetry: true,
      maxRetryAttempts: 5,
      priceCalculationMethods: ['offer', 'maxsaver']
    };
  }
}

module.exports = NewApiAdapter;