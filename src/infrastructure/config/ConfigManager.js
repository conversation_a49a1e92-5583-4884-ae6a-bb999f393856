/**
 * Configuration Manager
 * 
 * Single responsibility: Centralized configuration management
 */

const path = require('path');
const fs = require('fs');

class ConfigManager {
  constructor() {
    this.config = null;
    this.configPath = path.join(__dirname, '../../../config.json');
    this.load();
  }

  /**
   * Load configuration from file
   */
  load() {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        this.config = JSON.parse(configData);
      } else {
        this.config = this.getDefaultConfig();
        this.save();
      }
      
      // Apply environment variable overrides
      this.applyEnvironmentOverrides();
      
      console.log('✅ Configuration loaded successfully');
    } catch (error) {
      console.error('❌ Error loading configuration:', error.message);
      this.config = this.getDefaultConfig();
    }
  }

  /**
   * Save configuration to file
   */
  save() {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
      console.log('✅ Configuration saved successfully');
    } catch (error) {
      console.error('❌ Error saving configuration:', error.message);
    }
  }

  /**
   * Get default configuration
   */
  getDefaultConfig() {
    return {
      api: {
        defaultDiscountThreshold: 60,
        useCache: false,
        cacheTimeout: 300,
        retryAttempts: 3,
        retryDelay: 3000,
        requestTimeout: 30000
      },
      telegram: {
        botToken: process.env.TELEGRAM_BOT_TOKEN || '',
        maxMessageLength: 4096,
        maxItemsPerMessage: 10,
        messageDelay: 1000
      },
      database: {
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT) || 5432,
        database: process.env.DB_NAME || 'deals_db',
        username: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || '',
        ssl: process.env.DB_SSL === 'true',
        maxConnections: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000
      },
      server: {
        port: parseInt(process.env.PORT) || 3001,
        host: process.env.HOST || 'localhost',
        cors: {
          origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
          credentials: true
        }
      },
      cleanup: {
        jsonLogsDays: 7,
        databaseDays: 30,
        scheduleIntervalHours: 24,
        enabled: true
      },
      features: {
        hideRepeatedItems: true,
        maxSaverPricing: true,
        keywordFiltering: true,
        duplicateRemoval: true,
        priceChangeDetection: true
      },
      limits: {
        maxCronJobsPerUser: 10,
        maxCodesPerJob: 20,
        maxItemsPerExecution: 100,
        maxExecutionTimeMs: 300000
      },
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        enableFileLogging: true,
        enableConsoleLogging: true,
        logDirectory: './logs'
      }
    };
  }

  /**
   * Apply environment variable overrides
   */
  applyEnvironmentOverrides() {
    // API configuration
    if (process.env.DEFAULT_DISCOUNT_THRESHOLD) {
      this.config.api.defaultDiscountThreshold = parseInt(process.env.DEFAULT_DISCOUNT_THRESHOLD);
    }
    
    if (process.env.USE_CACHE) {
      this.config.api.useCache = process.env.USE_CACHE === 'true';
    }

    // Telegram configuration
    if (process.env.TELEGRAM_BOT_TOKEN) {
      this.config.telegram.botToken = process.env.TELEGRAM_BOT_TOKEN;
    }

    // Database configuration
    if (process.env.DATABASE_URL) {
      const dbUrl = new URL(process.env.DATABASE_URL);
      this.config.database = {
        ...this.config.database,
        host: dbUrl.hostname,
        port: parseInt(dbUrl.port) || 5432,
        database: dbUrl.pathname.slice(1),
        username: dbUrl.username,
        password: dbUrl.password,
        ssl: dbUrl.searchParams.get('sslmode') === 'require'
      };
    }

    // Feature flags
    if (process.env.ENABLE_HIDE_REPEATED_ITEMS) {
      this.config.features.hideRepeatedItems = process.env.ENABLE_HIDE_REPEATED_ITEMS === 'true';
    }

    if (process.env.ENABLE_MAX_SAVER_PRICING) {
      this.config.features.maxSaverPricing = process.env.ENABLE_MAX_SAVER_PRICING === 'true';
    }
  }

  /**
   * Get configuration value
   */
  get(path, defaultValue = null) {
    const keys = path.split('.');
    let value = this.config;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }

    return value;
  }

  /**
   * Set configuration value
   */
  set(path, value) {
    const keys = path.split('.');
    let current = this.config;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
    this.save();
  }

  /**
   * Get all configuration
   */
  getAll() {
    return { ...this.config };
  }

  /**
   * Validate configuration
   */
  validate() {
    const errors = [];

    // Validate required fields
    if (!this.config.telegram.botToken) {
      errors.push('Telegram bot token is required');
    }

    if (!this.config.database.host) {
      errors.push('Database host is required');
    }

    if (!this.config.database.database) {
      errors.push('Database name is required');
    }

    // Validate numeric values
    if (this.config.api.defaultDiscountThreshold < 0 || this.config.api.defaultDiscountThreshold > 100) {
      errors.push('Default discount threshold must be between 0 and 100');
    }

    if (this.config.server.port < 1 || this.config.server.port > 65535) {
      errors.push('Server port must be between 1 and 65535');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig() {
    const env = process.env.NODE_ENV || 'development';
    
    const envConfig = {
      development: {
        logging: { level: 'debug' },
        api: { useCache: false }
      },
      production: {
        logging: { level: 'info' },
        api: { useCache: true }
      },
      test: {
        logging: { level: 'error' },
        database: { database: 'deals_db_test' }
      }
    };

    return envConfig[env] || {};
  }
}

// Singleton instance
let instance = null;

module.exports = {
  getInstance() {
    if (!instance) {
      instance = new ConfigManager();
    }
    return instance;
  },
  
  ConfigManager
};
