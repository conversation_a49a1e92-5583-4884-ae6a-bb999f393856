/**
 * Dependency Injection Container
 * 
 * Single responsibility: Manage service dependencies and lifecycle
 */

class Container {
  constructor() {
    this.services = new Map();
    this.singletons = new Map();
    this.factories = new Map();
  }

  /**
   * Register a singleton service
   */
  singleton(name, factory) {
    this.factories.set(name, { type: 'singleton', factory });
    return this;
  }

  /**
   * Register a transient service (new instance each time)
   */
  transient(name, factory) {
    this.factories.set(name, { type: 'transient', factory });
    return this;
  }

  /**
   * Register an instance
   */
  instance(name, instance) {
    this.singletons.set(name, instance);
    return this;
  }

  /**
   * Resolve a service
   */
  resolve(name) {
    // Check if we have a singleton instance
    if (this.singletons.has(name)) {
      return this.singletons.get(name);
    }

    // Check if we have a factory
    if (this.factories.has(name)) {
      const { type, factory } = this.factories.get(name);
      
      if (type === 'singleton') {
        const instance = factory(this);
        this.singletons.set(name, instance);
        return instance;
      } else {
        return factory(this);
      }
    }

    throw new Error(`Service '${name}' not found in container`);
  }

  /**
   * Check if service is registered
   */
  has(name) {
    return this.singletons.has(name) || this.factories.has(name);
  }

  /**
   * Get all registered service names
   */
  getServiceNames() {
    const names = new Set();
    
    for (const name of this.singletons.keys()) {
      names.add(name);
    }
    
    for (const name of this.factories.keys()) {
      names.add(name);
    }
    
    return Array.from(names);
  }

  /**
   * Clear all services (useful for testing)
   */
  clear() {
    this.services.clear();
    this.singletons.clear();
    this.factories.clear();
  }

  /**
   * Register all core services
   */
  registerCoreServices() {
    // Configuration
    this.singleton('config', () => {
      const { getInstance } = require('../config/ConfigManager');
      return getInstance();
    });

    // Database
    this.singleton('database', (container) => {
      const DatabaseSingleton = require('../../../dbSingleton');
      return DatabaseSingleton.getInstance();
    });

    // Repositories
    this.singleton('cronJobRepository', (container) => {
      const CronJobRepository = require('../../data/repositories/CronJobRepository');
      return new CronJobRepository(container.resolve('database'));
    });

    this.singleton('displayedItemsRepository', (container) => {
      const DisplayedItemsRepository = require('../../data/repositories/DisplayedItemsRepository');
      return new DisplayedItemsRepository(container.resolve('database'));
    });

    this.singleton('notificationRepository', (container) => {
      const NotificationRepository = require('../../data/repositories/NotificationRepository');
      return new NotificationRepository(container.resolve('database'));
    });

    // Services
    this.singleton('mappingService', () => {
      const MappingService = require('../../services/mapping/MappingService');
      return new MappingService();
    });

    this.singleton('cacheService', (container) => {
      const CacheService = require('../../services/cache/CacheService');
      const config = container.resolve('config');
      return new CacheService(config.get('api.cacheTimeout', 300));
    });

    this.singleton('dealApiService', (container) => {
      const DealApiService = require('../../services/api/DealApiService');
      return new DealApiService(
        container.resolve('cacheService'),
        container.resolve('mappingService')
      );
    });

    this.singleton('filterService', (container) => {
      const FilterService = require('../../core/dealDiscovery/FilterService');
      return new FilterService(
        container.resolve('displayedItemsRepository'),
        container.resolve('notificationRepository'),
        container.resolve('keywordRepository')
      );
    });

    this.singleton('validationService', () => {
      const ValidationService = require('../../core/validation/ValidationService');
      return new ValidationService();
    });

    // Core Services
    this.singleton('dealDiscoveryService', (container) => {
      const DealDiscoveryService = require('../../core/dealDiscovery/DealDiscoveryService');
      return new DealDiscoveryService(
        container.resolve('dealApiService'),
        container.resolve('filterService'),
        container.resolve('validationService')
      );
    });

    this.singleton('cronJobService', (container) => {
      const CronJobService = require('../../core/cronJobs/CronJobService');
      return new CronJobService(
        container.resolve('cronJobRepository'),
        container.resolve('dealDiscoveryService'),
        container.resolve('notificationService')
      );
    });

    this.singleton('priceGapService', (container) => {
      const PriceGapService = require('../../core/priceGap/PriceGapService');
      return new PriceGapService(
        container.resolve('dealApiService'),
        container.resolve('database')
      );
    });

    // Notification Services
    this.singleton('telegramService', (container) => {
      const TelegramService = require('../../services/telegram/TelegramService');
      const config = container.resolve('config');
      return new TelegramService(config.get('telegram.botToken'));
    });

    this.singleton('notificationService', (container) => {
      const NotificationService = require('../../services/notification/NotificationService');
      return new NotificationService(
        container.resolve('telegramService'),
        container.resolve('notificationRepository')
      );
    });

    // Cleanup Service
    this.singleton('cleanupService', (container) => {
      const CleanupService = require('../../services/cleanup/CleanupService');
      const config = container.resolve('config');
      return new CleanupService(
        container.resolve('database'),
        config.get('cleanup')
      );
    });

    console.log('✅ Core services registered in container');
    return this;
  }

  /**
   * Initialize all services
   */
  async initialize() {
    console.log('🚀 Initializing services...');
    
    try {
      // Initialize database
      const database = this.resolve('database');
      await database.initialize();
      
      // Initialize other services that need async setup
      const services = [
        'dealApiService',
        'cronJobService',
        'cleanupService'
      ];
      
      for (const serviceName of services) {
        const service = this.resolve(serviceName);
        if (service.initialize && typeof service.initialize === 'function') {
          await service.initialize();
          console.log(`✅ ${serviceName} initialized`);
        }
      }
      
      console.log('✅ All services initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing services:', error.message);
      throw error;
    }
  }

  /**
   * Shutdown all services
   */
  async shutdown() {
    console.log('🛑 Shutting down services...');
    
    const services = this.getServiceNames();
    
    for (const serviceName of services) {
      try {
        const service = this.resolve(serviceName);
        if (service.shutdown && typeof service.shutdown === 'function') {
          await service.shutdown();
          console.log(`✅ ${serviceName} shut down`);
        }
      } catch (error) {
        console.error(`❌ Error shutting down ${serviceName}:`, error.message);
      }
    }
    
    console.log('✅ All services shut down');
  }
}

// Global container instance
let globalContainer = null;

module.exports = {
  Container,
  
  getContainer() {
    if (!globalContainer) {
      globalContainer = new Container();
      globalContainer.registerCoreServices();
    }
    return globalContainer;
  },
  
  setContainer(container) {
    globalContainer = container;
  }
};
