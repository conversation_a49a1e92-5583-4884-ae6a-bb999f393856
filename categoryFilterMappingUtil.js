const fs = require('fs');
const path = require('path');

// Path to the mapping file
const mappingFilePath = path.join(__dirname, 'category_filter_mapping.json');

// Cache the mapping data to avoid reading the file multiple times
let mappingData = null;

/**
 * Loads the mapping data from the file
 * @returns {Object} The mapping data
 */
function loadMappingData() {
  if (mappingData) {
    return mappingData;
  }
  
  try {
    const data = fs.readFileSync(mappingFilePath, 'utf8');
    mappingData = JSON.parse(data);
    return mappingData;
  } catch (error) {
    console.error(`Error loading mapping data: ${error.message}`);
    return null;
  }
}

/**
 * Converts a mapping code to category and filter names
 * @param {string} code - The mapping code (e.g., "A1")
 * @returns {Object|null} An object with category and filter names, or null if the code is invalid
 */
function codeToNames(code) {
  const mapping = loadMappingData();
  if (!mapping || !mapping.mapping || !mapping.mapping[code]) {
    return null;
  }
  
  return mapping.mapping[code];
}

/**
 * Converts category and filter names to a mapping code
 * @param {string} categoryName - The category name
 * @param {string} [filterName] - The filter name (optional, if not provided returns category-only code)
 * @returns {string|null} The mapping code, or null if the names are invalid
 */
function namesToCode(categoryName, filterName) {
  const mapping = loadMappingData();
  if (!mapping || !mapping.reverseMapping) {
    return null;
  }

  // Check if the category exists in the reverse mapping
  if (!mapping.reverseMapping[categoryName]) {
    return null;
  }

  // If no filter name provided, return category-only code
  if (!filterName) {
    return mapping.reverseMapping[categoryName].categoryCode;
  }

  // Check if the filter exists for this category
  if (!mapping.reverseMapping[categoryName].filters[filterName]) {
    return null;
  }

  return mapping.reverseMapping[categoryName].filters[filterName];
}

/**
 * Checks if a string is a valid mapping code
 * @param {string} code - The string to check
 * @returns {boolean} True if the string is a valid mapping code, false otherwise
 */
function isValidCode(code) {
  if (!code || typeof code !== 'string') {
    return false;
  }

  // Check if the code matches the pattern of:
  // - Category-only: one or two letters (A, B, ..., Z, AA, AB, ...)
  // - Filter: one or two letters followed by one or more digits (A1, A2, ..., AA1, AB2, ...)
  const categoryOnlyPattern = /^[A-Z]{1,2}$/;
  const filterPattern = /^[A-Z]{1,2}\d+$/;

  if (!categoryOnlyPattern.test(code) && !filterPattern.test(code)) {
    return false;
  }

  // Check if the code exists in the mapping
  const mapping = loadMappingData();
  return mapping && mapping.mapping && mapping.mapping[code] !== undefined;
}

/**
 * Gets all available mapping codes
 * @returns {Array} An array of all mapping codes
 */
function getAllCodes() {
  const mapping = loadMappingData();
  if (!mapping || !mapping.mapping) {
    return [];
  }
  
  return Object.keys(mapping.mapping);
}

/**
 * Gets all available categories
 * @returns {Array} An array of all category names
 */
function getAllCategories() {
  const mapping = loadMappingData();
  if (!mapping || !mapping.reverseMapping) {
    return [];
  }
  
  return Object.keys(mapping.reverseMapping);
}

/**
 * Gets all available filters for a category
 * @param {string} categoryName - The category name
 * @returns {Array} An array of all filter names for the category
 */
function getFiltersForCategory(categoryName) {
  const mapping = loadMappingData();
  if (!mapping || !mapping.reverseMapping || !mapping.reverseMapping[categoryName]) {
    return [];
  }

  return Object.keys(mapping.reverseMapping[categoryName].filters || {});
}

/**
 * Checks if a code is a category-only code (no filter)
 * @param {string} code - The mapping code to check
 * @returns {boolean} True if it's a category-only code
 */
function isCategoryOnlyCode(code) {
  const mapping = loadMappingData();
  if (!mapping || !mapping.mapping || !mapping.mapping[code]) {
    return false;
  }

  const entry = mapping.mapping[code];
  return entry.category && !entry.filter;
}

/**
 * Gets the metadata about the mapping
 * @returns {Object} The metadata object
 */
function getMappingMetadata() {
  const mapping = loadMappingData();
  if (!mapping || !mapping.metadata) {
    return {};
  }
  
  return mapping.metadata;
}

// Export the functions
module.exports = {
  codeToNames,
  namesToCode,
  isValidCode,
  getAllCodes,
  getAllCategories,
  getFiltersForCategory,
  isCategoryOnlyCode,
  getMappingMetadata
};