#!/bin/bash

echo "🚀 Setting up <PERSON>wiggy Deals Dashboard Frontend..."

# Install backend dependencies
echo "📦 Installing backend dependencies..."
npm install

# Setup frontend
echo "📦 Setting up React frontend..."
cd frontend

# Install frontend dependencies
npm install

# Build the frontend for production (optional)
echo "🏗️ Building frontend for production..."
npm run build

cd ..

echo "✅ Setup complete!"
echo ""
echo "🎯 To start the development servers:"
echo "   npm run dev          # Start both API and frontend"
echo "   npm run api          # Start API server only (port 3001)"
echo "   npm run frontend     # Start React frontend only (port 3000)"
echo ""
echo "📊 Dashboard will be available at:"
echo "   Frontend: http://localhost:3000"
echo "   API:      http://localhost:3001/api"
echo ""
echo "🤖 Don't forget to start your Telegram bot:"
echo "   npm run bot"
