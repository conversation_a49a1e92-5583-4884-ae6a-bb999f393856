const TelegramBot = require('node-telegram-bot-api');
const cron = require('node-cron');
const fs = require('fs');
const path = require('path');
const discountedItemsUtil = require('./discountedItemsUtil');
const enhancedDealDiscovery = require('./enhancedDealDiscovery');
const mappingUtil = require('./categoryFilterMappingUtil');
const DatabaseSingleton = require('./dbSingleton');
const BotSingleton = require('./botSingleton');
const { scheduleCleanup } = require('./logCleanup');

// Override console.log to add IST timestamp
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

function getISTTimestamp() {
  const now = new Date();
  const istTime = new Date(now.getTime() + (5.5 * 60 * 60 * 1000)); // UTC + 5:30
  return istTime.toISOString().substr(11, 8); // Extract HH:MM:SS
}

console.log = function(...args) {
  originalConsoleLog(`[${getISTTimestamp()}]`, ...args);
};

console.error = function(...args) {
  originalConsoleError(`[${getISTTimestamp()}]`, ...args);
};

console.warn = function(...args) {
  originalConsoleWarn(`[${getISTTimestamp()}]`, ...args);
};

// Load configuration
const config = JSON.parse(fs.readFileSync(path.join(__dirname, 'config.json'), 'utf8'));

// Get bot instance from singleton
const bot = BotSingleton.getInstance();
const BOT_CONFIG = BotSingleton.getBotConfig();

// User session storage
const userSessions = new Map();

// Active cron jobs storage
const activeCronJobs = new Map();

// Database singleton will be initialized when needed
// Helper function to get database instance
async function getDatabase() {
  return await DatabaseSingleton.getInstance();
}

/**
 * Save cron jobs to persistent storage
 */
function saveCronJobs() {
  try {
    const jobsData = [];

    for (const [jobId, job] of activeCronJobs.entries()) {
      // Save job data without the task object (can't serialize functions)
      const jobToSave = {
        jobId: job.jobId,
        chatId: job.chatId,
        code: job.code,
        categoryName: job.categoryName,
        filterName: job.filterName,
        isCategoryOnly: job.isCategoryOnly,
        intervalMinutes: job.intervalMinutes,
        threshold: job.threshold,
        startTime: job.startTime,
        totalItemsFound: job.totalItemsFound || 0,
        executionCount: job.executionCount || 0,
        failedExecutions: job.failedExecutions || 0,
        lastRun: job.lastRun
      };
      jobsData.push(jobToSave);
    }

    fs.writeFileSync(CRON_JOBS_FILE, JSON.stringify(jobsData, null, 2), 'utf8');
    console.log(`💾 Saved ${jobsData.length} cron jobs to persistent storage`);
  } catch (error) {
    console.error('❌ Error saving cron jobs:', error.message);
  }
}

/**
 * Load cron jobs from persistent storage and restart them
 */
function loadCronJobs() {
  try {
    if (!fs.existsSync(CRON_JOBS_FILE)) {
      console.log('📂 No existing cron jobs file found');
      return;
    }

    const jobsData = JSON.parse(fs.readFileSync(CRON_JOBS_FILE, 'utf8'));
    let restoredCount = 0;

    for (const jobData of jobsData) {
      try {
        // Recreate the cron task
        const cronExpression = generateCronExpression(jobData.intervalMinutes);
        const task = cron.schedule(cronExpression, () => {
          executeCronJob(jobData.jobId);
        }, {
          scheduled: true,
          timezone: "Asia/Kolkata"
        });

        // Restore the job with the new task
        jobData.task = task;
        activeCronJobs.set(jobData.jobId, jobData);
        restoredCount++;

        console.log(`🔄 Restored cron job ${jobData.jobId} for user ${jobData.chatId}: ${jobData.code} every ${jobData.intervalMinutes}min`);
      } catch (error) {
        console.error(`❌ Error restoring cron job ${jobData.jobId}:`, error.message);
      }
    }

    console.log(`✅ Restored ${restoredCount} cron jobs from persistent storage`);
  } catch (error) {
    console.error('❌ Error loading cron jobs:', error.message);
  }
}

/**
 * Load cron jobs from persistent storage and restart them
 */
async function loadPersistedCronJobs() {
  try {
    // Get database singleton instance
    const cronPersistence = await DatabaseSingleton.getInstance();

    // Clean up any invalid jobs and old notified deals
    await cronPersistence.cleanupInvalidJobs();
    await cronPersistence.cleanupOldNotifiedDeals();

    const jobsData = await cronPersistence.loadCronJobs();
    let restoredCount = 0;

    for (const jobData of jobsData) {
      try {
        // Skip jobs with no valid codes
        if (!jobData.codes || jobData.codes.length === 0) {
          console.log(`⚠️ Skipping cron job ${jobData.jobId} - no valid codes found`);
          continue;
        }

        // Recreate the cron task based on schedule type
        let cronExpression;
        let logInfo;

        if (jobData.scheduleType === 'time' && jobData.scheduleTime) {
          cronExpression = generateTimeCronExpression(jobData.scheduleTime);
          logInfo = `daily at ${jobData.scheduleTime} IST`;
        } else {
          cronExpression = generateCronExpression(jobData.intervalMinutes);
          logInfo = `every ${jobData.intervalMinutes}min`;
        }

        const task = cron.schedule(cronExpression, () => {
          executeCronJob(jobData.jobId);
        }, {
          scheduled: true,
          timezone: "Asia/Kolkata"
        });

        // Restore the job with the new task
        jobData.task = task;
        activeCronJobs.set(jobData.jobId, jobData);
        restoredCount++;

        const codesText = jobData.codes.join(', ');
        console.log(`🔄 Restored cron job ${jobData.jobId} for user ${jobData.chatId}: ${codesText} ${logInfo}`);
      } catch (error) {
        console.error(`❌ Error restoring cron job ${jobData.jobId}:`, error.message);
      }
    }

    console.log(`✅ Restored ${restoredCount} cron jobs from persistent storage`);
  } catch (error) {
    console.error('❌ Error loading persisted cron jobs:', error.message);
  }
}

/**
 * Get or create user session
 */
function getUserSession(chatId) {
  if (!userSessions.has(chatId)) {
    userSessions.set(chatId, {
      state: 'idle',
      data: {}
    });
  }
  return userSessions.get(chatId);
}

/**
 * Create main menu keyboard
 */
function getMainMenuKeyboard() {
  return {
    reply_markup: {
      keyboard: [
        [{ text: '🔍 Find Code by Category/Filter' }],
        [{ text: '💰 Find Deals by Code(s)' }],
        [{ text: '⏰ Start Cron Job' }, { text: '⏹️ Manage Cron Jobs' }],
        [{ text: '🎯 Keyword Rules' }, { text: '📋 List All Categories' }],
        [{ text: '📊 Show Statistics' }],
        [{ text: '❓ Help' }]
      ],
      resize_keyboard: true,
      one_time_keyboard: false
    }
  };
}

/**
 * Create back to menu keyboard
 */
function getBackKeyboard() {
  return {
    reply_markup: {
      keyboard: [
        [{ text: '🏠 Back to Main Menu' }]
      ],
      resize_keyboard: true,
      one_time_keyboard: true
    }
  };
}

/**
 * Format item for display
 */
function formatItem(item, index) {
  const discount = parseFloat(item.discountPercentage);
  const discountEmoji = discount >= 70 ? '🔥' : discount >= 50 ? '⚡' : '💰';
  
  return `${discountEmoji} *${index}. ${item.name}*
${item.variation ? `_${item.variation}_` : ''}

💵 *Original:* ₹${item.storePrice}
🏷️ *Offer:* ₹${item.offerPrice}
📉 *Discount:* ${item.discountPercentage}% (Threshold: ${item.appliedThreshold}%)
📦 *Quantity:* ${item.quantity} ${item.unit}
🔗 [View Product](${item.url})

`;
}

/**
 * Generate cron expression for given interval in minutes
 */
function generateCronExpression(intervalMinutes) {
  const minutes = parseInt(intervalMinutes, 10);
  if (!Number.isFinite(minutes) || minutes <= 0) {
    return '* * * * *';
  }

  // If under an hour and cleanly divides 60, we can use minute step
  if (minutes < 60 && 60 % minutes === 0) {
    return `*/${minutes} * * * *`;
  }

  // Exact hours (e.g., 60, 120, 180) can use hourly step
  if (minutes % 60 === 0) {
    const hours = Math.max(1, Math.floor(minutes / 60));
    return `0 */${hours} * * *`;
  }

  // Fallback: run every minute and gate in code
  return '* * * * *';
}

/**
 * Generate cron expression for specific time (HH:MM format in IST)
 */
function generateTimeCronExpression(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return `${minutes} ${hours} * * *`;
}

/**
 * Calculate time remaining until next cron execution
 */
function getTimeUntilNextRun(job) {
  if (!job.task || !job.lastRun) {
    return 'Starting soon...';
  }

  const now = Date.now();
  const lastRun = job.lastRun;

  if (job.scheduleType === 'interval') {
    const intervalMs = job.intervalMinutes * 60 * 1000;
    const nextRun = lastRun + intervalMs;
    const timeRemaining = nextRun - now;

    if (timeRemaining <= 0) {
      return 'Running now...';
    }

    const minutes = Math.floor(timeRemaining / (60 * 1000));
    const seconds = Math.floor((timeRemaining % (60 * 1000)) / 1000);

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  } else if (job.scheduleType === 'time') {
    // For daily time-based schedules
    const [hours, minutes] = job.scheduleTime.split(':').map(Number);
    const now = new Date();
    const nextRun = new Date();
    nextRun.setHours(hours, minutes, 0, 0);

    // If the time has passed today, schedule for tomorrow
    if (nextRun <= now) {
      nextRun.setDate(nextRun.getDate() + 1);
    }

    const timeRemaining = nextRun.getTime() - now.getTime();
    const hoursRemaining = Math.floor(timeRemaining / (60 * 60 * 1000));
    const minutesRemaining = Math.floor((timeRemaining % (60 * 60 * 1000)) / (60 * 1000));

    if (hoursRemaining > 0) {
      return `${hoursRemaining}h ${minutesRemaining}m`;
    } else {
      return `${minutesRemaining}m`;
    }
  }

  return 'Unknown';
}

/**
 * Get user's active cron jobs
 */
function getUserCronJobs(chatId) {
  // Convert chatId to both string and number for comparison
  const chatIdStr = String(chatId);
  const chatIdNum = Number(chatId);

  return Array.from(activeCronJobs.entries())
    .filter(([jobId, job]) => {
      // Handle both string and number chatId comparisons
      return job.chatId === chatId ||
             job.chatId === chatIdStr ||
             job.chatId === chatIdNum ||
             String(job.chatId) === chatIdStr ||
             Number(job.chatId) === chatIdNum;
    })
    .map(([jobId, job]) => ({ jobId, ...job }));
}

/**
 * Create a unique job ID
 */
function generateJobId(chatId, codes) {
  // Handle both single code and multiple codes
  const codeString = Array.isArray(codes) ? codes.join('') : codes;
  // Clean the code string for use in ID (remove commas, spaces, special chars)
  const cleanCode = codeString.replace(/[^A-Z0-9]/g, '');

  // Add random component to ensure uniqueness even if created at same millisecond
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  return `${chatId}_${cleanCode}_${Date.now()}_${randomSuffix}`;
}

/**
 * Format cron job for display
 */
function formatCronJob(job, index) {
  const status = job.task ? 'Running' : 'Stopped';
  const elapsed = Math.floor((Date.now() - job.startTime) / 1000 / 60);
  const timeUntilNext = job.task ? getTimeUntilNextRun(job) : 'Stopped';

  // Handle both old and new format
  const codes = job.codes || [job.code];
  const codesText = codes.join(', ');
  const isMultiple = codes.length > 1;

  let scheduleInfo;
  if (job.scheduleType === 'time') {
    scheduleInfo = `🕐 *Daily at:* ${job.scheduleTime} IST`;
  } else {
    scheduleInfo = `⏱️ *Interval:* ${job.intervalMinutes} minutes`;
  }

  const codeLabel = isMultiple ? 'Codes' : 'Code';

  return `${index + 1}. *${codeLabel}:* ${codesText}
${scheduleInfo}
🎯 *Threshold:* ${job.threshold}%
📊 *Status:* ${status}
⏰ *Running for:* ${elapsed} minutes
⏳ *Next run:* ${timeUntilNext}
🔍 *Last check:* ${job.lastRun ? new Date(job.lastRun).toLocaleTimeString('en-IN', {timeZone: 'Asia/Kolkata'}) : 'Not yet'}
📦 *Items found:* ${job.totalItemsFound || 0}

`;
}

/**
 * Save execution log to file
 */
async function saveExecutionLog(jobId, logData) {
  try {
    const fs = require('fs');
    const path = require('path');

    // Create logs directory if it doesn't exist
    const logsDir = path.join(__dirname, 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    // Extract code from jobId for filename compatibility
    const jobParts = jobId.split('_');
    const codesPart = jobParts.length > 1 ? jobParts[1] : 'unknown';
    const timestamp = Date.now();

    const filename = `${codesPart}_${timestamp}.json`;
    const filepath = path.join(logsDir, filename);

    const logEntry = {
      jobId,
      ...logData,
      executionId: timestamp
    };

    fs.writeFileSync(filepath, JSON.stringify(logEntry, null, 2));
    console.log(`📝 Saved execution log: ${filename}`);
  } catch (error) {
    console.error('Error saving execution log:', error);
  }
}

/**
 * Execute cron job - fetch deals and send notifications
 */
async function executeCronJob(jobId) {
  const job = activeCronJobs.get(jobId);
  if (!job) return;

  try {
    const codes = job.codes || [job.code]; // Support both old and new format

    // Filter out empty/undefined codes
    const validCodes = codes.filter(code => code && code.trim().length > 0);

    if (validCodes.length === 0) {
      console.log(`⚠️ Cron job ${jobId} has no valid codes, skipping execution`);
      return;
    }

    console.log(`Executing cron job ${jobId} for user ${job.chatId} with codes: ${validCodes.join(', ')}`);

    // Process each code individually and combine results
    let allItems = [];

    for (const code of validCodes) {
      const options = {
        code: code,
        discountThreshold: job.threshold,
        useCache: config.api.useCache,
        apiType: job.apiType || 'existing',
        priceCalculation: job.priceCalculation || 'offer'
      };

      // Use enhanced deal discovery that supports both API types
      const items = await enhancedDealDiscovery.getHighlyDiscountedItems(options);
      allItems = allItems.concat(items);
    }

    // Remove duplicates based on item ID
    const uniqueItems = [];
    const seenIds = new Set();
    for (const item of allItems) {
      if (!seenIds.has(item.id)) {
        seenIds.add(item.id);
        uniqueItems.push(item);
      }
    }

    const items = uniqueItems;

    // Filter out deals that have already been notified (unless discount changed)
    // and apply keyword-based threshold filtering
    const newItems = [];
    let skippedCount = 0;
    let keywordFilteredCount = 0;

    const cronPersistence = await getDatabase();

    for (const item of items) {
      // First check keyword-based thresholds
      const meetsKeywordThresholds = await cronPersistence.checkKeywordThresholds(job.chatId, item);
      if (!meetsKeywordThresholds) {
        keywordFilteredCount++;
        continue;
      }

      // Check if this item should be hidden due to repeated display
      if (job.hideRepeatedItems) {
        const displayCheck = await cronPersistence.checkDisplayedItem(jobId, job.chatId, item);
        if (displayCheck.wasDisplayed && !displayCheck.pricingChanged) {
          console.log(`🔄 Skipping repeated item: ${item.name} (last shown: ${displayCheck.lastDisplayed})`);
          skippedCount++;
          continue;
        }
        if (displayCheck.wasDisplayed && displayCheck.pricingChanged) {
          console.log(`💰 Showing item with price change: ${item.name} (displayed ${displayCheck.displayCount} times)`);
        }
      }

      // Then check if already notified
      const shouldNotify = await cronPersistence.shouldNotifyDeal(job.chatId, item, job.threshold);
      if (shouldNotify) {
        newItems.push(item);
      } else {
        skippedCount++;
      }
    }



    // Generate execution ID
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const executionStartTime = Date.now();

    // Save execution log
    await saveExecutionLog(jobId, {
      executionId,
      timestamp: new Date().toISOString(),
      codes: validCodes,
      threshold: job.threshold,
      totalItems: items.length,
      newItems: newItems.length,
      skippedItems: skippedCount,
      keywordFilteredItems: keywordFilteredCount,
      items: newItems.slice(0, 50), // Save top 50 items
      success: true,
      error: null
    });

    // Save execution run to database
    const executionData = {
      executionId,
      jobId,
      chatId: job.chatId,
      codes: validCodes.join(', '),
      threshold: job.threshold,
      apiType: job.apiType || 'existing',
      priceCalculation: job.priceCalculation || 'offer',
      totalItemsFound: items.length,
      newItemsFound: newItems.length,
      skippedItems: skippedCount,
      keywordFilteredItems: keywordFilteredCount,
      executionTimeMs: Date.now() - executionStartTime,
      success: true,
      completedAt: new Date(),
      items: newItems // Include the actual items found
    };

    const executionId = await cronPersistence.saveCronExecutionRun(executionData);
    console.log(`💾 Saved execution ${executionId} with ${newItems.length} items`);

    // Save individual items to database
    if (items.length > 0) {
      await cronPersistence.saveCronExecutionResults(executionId, jobId, job.chatId, items);
    }

    // Update job statistics
    job.lastRun = Date.now();
    job.totalItemsFound = (job.totalItemsFound || 0) + items.length;
    job.executionCount = (job.executionCount || 0) + 1;

    // Save updated statistics to persistent storage
    await cronPersistence.updateCronJobStats(jobId, {
      totalItemsFound: job.totalItemsFound,
      executionCount: job.executionCount,
      failedExecutions: job.failedExecutions || 0,
      lastRun: job.lastRun
    });

    if (newItems.length > 0) {
      // Limit items to prevent spam
      const limitedItems = newItems.slice(0, config.cron.notificationLimit);

      const codesText = validCodes.join(', ');
      const title = `🔔 Cron Alert: ${codesText}`;
      const summary = newItems.length === items.length
        ? `Found ${newItems.length} new deals (showing top ${limitedItems.length})`
        : `Found ${items.length} deals, ${newItems.length} new/changed (showing top ${limitedItems.length})`;

      // Send notification
      await bot.sendMessage(job.chatId, `${title}\n\n${summary}`, { parse_mode: 'Markdown' });
      await sendItemsInChunks(job.chatId, limitedItems, title);

      // Record all notified deals
      for (const item of limitedItems) {
        await cronPersistence.recordNotifiedDeal(job.chatId, item);

        // Mark item as displayed if hideRepeatedItems is enabled
        if (job.hideRepeatedItems) {
          await cronPersistence.markItemAsDisplayed(jobId, job.chatId, item);
        }
      }

    }
  } catch (error) {
    console.error(`Error in cron job ${jobId}:`, error);

    // Generate execution ID for failed run
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_failed`;

    // Save error log
    await saveExecutionLog(jobId, {
      executionId,
      timestamp: new Date().toISOString(),
      codes: job.codes || [job.code],
      threshold: job.threshold,
      totalItems: 0,
      newItems: 0,
      skippedItems: 0,
      keywordFilteredItems: 0,
      items: [],
      success: false,
      error: error.message
    });

    // Save failed execution to database
    const cronPersistence = await getDatabase();
    const executionData = {
      executionId,
      jobId,
      chatId: job.chatId,
      codes: (job.codes || [job.code]).join(', '),
      threshold: job.threshold,
      apiType: job.apiType || 'existing',
      priceCalculation: job.priceCalculation || 'offer',
      totalItemsFound: 0,
      newItemsFound: 0,
      skippedItems: 0,
      keywordFilteredItems: 0,
      executionTimeMs: 0,
      success: false,
      errorMessage: error.message,
      completedAt: new Date(),
      items: [] // No items for failed execution
    };

    await cronPersistence.saveCronExecutionRun(executionData);

    // Update job statistics for failed runs
    job.lastRun = Date.now();
    job.executionCount = (job.executionCount || 0) + 1;
    job.failedExecutions = (job.failedExecutions || 0) + 1;

    // Save updated statistics to persistent storage
    await cronPersistence.updateCronJobStats(jobId, {
      totalItemsFound: job.totalItemsFound || 0,
      executionCount: job.executionCount,
      failedExecutions: job.failedExecutions,
      lastRun: job.lastRun
    });

    // Determine error type for better user messaging
    let errorType = 'Unknown Error';
    let userMessage = error.message;

    if (error.message.includes('API request failed after')) {
      errorType = 'API Connection Issue';
      userMessage = 'Unable to connect to Swiggy API after multiple retries. This is usually temporary.';
    } else if (error.message.includes('Failed to parse response')) {
      errorType = 'Data Parsing Error';
      userMessage = 'Received invalid data from Swiggy API. This is usually temporary.';
    } else if (error.message.includes('Request failed')) {
      errorType = 'Network Error';
      userMessage = 'Network connection issue. Will retry on next scheduled run.';
    }

    // Notify user of error with more context
    await bot.sendMessage(job.chatId, `❌ *Cron Job Error*

🔖 *Code:* ${job.code}
🚨 *Error Type:* ${errorType}
📝 *Details:* ${userMessage}

📊 *Job Stats:*
• Total runs: ${job.executionCount}
• Failed runs: ${job.failedExecutions}
• Success rate: ${Math.round(((job.executionCount - job.failedExecutions) / job.executionCount) * 100)}%

⚠️ Job will continue running and retry on next scheduled interval.`, { parse_mode: 'Markdown' });
  }
}

/**
 * Send items in chunks to avoid message length limits
 */
async function sendItemsInChunks(chatId, items, title) {
  if (items.length === 0) {
    await bot.sendMessage(chatId, '❌ No highly discounted items found with the current criteria.', getBackKeyboard());
    return;
  }

  // Send summary first
  await bot.sendMessage(chatId, `🎉 *${title}*\n\nFound *${items.length}* highly discounted items!\n\nSending results...`, { parse_mode: 'Markdown' });

  // Send items in chunks
  const chunkSize = BOT_CONFIG.maxItemsPerMessage;
  for (let i = 0; i < items.length; i += chunkSize) {
    const chunk = items.slice(i, i + chunkSize);
    let message = '';
    
    chunk.forEach((item, index) => {
      message += formatItem(item, i + index + 1);
    });

    // Ensure message doesn't exceed Telegram's limit
    if (message.length > BOT_CONFIG.maxMessageLength) {
      // Send items one by one if chunk is too large
      for (let j = 0; j < chunk.length; j++) {
        const singleItem = formatItem(chunk[j], i + j + 1);
        await bot.sendMessage(chatId, singleItem, { parse_mode: 'Markdown' });
        
        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } else {
      await bot.sendMessage(chatId, message, { parse_mode: 'Markdown' });
    }
    
    // Delay between chunks
    if (i + chunkSize < items.length) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  // Send completion message
  await bot.sendMessage(chatId, `✅ *Completed!*\n\nShowed ${items.length} items in total.`, {
    parse_mode: 'Markdown',
    ...getBackKeyboard()
  });
}

/**
 * Handle start command
 */
bot.onText(/\/start/, async (msg) => {
  const chatId = msg.chat.id;
  const session = getUserSession(chatId);
  session.state = 'idle';

  const welcomeMessage = `🛒 *Welcome to Swiggy Deals Bot!*

I can help you find the best discounted items from Swiggy Instamart.

*Available Features:*
🔍 Find mapping codes by category/filter names
💰 Get deals using mapping codes
📋 Browse all available categories
📊 View system statistics

Choose an option from the menu below:`;

  await bot.sendMessage(chatId, welcomeMessage, {
    parse_mode: 'Markdown',
    ...getMainMenuKeyboard()
  });
});

/**
 * Handle keyword rules command
 */
bot.onText(/🎯 Keyword Rules/, async (msg) => {
  const chatId = msg.chat.id;

  try {
    const cronPersistence = await getDatabase();
    const keywordThresholds = await cronPersistence.getKeywordThresholds(chatId);

    let message = `🎯 *Keyword-Based Discount Rules*

These rules apply to ALL your cron jobs. If a deal contains any of these keywords, it must meet the specified discount threshold to be notified.

*Current Rules:*\n`;

    if (keywordThresholds.length === 0) {
      message += `No keyword rules configured.

*Example:* Set "lifetime" to require 90% discount for any deal containing "lifetime" in the name.`;
    } else {
      keywordThresholds.forEach((rule, index) => {
        message += `${index + 1}. "${rule.keyword}" → ${rule.threshold}% minimum\n`;
      });
    }

    message += `\n*Commands:*
• Send "add lifelong 90" to require 90% discount for "lifelong" deals
• Send "remove lifelong" to delete a rule
• Send "clear all" to remove all rules`;

    await bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });

    // Set user state for keyword management
    const session = getUserSession(chatId);
    session.state = 'managing_keywords';
  } catch (error) {
    console.error('Error showing keyword rules:', error);
    await bot.sendMessage(chatId, '❌ Error loading keyword rules. Please try again.', getMainMenuKeyboard());
  }
});

/**
 * Handle help command
 */
bot.onText(/\/help|❓ Help/, async (msg) => {
  const chatId = msg.chat.id;
  
  const helpMessage = `📖 *Help & Instructions*

*🔍 Find Code by Category/Filter:*
• Enter category name (e.g., "Appliances")
• Optionally add filter name (e.g., "Air Fryers")
• Get the corresponding mapping code

*💰 Find Deals by Code(s):*
• Enter single code (e.g., "A1") or multiple codes (e.g., "A1,D15,K5")
• Set discount threshold (default: ${config.api.defaultDiscountThreshold}%)
• Get highly discounted items with images and details

*⏰ Cron Jobs (Automated Monitoring):*
• Set up automatic deal checking for any code
• Default interval: ${config.cron.defaultInterval} minutes (customizable)
• Get notifications only when deals are found
• Manage up to ${config.cron.maxActiveJobs} active jobs simultaneously

*⏹️ Manage Cron Jobs:*
• View all your active monitoring jobs
• Stop individual jobs or all jobs at once
• See job statistics and performance

*📋 Categories & Codes:*
• Category-only codes: A, B, C, etc. (all filters in category)
• Filter-specific codes: A1, A2, B1, etc. (specific filters)

*💡 Tips:*
• Use category-only codes (like "A") to get deals from all filters
• Combine multiple codes with commas for broader search
• Lower thresholds show more items, higher thresholds show better deals
• Cron jobs are perfect for monitoring high-value categories

*🔧 Settings:*
• Cache: ${config.api.useCache ? 'Enabled' : 'Disabled'}
• API Delay: ${config.api.requestDelay / 1000}s between requests
• Default Threshold: ${config.api.defaultDiscountThreshold}%
• Cron Interval: ${config.cron.defaultInterval} minutes
• Max Notifications: ${config.cron.notificationLimit} items per alert`;

  await bot.sendMessage(chatId, helpMessage, {
    parse_mode: 'Markdown',
    ...getBackKeyboard()
  });
});

/**
 * Handle find code by category/filter
 */
bot.onText(/🔍 Find Code by Category\/Filter/, async (msg) => {
  const chatId = msg.chat.id;
  const session = getUserSession(chatId);
  session.state = 'waiting_for_category';
  session.data = {};

  await bot.sendMessage(chatId, `🔍 *Find Mapping Code*

Please enter the category name (e.g., "Appliances", "Bath and Body"):

You can also include a filter name after the category:
• "Appliances" → Get category-only code
• "Appliances Air Fryers" → Get specific filter code

*Available categories:* Fresh Fruits, Appliances, Bath and Body, Dairy Bread and Eggs, etc.`, {
    parse_mode: 'Markdown',
    ...getBackKeyboard()
  });
});

/**
 * Handle find deals by codes
 */
bot.onText(/💰 Find Deals by Code\(s\)/, async (msg) => {
  const chatId = msg.chat.id;
  const session = getUserSession(chatId);
  session.state = 'waiting_for_codes';
  session.data = {};

  await bot.sendMessage(chatId, `💰 *Find Deals by Code(s)*

Please enter mapping code(s):

*Examples:*
• \`A\` → All filters in Appliances category
• \`A1\` → Specific filter (Air Fryers)
• \`A,D,K\` → Multiple categories
• \`A1,A2,D15\` → Multiple specific filters

*Format:* Use commas to separate multiple codes`, {
    parse_mode: 'Markdown',
    ...getBackKeyboard()
  });
});

/**
 * Handle list categories
 */
bot.onText(/📋 List All Categories/, async (msg) => {
  const chatId = msg.chat.id;

  try {
    const categories = mappingUtil.getAllCategories();
    let message = '📋 *All Available Categories:*\n\n';

    categories.forEach((category, index) => {
      const code = mappingUtil.namesToCode(category);
      message += `${index + 1}. *${category}* (Code: \`${code}\`)\n`;
    });

    message += `\n💡 *Tip:* Use category codes (like \`A\`, \`B\`) to get all deals from that category!`;

    await bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
  } catch (error) {
    await bot.sendMessage(chatId, '❌ Error loading categories. Please try again.', getBackKeyboard());
  }
});

/**
 * Handle show statistics
 */
bot.onText(/📊 Show Statistics/, async (msg) => {
  const chatId = msg.chat.id;

  try {
    const cronPersistence = await getDatabase();
    const metadata = mappingUtil.getMappingMetadata();
    const categories = mappingUtil.getAllCategories();
    const userJobs = getUserCronJobs(chatId);
    const notificationStats = await cronPersistence.getNotificationStats(chatId);

    let statsMessage = `📊 *System Statistics*

🏪 *Categories:* ${metadata.totalCategories}
🔖 *Category-Only Codes:* ${metadata.totalCategoryOnlyMappings}
🎯 *Filter-Specific Codes:* ${metadata.totalFilterMappings}
📝 *Total Mappings:* ${metadata.totalMappings}

⚙️ *Configuration:*
• Cache: ${config.api.useCache ? '✅ Enabled' : '❌ Disabled'}
• API Delay: ${config.api.requestDelay / 1000}s
• Default Threshold: ${config.api.defaultDiscountThreshold}%
• Blacklist Keywords: ${config.filtering.blacklistKeywords.length}
• Custom Thresholds: ${Object.keys(config.filtering.customDiscountThresholds).length}

👤 *Your Activity:*
• Active Cron Jobs: ${userJobs.length}/${config.cron.maxActiveJobs}`;

    if (notificationStats) {
      statsMessage += `
• Total Deals Tracked: ${notificationStats.total_deals}
• Deals Notified Multiple Times: ${notificationStats.repeated_deals}
• Avg Notifications per Deal: ${parseFloat(notificationStats.avg_notifications_per_deal).toFixed(1)}`;
    }

    statsMessage += `

🕒 *Last Updated:* ${new Date(metadata.generatedAt).toLocaleString()}`;

    await bot.sendMessage(chatId, statsMessage, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
  } catch (error) {
    await bot.sendMessage(chatId, '❌ Error loading statistics. Please try again.', getBackKeyboard());
  }
});

/**
 * Handle start cron job
 */
bot.onText(/⏰ Start Cron Job/, async (msg) => {
  const chatId = msg.chat.id;
  const session = getUserSession(chatId);

  // Check if user has reached max active jobs
  const userJobs = getUserCronJobs(chatId);
  if (userJobs.length >= config.cron.maxActiveJobs) {
    await bot.sendMessage(chatId, `⚠️ *Maximum Active Jobs Reached*

You have ${userJobs.length}/${config.cron.maxActiveJobs} active cron jobs.
Please stop some jobs before creating new ones.

Use "⏹️ Manage Cron Jobs" to view and stop existing jobs.`, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
    return;
  }

  session.state = 'waiting_for_cron_code';
  session.data = {};

  await bot.sendMessage(chatId, `⏰ *Start Cron Job*

Enter the mapping code(s) for automated deal checking:

*Single Code Examples:*
• \`A\` → Monitor all Appliances deals
• \`D\` → Monitor all Bath and Body deals
• \`A1\` → Monitor only Air Fryers deals
• \`K5\` → Monitor specific filter

*Multiple Codes Examples:*
• \`A1,B2,C3\` → Monitor Air Fryers, Shampoo, and Chocolates
• \`A,D\` → Monitor all Appliances and Bath & Body deals
• \`K1,K2,K3,K4\` → Monitor multiple specific filters

*Current Settings:*
• Default interval: ${config.cron.defaultInterval} minutes
• Default threshold: ${config.cron.defaultThreshold}%
• Max notifications per run: ${config.cron.notificationLimit} items`, {
    parse_mode: 'Markdown',
    ...getBackKeyboard()
  });
});

/**
 * Handle manage cron jobs
 */
bot.onText(/⏹️ Manage Cron Jobs/, async (msg) => {
  const chatId = msg.chat.id;

  console.log(`[DEBUG] Managing cron jobs for chatId: ${chatId} (type: ${typeof chatId})`);
  console.log(`[DEBUG] Active cron jobs count: ${activeCronJobs.size}`);

  // Debug: Log all active jobs
  for (const [jobId, job] of activeCronJobs.entries()) {
    console.log(`[DEBUG] Job ${jobId}: chatId=${job.chatId} (type: ${typeof job.chatId})`);
  }

  const userJobs = getUserCronJobs(chatId);
  console.log(`[DEBUG] Found ${userJobs.length} jobs for user ${chatId}`);

  if (userJobs.length === 0) {
    await bot.sendMessage(chatId, `📭 *No Active Cron Jobs*

You don't have any active cron jobs.
Use "⏰ Start Cron Job" to create one!`, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
    return;
  }

  let message = `⏹️ *Your Active Cron Jobs* (${userJobs.length}/${config.cron.maxActiveJobs})\n\n`;

  userJobs.forEach((job, index) => {
    message += formatCronJob(job, index);
  });

  message += `💡 *Commands:*
• Send job number to stop (e.g., "1", "2")
• Send "stop all" to stop all jobs
• Send "refresh" to update status`;

  const session = getUserSession(chatId);
  session.state = 'managing_cron_jobs';
  session.data = { userJobs };

  await bot.sendMessage(chatId, message, {
    parse_mode: 'Markdown',
    ...getBackKeyboard()
  });
});

/**
 * Handle back to main menu
 */
bot.onText(/🏠 Back to Main Menu/, async (msg) => {
  const chatId = msg.chat.id;
  const session = getUserSession(chatId);
  session.state = 'idle';
  session.data = {};

  await bot.sendMessage(chatId, '🏠 Back to main menu. Choose an option:', getMainMenuKeyboard());
});

/**
 * Handle text messages based on user state
 */
bot.on('message', async (msg) => {
  const chatId = msg.chat.id;
  const text = msg.text;
  const session = getUserSession(chatId);

  // Skip if it's a command or menu button
  if (text.startsWith('/') || text.includes('🔍') || text.includes('💰') || text.includes('⏰') || text.includes('⏹️') || text.includes('📋') || text.includes('📊') || text.includes('❓') || text.includes('🏠')) {
    return;
  }

  try {
    switch (session.state) {
      case 'waiting_for_category':
        await handleCategoryInput(chatId, text, session);
        break;

      case 'waiting_for_codes':
        await handleCodesInput(chatId, text, session);
        break;

      case 'waiting_for_threshold':
        await handleThresholdInput(chatId, text, session);
        break;

      case 'waiting_for_cron_code':
        await handleCronCodeInput(chatId, text, session);
        break;



      case 'waiting_for_cron_interval':
        await handleCronIntervalInput(chatId, text, session);
        break;

      case 'waiting_for_cron_time':
        await handleCronTimeInput(chatId, text, session);
        break;

      case 'waiting_for_cron_threshold':
        await handleCronThresholdInput(chatId, text, session);
        break;

      case 'waiting_for_custom_threshold':
        await handleCustomThresholdInput(chatId, text, session);
        break;

      case 'managing_keywords':
        await handleKeywordManagement(chatId, text, session);
        break;

      case 'managing_cron_jobs':
        await handleCronJobManagement(chatId, text, session);
        break;

      default:
        await bot.sendMessage(chatId, '❓ I didn\'t understand that. Please use the menu options or type /help for assistance.', getMainMenuKeyboard());
    }
  } catch (error) {
    console.error('Error handling message:', error);
    await bot.sendMessage(chatId, '❌ An error occurred. Please try again or contact support.', getMainMenuKeyboard());
  }
});

/**
 * Handle category input for finding codes
 */
async function handleCategoryInput(chatId, text, session) {
  const parts = text.trim().split(/\s+/);
  const categoryName = parts.slice(0, -1).join(' ') || parts.join(' ');
  const filterName = parts.length > 1 ? parts[parts.length - 1] : null;

  // Try to find exact category match first
  const categories = mappingUtil.getAllCategories();
  let matchedCategory = categories.find(cat => cat.toLowerCase() === text.toLowerCase());

  if (!matchedCategory) {
    // Try partial match
    matchedCategory = categories.find(cat => cat.toLowerCase().includes(text.toLowerCase()));
  }

  if (matchedCategory) {
    // Category found, check if filter was specified
    if (parts.length > 1) {
      const filters = mappingUtil.getFiltersForCategory(matchedCategory);
      const matchedFilter = filters.find(filter => filter.toLowerCase().includes(parts[parts.length - 1].toLowerCase()));

      if (matchedFilter) {
        const code = mappingUtil.namesToCode(matchedCategory, matchedFilter);
        await bot.sendMessage(chatId, `✅ *Code Found!*

📂 *Category:* ${matchedCategory}
🎯 *Filter:* ${matchedFilter}
🔖 *Code:* \`${code}\`

You can use this code to find deals!`, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
      } else {
        const categoryCode = mappingUtil.namesToCode(matchedCategory);
        const filters = mappingUtil.getFiltersForCategory(matchedCategory);

        let message = `⚠️ Filter "${parts[parts.length - 1]}" not found in "${matchedCategory}".

📂 *Category Code:* \`${categoryCode}\` (all filters)

*Available filters:*\n`;

        filters.slice(0, 10).forEach(filter => {
          const filterCode = mappingUtil.namesToCode(matchedCategory, filter);
          message += `• ${filter} (\`${filterCode}\`)\n`;
        });

        if (filters.length > 10) {
          message += `\n... and ${filters.length - 10} more filters`;
        }

        await bot.sendMessage(chatId, message, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
      }
    } else {
      // Category only
      const code = mappingUtil.namesToCode(matchedCategory);
      const filters = mappingUtil.getFiltersForCategory(matchedCategory);

      await bot.sendMessage(chatId, `✅ *Category Code Found!*

📂 *Category:* ${matchedCategory}
🔖 *Code:* \`${code}\` (includes all ${filters.length} filters)

Use this code to get deals from all filters in this category!`, {
        parse_mode: 'Markdown',
        ...getBackKeyboard()
      });
    }
  } else {
    // No category found, show suggestions
    const suggestions = categories.filter(cat =>
      cat.toLowerCase().includes(text.toLowerCase()) ||
      text.toLowerCase().includes(cat.toLowerCase())
    ).slice(0, 5);

    let message = `❌ Category "${text}" not found.`;

    if (suggestions.length > 0) {
      message += '\n\n*Did you mean:*\n';
      suggestions.forEach(suggestion => {
        const code = mappingUtil.namesToCode(suggestion);
        message += `• ${suggestion} (\`${code}\`)\n`;
      });
    } else {
      message += '\n\nUse 📋 "List All Categories" to see available options.';
    }

    await bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
  }

  session.state = 'idle';
}

/**
 * Handle codes input for finding deals
 */
async function handleCodesInput(chatId, text, session) {
  const codes = text.trim().split(',').map(code => code.trim().toUpperCase());
  const invalidCodes = codes.filter(code => !mappingUtil.isValidCode(code));

  if (invalidCodes.length > 0) {
    await bot.sendMessage(chatId, `❌ Invalid code(s): ${invalidCodes.join(', ')}

Please enter valid mapping codes. Examples:
• \`A\` (category-only)
• \`A1\` (specific filter)
• \`A,D,K\` (multiple categories)`, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
    return;
  }

  session.data.codes = codes;
  session.state = 'waiting_for_threshold';

  await bot.sendMessage(chatId, `✅ *Codes Accepted:* ${codes.join(', ')}

Now enter the discount threshold percentage (10-90):

*Examples:*
• \`20\` → Show items with 20%+ discount
• \`50\` → Show items with 50%+ discount
• \`75\` → Show only highly discounted items

*Default:* ${config.api.defaultDiscountThreshold}% (just press Enter)`, {
    parse_mode: 'Markdown',
    ...getBackKeyboard()
  });
}

/**
 * Handle threshold input and fetch deals
 */
async function handleThresholdInput(chatId, text, session) {
  let threshold = config.api.defaultDiscountThreshold;

  if (text.trim()) {
    const inputThreshold = parseInt(text.trim());
    if (isNaN(inputThreshold) || inputThreshold < 10 || inputThreshold > 90) {
      await bot.sendMessage(chatId, '❌ Please enter a valid threshold between 10 and 90.', getBackKeyboard());
      return;
    }
    threshold = inputThreshold;
  }

  const codes = session.data.codes;
  session.state = 'idle';
  session.data = {};

  // Show processing message
  await bot.sendMessage(chatId, `🔄 *Processing...*

Fetching deals for codes: ${codes.join(', ')}
Threshold: ${threshold}%

This may take a few minutes...`, {
    parse_mode: 'Markdown'
  });

  try {
    const startTime = Date.now();
    let allItems = [];

    // Process each code
    for (let i = 0; i < codes.length; i++) {
      const code = codes[i];

      if (codes.length > 1) {
        await bot.sendMessage(chatId, `⏳ Processing code ${i + 1}/${codes.length}: ${code}...`);
      }

      const options = {
        code: code,
        discountThreshold: threshold,
        useCache: config.api.useCache
      };

      const items = await discountedItemsUtil.getHighlyDiscountedItems(options);
      allItems = allItems.concat(items);

      if (codes.length > 1) {
        await bot.sendMessage(chatId, `✅ Found ${items.length} items for code ${code}`);
      }
    }

    // Remove duplicates
    const uniqueItems = [];
    const seenIds = new Set();
    for (const item of allItems) {
      if (!seenIds.has(item.id)) {
        seenIds.add(item.id);
        uniqueItems.push(item);
      }
    }

    // Sort by discount percentage (highest first)
    uniqueItems.sort((a, b) => parseFloat(b.discountPercentage) - parseFloat(a.discountPercentage));

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);

    // Save search history
    try {
      const fs = require('fs');
      const path = require('path');

      const searchData = {
        timestamp: new Date().toISOString(),
        codes,
        threshold,
        totalItems: allItems.length,
        uniqueItems: uniqueItems.length,
        duration: parseFloat(duration),
        source: 'mobile',
        chatId
      };

      const historyDir = path.join(__dirname, 'search_history');
      if (!fs.existsSync(historyDir)) {
        fs.mkdirSync(historyDir, { recursive: true });
      }

      const historyFile = path.join(historyDir, `search_${Date.now()}.json`);
      fs.writeFileSync(historyFile, JSON.stringify({
        ...searchData,
        items: uniqueItems.slice(0, 50) // Save top 50 items
      }, null, 2));

      console.log(`📝 Saved search history: ${historyFile}`);
    } catch (error) {
      console.error('Error saving search history:', error);
    }

    const title = codes.length === 1
      ? `Deals for Code ${codes[0]} (${threshold}%+ discount)`
      : `Combined Deals for ${codes.length} Codes (${threshold}%+ discount)`;

    await sendItemsInChunks(chatId, uniqueItems, title);

    // Send summary
    const summaryMessage = `📈 *Search Summary*

🔖 *Codes:* ${codes.join(', ')}
🎯 *Threshold:* ${threshold}%
📦 *Total Items:* ${allItems.length}
🎯 *Unique Items:* ${uniqueItems.length}
⏱️ *Duration:* ${duration}s`;

    await bot.sendMessage(chatId, summaryMessage, {
      parse_mode: 'Markdown',
      ...getMainMenuKeyboard()
    });

  } catch (error) {
    console.error('Error fetching deals:', error);
    await bot.sendMessage(chatId, `❌ Error fetching deals: ${error.message}

Please try again or contact support.`, getMainMenuKeyboard());
  }
}



/**
 * Handle cron time input
 */
async function handleCronTimeInput(chatId, text, session) {
  const input = text.trim();

  // Validate time format (HH:MM)
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;
  if (!timeRegex.test(input)) {
    await bot.sendMessage(chatId, `❌ Invalid time format: ${input}

Please enter time in HH:MM format (24-hour):
• \`09:30\` → 9:30 AM
• \`14:15\` → 2:15 PM
• \`21:00\` → 9:00 PM`, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
    return;
  }

  session.data.scheduleTime = input;
  session.state = 'waiting_for_cron_threshold';

  const codesText = session.data.codes ? session.data.codes.join(',') : '';
  await bot.sendMessage(chatId, `🕐 *Schedule Time:* ${input} IST (Daily)

Choose discount threshold percentage:`, {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '🔥 65%', callback_data: `threshold_65_${codesText}` },
          { text: '🚀 75%', callback_data: `threshold_75_${codesText}` },
          { text: '💎 85%', callback_data: `threshold_85_${codesText}` }
        ],
        [
          { text: '⚙️ Custom', callback_data: `threshold_custom_${codesText}` }
        ],
        [
          { text: '🏠 Back to Main Menu', callback_data: 'back_to_main' }
        ]
      ]
    }
  });
}

/**
 * Handle cron code input (supports multiple codes)
 */
async function handleCronCodeInput(chatId, text, session) {
  const input = text.trim().toUpperCase();
  const codes = input.split(',').map(code => code.trim()).filter(code => code.length > 0);

  // Validate all codes
  const invalidCodes = [];
  const validCodes = [];
  const codeDescriptions = [];

  for (const code of codes) {
    if (!mappingUtil.isValidCode(code)) {
      invalidCodes.push(code);
    } else {
      validCodes.push(code);
      const names = mappingUtil.codeToNames(code);
      const isCategoryOnly = mappingUtil.isCategoryOnlyCode(code);
      const description = isCategoryOnly
        ? `${names.category} (ALL FILTERS)`
        : `${names.category} / ${names.filter}`;
      codeDescriptions.push(`• ${code} → ${description}`);
    }
  }

  if (invalidCodes.length > 0) {
    await bot.sendMessage(chatId, `❌ Invalid code(s): ${invalidCodes.join(', ')}

Please enter valid mapping code(s). Examples:
• \`A\` (single category)
• \`A1,B2\` (multiple specific filters)
• \`A,D\` (multiple categories)`, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
    return;
  }

  if (validCodes.length === 0) {
    await bot.sendMessage(chatId, `❌ No valid codes provided.

Please enter at least one valid mapping code.`, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
    return;
  }

  // Store multiple codes
  session.data.codes = validCodes;
  session.data.isMultipleCodes = validCodes.length > 1;
  // State will be set by callback query handler

  const codesText = validCodes.join(', ');
  const descriptionsText = codeDescriptions.join('\n');

  await bot.sendMessage(chatId, `✅ *Code(s) Accepted:* ${codesText}
📂 *Target(s):*
${descriptionsText}

Choose your monitoring schedule:`, {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '⏱️ Interval (Every X minutes)', callback_data: `schedule_interval_${codesText}` },
          { text: '🕐 Daily Time (Specific IST time)', callback_data: `schedule_time_${codesText}` }
        ],
        [
          { text: `⚡ Default (${config.cron.defaultInterval} min)`, callback_data: `schedule_default_${codesText}` }
        ],
        [
          { text: '🏠 Back to Main Menu', callback_data: 'back_to_main' }
        ]
      ]
    }
  });
}

/**
 * Handle cron interval input
 */
async function handleCronIntervalInput(chatId, text, session) {
  let interval = config.cron.defaultInterval;

  if (text.trim()) {
    const inputInterval = parseInt(text.trim());
    if (isNaN(inputInterval) || inputInterval < 5 || inputInterval > 1440) {
      await bot.sendMessage(chatId, '❌ Please enter a valid interval between 5 and 1440 minutes (24 hours).', getBackKeyboard());
      return;
    }
    interval = inputInterval;
  }

  session.data.intervalMinutes = interval;
  session.state = 'waiting_for_cron_threshold';

  const codesText = session.data.codes ? session.data.codes.join(',') : '';
  await bot.sendMessage(chatId, `⏱️ *Interval Set:* ${interval} minutes

Choose discount threshold percentage:`, {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '🔥 65%', callback_data: `threshold_65_${codesText}` },
          { text: '🚀 75%', callback_data: `threshold_75_${codesText}` },
          { text: '💎 85%', callback_data: `threshold_85_${codesText}` }
        ],
        [
          { text: '⚙️ Custom', callback_data: `threshold_custom_${codesText}` }
        ],
        [
          { text: '🏠 Back to Main Menu', callback_data: 'back_to_main' }
        ]
      ]
    }
  });
}

/**
 * Handle keyword management commands
 */
async function handleKeywordManagement(chatId, text, session) {
  const input = text.trim().toLowerCase();

  try {
    if (input.startsWith('add ')) {
      // Format: "add keyword threshold"
      const parts = input.split(' ');
      if (parts.length !== 3) {
        await bot.sendMessage(chatId, `❌ Invalid format. Use: "add keyword threshold"

*Example:* add lifelong 90`, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
        return;
      }

      const keyword = parts[1];
      const threshold = parseInt(parts[2]);

      if (isNaN(threshold) || threshold < 10 || threshold > 95) {
        await bot.sendMessage(chatId, `❌ Invalid threshold: ${parts[2]}

Threshold must be between 10 and 95.`, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
        return;
      }

      const cronPersistence = await getDatabase();
      const success = await cronPersistence.setKeywordThreshold(chatId, keyword, threshold);
      if (success) {
        await bot.sendMessage(chatId, `✅ *Keyword Rule Added*

"${keyword}" → ${threshold}% minimum discount

This rule will apply to all your cron jobs.`, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
      } else {
        await bot.sendMessage(chatId, '❌ Failed to add keyword rule. Please try again.', getBackKeyboard());
      }

    } else if (input.startsWith('remove ')) {
      // Format: "remove keyword"
      const keyword = input.substring(7).trim();
      if (!keyword) {
        await bot.sendMessage(chatId, `❌ Invalid format. Use: "remove keyword"

*Example:* remove lifelong`, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
        return;
      }

      const cronPersistence = await getDatabase();
      const success = await cronPersistence.deleteKeywordThreshold(chatId, keyword);
      if (success) {
        await bot.sendMessage(chatId, `✅ *Keyword Rule Removed*

"${keyword}" rule has been deleted.`, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
      } else {
        await bot.sendMessage(chatId, `❌ Keyword "${keyword}" not found or failed to delete.`, getBackKeyboard());
      }

    } else if (input === 'clear all') {
      const cronPersistence = await getDatabase();
      const keywordThresholds = await cronPersistence.getKeywordThresholds(chatId);
      let deletedCount = 0;

      for (const rule of keywordThresholds) {
        const success = await cronPersistence.deleteKeywordThreshold(chatId, rule.keyword);
        if (success) deletedCount++;
      }

      await bot.sendMessage(chatId, `✅ *All Keyword Rules Cleared*

Removed ${deletedCount} keyword rules.`, {
        parse_mode: 'Markdown',
        ...getBackKeyboard()
      });

    } else {
      await bot.sendMessage(chatId, `❌ Unknown command: "${text}"

*Available commands:*
• "add keyword threshold" - Add new rule
• "remove keyword" - Delete rule
• "clear all" - Remove all rules

*Example:* add lifelong 90`, {
        parse_mode: 'Markdown',
        ...getBackKeyboard()
      });
    }

    // Reset state
    session.state = 'idle';

  } catch (error) {
    console.error('Error handling keyword management:', error);
    await bot.sendMessage(chatId, '❌ An error occurred. Please try again.', getMainMenuKeyboard());
    session.state = 'idle';
  }
}

/**
 * Handle custom threshold input
 */
async function handleCustomThresholdInput(chatId, text, session) {
  const input = text.trim();
  let threshold = config.cron.defaultThreshold; // default

  if (input && input !== '') {
    const inputThreshold = parseInt(input);
    if (isNaN(inputThreshold) || inputThreshold < 10 || inputThreshold > 95) {
      await bot.sendMessage(chatId, `❌ Invalid threshold: ${input}

Please enter a number between 10 and 95.`, {
        parse_mode: 'Markdown',
        ...getBackKeyboard()
      });
      return;
    }
    threshold = inputThreshold;
  }

  await createCronJobWithThreshold(chatId, session, threshold);
}

/**
 * Create cron job with specified threshold
 */
async function createCronJobWithThreshold(chatId, session, threshold) {
  // Create the cron job
  const codes = session.data.codes || [];
  const jobId = generateJobId(chatId, codes);
  const scheduleType = session.data.scheduleType || 'interval';

  let cronExpression;
  if (scheduleType === 'time') {
    cronExpression = generateTimeCronExpression(session.data.scheduleTime);
  } else {
    cronExpression = generateCronExpression(session.data.intervalMinutes);
  }

  const jobData = {
    jobId,
    chatId,
    codes: codes,
    isMultipleCodes: session.data.isMultipleCodes || false,
    scheduleType: scheduleType,
    intervalMinutes: session.data.intervalMinutes,
    scheduleTime: session.data.scheduleTime,
    threshold,
    startTime: Date.now(),
    totalItemsFound: 0,
    executionCount: 0,
    lastRun: null
  };

  // Create and start the cron task
  const task = cron.schedule(cronExpression, () => {
    executeCronJob(jobId);
  }, {
    scheduled: true,
    timezone: "Asia/Kolkata"
  });

  jobData.task = task;
  activeCronJobs.set(jobId, jobData);

  // Save to persistent storage
  console.log(`💾 Attempting to save cron job ${jobId} to database...`);
  const cronPersistence = await getDatabase();
  await cronPersistence.saveCronJob(jobData);
  console.log(`✅ Cron job ${jobId} saved successfully`);

  // Store session data before clearing it
  const isMultipleCodes = session.data.isMultipleCodes || false;
  const intervalMinutes = session.data.intervalMinutes;
  const scheduleTime = session.data.scheduleTime;

  session.state = 'idle';
  session.data = {};

  // Generate descriptions for all codes
  const descriptions = codes.map(code => {
    const names = mappingUtil.codeToNames(code);
    const isCategoryOnly = mappingUtil.isCategoryOnlyCode(code);
    return isCategoryOnly
      ? `${names.category} (ALL FILTERS)`
      : `${names.category} / ${names.filter}`;
  });

  const codesText = codes.join(', ');
  const descriptionsText = descriptions.map((desc, index) => `• ${codes[index]} → ${desc}`).join('\n');

  let scheduleInfo, nextCheckInfo, logInfo;
  if (scheduleType === 'time') {
    scheduleInfo = `🕐 *Schedule:* Daily at ${scheduleTime} IST`;
    nextCheckInfo = `*Next check:* Today/Tomorrow at ${scheduleTime} IST`;
    logInfo = `daily at ${scheduleTime} IST`;
  } else {
    scheduleInfo = `⏱️ *Interval:* ${intervalMinutes} minutes`;
    nextCheckInfo = `*Next check:* Within ${intervalMinutes} minutes`;
    logInfo = `every ${intervalMinutes}min`;
  }

  const targetLabel = isMultipleCodes ? 'Targets' : 'Target';
  const codeLabel = isMultipleCodes ? 'Codes' : 'Code';

  await bot.sendMessage(chatId, `✅ *Cron Job Created Successfully!*

🔖 *${codeLabel}:* ${codesText}
📂 *${targetLabel}:*
${descriptionsText}
${scheduleInfo}
🎯 *Threshold:* ${threshold}%
🚀 *Status:* Running

The job will start monitoring deals and notify you when items are found!

${nextCheckInfo}`, {
    parse_mode: 'Markdown',
    ...getMainMenuKeyboard()
  });


}

/**
 * Handle cron threshold input and create job
 */
async function handleCronThresholdInput(chatId, text, session) {
  let threshold = config.cron.defaultThreshold;

  if (text.trim()) {
    const inputThreshold = parseInt(text.trim());
    if (isNaN(inputThreshold) || inputThreshold < 10 || inputThreshold > 90) {
      await bot.sendMessage(chatId, '❌ Please enter a valid threshold between 10 and 90.', getBackKeyboard());
      return;
    }
    threshold = inputThreshold;
  }

  // Create the cron job
  const codes = session.data.codes || [];
  const jobId = generateJobId(chatId, codes);
  const scheduleType = session.data.scheduleType || 'interval';

  let cronExpression;
  if (scheduleType === 'time') {
    cronExpression = generateTimeCronExpression(session.data.scheduleTime);
  } else {
    cronExpression = generateCronExpression(session.data.intervalMinutes);
  }

  const jobData = {
    jobId,
    chatId,
    codes: codes,
    isMultipleCodes: session.data.isMultipleCodes || false,
    scheduleType: scheduleType,
    intervalMinutes: session.data.intervalMinutes,
    scheduleTime: session.data.scheduleTime,
    threshold,
    startTime: Date.now(),
    totalItemsFound: 0,
    executionCount: 0,
    lastRun: null
  };

  // Create and start the cron task
  const task = cron.schedule(cronExpression, () => {
    executeCronJob(jobId);
  }, {
    scheduled: true,
    timezone: "Asia/Kolkata"
  });

  jobData.task = task;
  activeCronJobs.set(jobId, jobData);

  // Save to persistent storage
  console.log(`💾 Attempting to save cron job ${jobId} to database...`);
  const cronPersistence = await getDatabase();
  await cronPersistence.saveCronJob(jobData);
  console.log(`✅ Cron job ${jobId} saved successfully`);

  // Store session data before clearing it
  const isMultipleCodes = session.data.isMultipleCodes || false;
  const intervalMinutes = session.data.intervalMinutes;
  const scheduleTime = session.data.scheduleTime;

  session.state = 'idle';
  session.data = {};

  // Generate descriptions for all codes
  const descriptions = codes.map(code => {
    const names = mappingUtil.codeToNames(code);
    const isCategoryOnly = mappingUtil.isCategoryOnlyCode(code);
    return isCategoryOnly
      ? `${names.category} (ALL FILTERS)`
      : `${names.category} / ${names.filter}`;
  });

  const codesText = codes.join(', ');
  const descriptionsText = descriptions.map((desc, index) => `• ${codes[index]} → ${desc}`).join('\n');

  let scheduleInfo, nextCheckInfo, logInfo;
  if (scheduleType === 'time') {
    scheduleInfo = `🕐 *Schedule:* Daily at ${scheduleTime} IST`;
    nextCheckInfo = `*Next check:* Today/Tomorrow at ${scheduleTime} IST`;
    logInfo = `daily at ${scheduleTime} IST`;
  } else {
    scheduleInfo = `⏱️ *Interval:* ${intervalMinutes} minutes`;
    nextCheckInfo = `*Next check:* Within ${intervalMinutes} minutes`;
    logInfo = `every ${intervalMinutes}min`;
  }

  const targetLabel = isMultipleCodes ? 'Targets' : 'Target';
  const codeLabel = isMultipleCodes ? 'Codes' : 'Code';

  await bot.sendMessage(chatId, `✅ *Cron Job Created Successfully!*

🔖 *${codeLabel}:* ${codesText}
📂 *${targetLabel}:*
${descriptionsText}
${scheduleInfo}
🎯 *Threshold:* ${threshold}%
🚀 *Status:* Running

The job will start monitoring deals and notify you when items are found!

${nextCheckInfo}`, {
    parse_mode: 'Markdown',
    ...getMainMenuKeyboard()
  });

  console.log(`Created cron job ${jobId} for user ${chatId}: ${codesText} ${logInfo}`);
}

/**
 * Handle cron job management commands
 */
async function handleCronJobManagement(chatId, text, session) {
  const input = text.trim().toLowerCase();
  const userJobs = getUserCronJobs(chatId);

  if (input === 'refresh') {
    // Refresh job list
    const updatedJobs = getUserCronJobs(chatId);

    if (updatedJobs.length === 0) {
      await bot.sendMessage(chatId, '📭 No active cron jobs found.', getMainMenuKeyboard());
      session.state = 'idle';
      return;
    }

    let message = `⏹️ *Your Active Cron Jobs* (${updatedJobs.length}/${config.cron.maxActiveJobs})\n\n`;
    updatedJobs.forEach((job, index) => {
      message += formatCronJob(job, index);
    });

    message += `💡 *Commands:*
• Send job number to stop (e.g., "1", "2")
• Send "stop all" to stop all jobs
• Send "refresh" to update status`;

    await bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown',
      ...getBackKeyboard()
    });
    return;
  }

  if (input === 'stop all') {
    // Stop all user's jobs
    let stoppedCount = 0;

    for (const job of userJobs) {
      if (job.task) {
        job.task.stop();
        job.task.destroy();
      }
      activeCronJobs.delete(job.jobId);
      const cronPersistence = await getDatabase();
      await cronPersistence.deleteCronJob(job.jobId);
      stoppedCount++;
    }

    session.state = 'idle';
    session.data = {};

    await bot.sendMessage(chatId, `✅ *All Jobs Stopped*

Stopped ${stoppedCount} cron job(s) successfully.`, {
      parse_mode: 'Markdown',
      ...getMainMenuKeyboard()
    });

    console.log(`User ${chatId} stopped all ${stoppedCount} cron jobs`);
    return;
  }

  // Check if input is a job number
  const jobNumber = parseInt(input);
  if (!isNaN(jobNumber) && jobNumber >= 1 && jobNumber <= userJobs.length) {
    const jobToStop = userJobs[jobNumber - 1];

    // Stop the specific job
    if (jobToStop.task) {
      jobToStop.task.stop();
      jobToStop.task.destroy();
    }
    activeCronJobs.delete(jobToStop.jobId);
    const cronPersistence = await getDatabase();
    await cronPersistence.deleteCronJob(jobToStop.jobId);

    session.state = 'idle';
    session.data = {};

    const description = jobToStop.isCategoryOnly
      ? `${jobToStop.categoryName} (ALL FILTERS)`
      : `${jobToStop.categoryName} / ${jobToStop.filterName}`;

    await bot.sendMessage(chatId, `✅ *Job Stopped Successfully*

🔖 *Code:* ${jobToStop.code}
📂 *Target:* ${description}
⏱️ *Was running for:* ${Math.floor((Date.now() - jobToStop.startTime) / 1000 / 60)} minutes
📦 *Total items found:* ${jobToStop.totalItemsFound || 0}`, {
      parse_mode: 'Markdown',
      ...getMainMenuKeyboard()
    });

    console.log(`User ${chatId} stopped cron job ${jobToStop.jobId}`);
    return;
  }

  // Invalid input
  await bot.sendMessage(chatId, `❌ Invalid command: "${text}"

*Valid commands:*
• Job number (1, 2, 3, etc.)
• "stop all" - Stop all jobs
• "refresh" - Update job list`, {
    parse_mode: 'Markdown',
    ...getBackKeyboard()
  });
}

/**
 * Handle callback queries (inline keyboard buttons)
 */
bot.on('callback_query', async (callbackQuery) => {
  const chatId = callbackQuery.message.chat.id;
  const data = callbackQuery.data;
  const session = getUserSession(chatId);

  try {
    // Answer the callback query to remove loading state
    await bot.answerCallbackQuery(callbackQuery.id);

    if (data === 'back_to_main') {
      session.state = 'idle';
      session.data = {};
      await bot.sendMessage(chatId, '🏠 *Back to Main Menu*', {
        parse_mode: 'Markdown',
        ...getMainMenuKeyboard()
      });
      return;
    }

    // Handle schedule type selection
    if (data.startsWith('schedule_')) {
      const parts = data.split('_');
      const scheduleType = parts[1];
      const codesText = parts.slice(2).join('_'); // Rejoin in case codes contain underscores
      const codes = codesText.split(',').map(code => code.trim());

      // Restore session data
      session.data.codes = codes;
      session.data.isMultipleCodes = codes.length > 1;
      session.data.scheduleType = scheduleType === 'default' ? 'interval' : scheduleType;

      if (scheduleType === 'interval') {
        session.state = 'waiting_for_cron_interval';
        await bot.sendMessage(chatId, `⏱️ *Schedule Type:* Interval-based

Now enter the monitoring interval in minutes:

*Examples:*
• \`10\` → Check every 10 minutes
• \`30\` → Check every 30 minutes
• \`60\` → Check every hour

*Send a number between 5 and 1440 minutes*`, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
      } else if (scheduleType === 'default') {
        // Use default interval
        session.data.intervalMinutes = config.cron.defaultInterval;
        session.state = 'waiting_for_cron_threshold';
        await bot.sendMessage(chatId, `⚡ *Schedule Type:* Default Interval (${config.cron.defaultInterval} minutes)

Choose discount threshold percentage:`, {
          parse_mode: 'Markdown',
          reply_markup: {
            inline_keyboard: [
              [
                { text: '🔥 65%', callback_data: `threshold_65_${codesText}` },
                { text: '🚀 75%', callback_data: `threshold_75_${codesText}` },
                { text: '💎 85%', callback_data: `threshold_85_${codesText}` }
              ],
              [
                { text: '⚙️ Custom', callback_data: `threshold_custom_${codesText}` }
              ],
              [
                { text: '🏠 Back to Main Menu', callback_data: 'back_to_main' }
              ]
            ]
          }
        });
      } else if (scheduleType === 'time') {
        session.state = 'waiting_for_cron_time';
        await bot.sendMessage(chatId, `🕐 *Schedule Type:* Time-based (Daily)

Enter the time in 24-hour format (HH:MM IST):

*Examples:*
• \`09:30\` → Run daily at 9:30 AM IST
• \`14:15\` → Run daily at 2:15 PM IST
• \`21:00\` → Run daily at 9:00 PM IST

*Note:* Time is in Indian Standard Time (IST)`, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
      }
    }

    // Handle threshold selection
    if (data.startsWith('threshold_')) {
      const parts = data.split('_');
      const thresholdValue = parts[1];
      const codesText = parts.slice(2).join('_');
      const codes = codesText.split(',').map(code => code.trim());

      // Restore session data
      session.data.codes = codes;
      session.data.isMultipleCodes = codes.length > 1;

      if (thresholdValue === 'custom') {
        session.state = 'waiting_for_custom_threshold';
        await bot.sendMessage(chatId, `⚙️ *Custom Threshold*

Enter your custom discount threshold percentage:

*Examples:*
• \`45\` → Show items with 45%+ discount
• \`90\` → Show only extremely high discounts

*Send a number between 10 and 95*`, {
          parse_mode: 'Markdown',
          ...getBackKeyboard()
        });
      } else {
        const threshold = parseInt(thresholdValue);
        await createCronJobWithThreshold(chatId, session, threshold);
      }
    }
  } catch (error) {
    console.error('Error handling callback query:', error);
    await bot.sendMessage(chatId, '❌ An error occurred. Please try again.', getMainMenuKeyboard());
  }
});

/**
 * Start the bot using singleton
 */
async function startBot() {
  try {
    // Start bot via singleton (prevents multiple instances)
    await BotSingleton.startBot();

    console.log('⏰ Cron Job Settings:');
    console.log(`   - Default Interval: ${config.cron.defaultInterval} minutes`);
    console.log(`   - Max Active Jobs: ${config.cron.maxActiveJobs} per user`);
    console.log(`   - Default Threshold: ${config.cron.defaultThreshold}%`);
    console.log(`   - Notification Limit: ${config.cron.notificationLimit} items per alert`);

    // Load persisted cron jobs
    console.log('');
    console.log('🔄 Loading persisted cron jobs...');
    await loadPersistedCronJobs();

    // Schedule automatic cleanup
    console.log('');
    console.log('🧹 Scheduling automatic cleanup...');
    scheduleCleanup(24); // Run cleanup every 24 hours
  } catch (error) {
    console.error('❌ Error starting Telegram bot:', error);
    throw error;
  }
}

/**
 * Stop the bot using singleton
 */
async function stopBot() {
  try {
    await BotSingleton.stopBot();
    console.log('🛑 Telegram bot stopped successfully!');
  } catch (error) {
    console.error('❌ Error stopping Telegram bot:', error);
    throw error;
  }
}

/**
 * Check if bot is running
 */
function isBotRunning() {
  return BotSingleton.isRunning();
}

/**
 * Get active cron jobs for status checking
 */
function getActiveCronJobs() {
  return activeCronJobs;
}

/**
 * Get user cron jobs (for external access)
 */
function getUserCronJobsExternal(chatId) {
  return getUserCronJobs(chatId);
}

/**
 * API functions for frontend integration
 */

/**
 * Create cron job from API request
 */
async function createCronJobFromAPI(jobData) {
  const { codes, scheduleType, intervalMinutes, scheduleTime, threshold, chatId, apiType, priceCalculation, hideRepeatedItems } = jobData;

  // Get database singleton instance
  const cronPersistence = await getDatabase();

  // Generate job ID
  const jobId = generateJobId(chatId, codes);

  // Create cron expression
  let cronExpression;
  if (scheduleType === 'time') {
    cronExpression = generateTimeCronExpression(scheduleTime);
  } else {
    cronExpression = generateCronExpression(intervalMinutes);
  }

  // Create job data
  const cronJobData = {
    jobId,
    chatId,
    codes: codes,
    isMultipleCodes: codes.length > 1,
    scheduleType: scheduleType,
    intervalMinutes: intervalMinutes,
    scheduleTime: scheduleTime,
    threshold,
    apiType: apiType || 'existing',
    priceCalculation: priceCalculation || 'offer',
    hideRepeatedItems: hideRepeatedItems || false,
    startTime: Date.now(),
    totalItemsFound: 0,
    executionCount: 0,
    lastRun: null
  };

  // Create and start the cron task
  const task = cron.schedule(cronExpression, () => {
    executeCronJob(jobId);
  }, {
    scheduled: true,
    timezone: "Asia/Kolkata"
  });

  cronJobData.task = task;
  activeCronJobs.set(jobId, cronJobData);

  // Save to persistent storage
  await cronPersistence.saveCronJob(cronJobData);

  console.log(`Created cron job ${jobId} via API: ${codes.join(', ')}`);

  return {
    jobId,
    codes,
    scheduleType,
    intervalMinutes,
    scheduleTime,
    threshold,
    status: 'running'
  };
}

/**
 * Delete cron job from API request
 */
async function deleteCronJobFromAPI(jobId) {
  // Get database singleton instance
  const cronPersistence = await getDatabase();

  const job = activeCronJobs.get(jobId);

  if (!job) {
    return false;
  }

  // Stop the cron task
  if (job.task) {
    job.task.stop();
    job.task.destroy();
  }

  // Remove from active jobs
  activeCronJobs.delete(jobId);

  // Delete from persistent storage
  await cronPersistence.deleteCronJob(jobId);

  console.log(`Deleted cron job ${jobId} via API`);

  return true;
}

/**
 * Execute a cron job by ID, even if it is not currently active in memory
 */
async function executeCronJobById(jobId) {
  // If already active, just delegate to existing executor
  if (activeCronJobs.has(jobId)) {
    await executeCronJob(jobId);
    return true;
  }

  try {
    const cronPersistence = await getDatabase();
    const jobs = await cronPersistence.loadCronJobs();
    const job = jobs.find(j => j.jobId === jobId);
    if (!job) {
      return false;
    }

    // Build a minimal in-memory job object compatible with executeCronJob
    const tempJob = {
      jobId: job.jobId,
      chatId: job.chatId,
      codes: job.codes,
      threshold: job.threshold,
      apiType: job.apiType || 'existing',
      priceCalculation: job.priceCalculation || 'offer',
      hideRepeatedItems: !!job.hideRepeatedItems
    };

    // Temporarily place into active map to reuse existing logic
    activeCronJobs.set(job.jobId, tempJob);
    try {
      await executeCronJob(job.jobId);
    } finally {
      // Remove temporary entry to avoid pretending it is scheduled
      activeCronJobs.delete(job.jobId);
    }

    return true;
  } catch (error) {
    console.error(`❌ Error executing cron job by ID ${jobId}:`, error.message);
    return false;
  }
}

// Export for external use
module.exports = {
  bot,
  BOT_CONFIG,
  startBot,
  stopBot,
  isBotRunning,
  getActiveCronJobs,
  getUserCronJobsExternal,
  executeCronJob,
  executeCronJobById,
  createCronJobFromAPI,
  deleteCronJobFromAPI
};