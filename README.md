# Discounted Items Utility

A utility for fetching highly discounted items from Swiggy Instamart based on category and filter.

## Overview

This utility provides a function to:
1. Find items with significant discounts (default threshold: 75%)
2. Filter items based on category and filter
3. Calculate discount percentages based on store price and offer price
4. Return detailed information about the discounted items

## Installation

No additional installation is required. The utility uses built-in Node.js modules:
- `fs` for file system operations
- `path` for path manipulation
- `https` for making API requests

## Usage

### Function: `getHighlyDiscountedItems`

```javascript
const discountedItemsUtil = require('./discountedItemsUtil');

// Example 1: Using category and filter names
const items1 = await discountedItemsUtil.getHighlyDiscountedItems({
  categoryName: 'Fresh Fruits',
  filterName: 'Seasonal Fruits',
  discountThreshold: 20,
  useCache: true
});

// Example 2: Using mapping code
const items2 = await discountedItemsUtil.getHighlyDiscountedItems({
  code: 'A1', // This will be converted to the corresponding category and filter
  discountThreshold: 20
});

// Example 3: Processing all filters for a category
const items3 = await discountedItemsUtil.getHighlyDiscountedItems({
  categoryName: 'Fresh Fruits',
  discountThreshold: 20
});
```

### Parameters

The function accepts a single options object with the following properties:

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| categoryName | string | The name of the category (e.g., "Fresh Fruits") | Required if code not provided |
| filterName | string | The name of the filter (e.g., "Seasonal Fruits"), if not provided all filters will be analyzed | Optional |
| code | string | The mapping code (e.g., "A1") as an alternative to categoryName and filterName | Required if categoryName not provided |
| discountThreshold | number | Minimum discount percentage to include items | 75 |
| useCache | boolean | Whether to use cached responses from logs instead of making new API requests | true |

### Return Value

The function returns an array of objects, each representing a discounted item with the following properties:

```javascript
{
  "name": "Item Name",
  "variation": "Variation Name",
  "storePrice": 100,
  "offerPrice": 20,
  "discountPercentage": "80.00",
  "quantity": "500",
  "unit": "g",
  "id": "ITEM_ID",
  "images": ["image1.jpg", "image2.jpg"]
}
```

## Category-Filter Mapping System

The utility includes a mapping system that allows you to use short codes instead of category and filter names. The mapping is in the format "A1": { category: "Category Name", filter: "Filter Name" }, where:

- Categories are represented by alphabets (A, B, C, ..., Z, AA, AB, ...)
- Filters are represented by numbers (1, 2, ...)

### Generating the Mapping

To generate the mapping file, run:

```bash
node generateCategoryFilterMapping.js
```

This will create a `category_filter_mapping.json` file with the mapping data.

### Using the Mapping

You can use the mapping in two ways:

1. Pass the mapping code directly to the `getHighlyDiscountedItems` function:

```javascript
const items = await discountedItemsUtil.getHighlyDiscountedItems({
  code: 'A1',
  discountThreshold: 20
});
```

2. Use the mapping utility functions to convert between codes and names:

```javascript
const mappingUtil = require('./categoryFilterMappingUtil');

// Convert code to names
const names = mappingUtil.codeToNames('A1');
console.log(names.category, names.filter);

// Convert names to code
const code = mappingUtil.namesToCode('Fresh Fruits', 'Seasonal Fruits');
console.log(code);
```

## Example

```javascript
const discountedItemsUtil = require('./discountedItemsUtil');

async function findDiscountedFruits() {
  try {
    // Get highly discounted seasonal fruits
    const items = await discountedItemsUtil.getHighlyDiscountedItems({
      categoryName: 'Fresh Fruits',
      filterName: 'Seasonal Fruits',
      discountThreshold: 20
    });
    
    console.log(`Found ${items.length} highly discounted items:`);
    items.forEach((item, index) => {
      console.log(`${index + 1}. ${item.name} (${item.variation})`);
      console.log(`   Original Price: ₹${item.storePrice}`);
      console.log(`   Offer Price: ₹${item.offerPrice}`);
      console.log(`   Discount: ${item.discountPercentage}%`);
      console.log(`   Quantity: ${item.quantity} ${item.unit}`);
      console.log('');
    });
  } catch (error) {
    console.error(`Error: ${error.message}`);
  }
}

findDiscountedFruits();
```

## Command-Line Runner

The project includes a command-line runner that provides an easy way to use the utility without writing code. The runner supports all the functionality of the utility, including category-filter mapping, and adds some additional features like listing available categories and filters.

### Usage

```bash
node runner.js [options]
```

### Options

| Option | Description | Default |
|--------|-------------|---------|
| `--category`, `-c` | Category name (e.g., "Fresh Fruits") | Required if code not provided |
| `--filter`, `-f` | Filter name (e.g., "Seasonal Fruits") | Optional |
| `--code`, `-m` | Mapping code (e.g., "A1") as an alternative to category and filter | Required if category not provided |
| `--threshold`, `-t` | Discount threshold percentage | 75 |
| `--no-cache` | Disable using cached responses | Cache is enabled by default |
| `--list-categories` | List all available categories | - |
| `--list-filters` | List all filters for a category (requires `--category`) | - |
| `--list-codes` | List all available mapping codes | - |
| `--help`, `-h` | Show help message | - |

### Examples

1. Fetch items with at least 20% discount in the "Fresh Fruits" category with "Seasonal Fruits" filter:

```bash
node runner.js --category "Fresh Fruits" --filter "Seasonal Fruits" --threshold 20
```

2. Fetch items using a mapping code with a custom discount threshold and without using cache:

```bash
node runner.js --code "Q12" --threshold 30 --no-cache
```

3. Process all filters for a category:

```bash
node runner.js --category "Fresh Fruits" --threshold 25
```

4. List all available categories:

```bash
node runner.js --list-categories
```

5. List all filters for a specific category:

```bash
node runner.js --list-filters --category "Fresh Fruits"
```

6. List all available mapping codes:

```bash
node runner.js --list-codes
```

### Output

The runner displays the results in a user-friendly format, including:
- The options being used
- The number of items found
- Detailed information for each item (name, prices, discount percentage, quantity, ID)
- Execution time

Items are sorted by discount percentage (highest first) for easier viewing.

## Testing

To test the utility, run:

```bash
node testDiscountedItems.js
```

This will run several test cases with different categories, filters, and discount thresholds.

To test the mapping functionality, run:

```bash
node testCategoryFilterMapping.js
```

This will test the mapping utility functions and the updated discountedItemsUtil functions.

## How It Works

1. The utility finds the appropriate filter URL from the `category_filters_data.json` file
2. It makes a POST request to the filter URL with the payload `{"facets":{},"sortAttribute":"priceLowToHigh"}`
3. It extracts items from the last widget in the response
4. It calculates discount percentages based on store price and offer price
5. It filters items that exceed the specified discount threshold
6. It returns the filtered items with detailed information

## Error Handling

The utility handles various error cases:
- Missing or invalid category/filter names
- Invalid mapping codes
- API errors or unexpected response formats
- Items without proper price information

In case of errors, the function returns an empty array and logs the error message to the console.