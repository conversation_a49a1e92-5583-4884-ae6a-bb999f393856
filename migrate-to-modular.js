#!/usr/bin/env node

/**
 * Migration Script: Legacy to Modular Architecture
 * 
 * Helps migrate from the old monolithic structure to the new modular architecture
 */

const fs = require('fs').promises;
const path = require('path');

class MigrationScript {
  constructor() {
    this.backupDir = './backup-legacy';
    this.legacyFiles = [
      'telegramBot.js',
      'discountedItemsUtil.js',
      'cronJobPersistence.js',
      'enhancedDealDiscovery.js',
      'findMaxSaverGaps.js',
      'runner.js',
      'logCleanup.js'
    ];
  }

  async run() {
    console.log('🔄 Starting migration to modular architecture...');
    console.log('');

    try {
      // Step 1: Create backup
      await this.createBackup();

      // Step 2: Show migration plan
      await this.showMigrationPlan();

      // Step 3: Validate new structure
      await this.validateNewStructure();

      // Step 4: Update package.json
      await this.updatePackageJson();

      // Step 5: Create migration guide
      await this.createMigrationGuide();

      console.log('');
      console.log('✅ Migration preparation complete!');
      console.log('');
      console.log('📋 Next Steps:');
      console.log('1. Review the migration guide: ./MIGRATION_GUIDE.md');
      console.log('2. Test the new modular application: node main.js --health');
      console.log('3. Start the application: node main.js');
      console.log('4. Verify all functionality works as expected');
      console.log('5. Remove legacy files when confident: rm -rf backup-legacy/');

    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    }
  }

  async createBackup() {
    console.log('📦 Creating backup of legacy files...');

    try {
      await fs.mkdir(this.backupDir, { recursive: true });

      for (const file of this.legacyFiles) {
        try {
          await fs.access(file);
          await fs.copyFile(file, path.join(this.backupDir, file));
          console.log(`  ✅ Backed up: ${file}`);
        } catch (error) {
          console.log(`  ⚠️ File not found: ${file}`);
        }
      }

      // Backup config if exists
      try {
        await fs.access('config.json');
        await fs.copyFile('config.json', path.join(this.backupDir, 'config.json'));
        console.log('  ✅ Backed up: config.json');
      } catch (error) {
        console.log('  ⚠️ config.json not found');
      }

      console.log('✅ Backup created successfully');
    } catch (error) {
      throw new Error(`Backup failed: ${error.message}`);
    }
  }

  async showMigrationPlan() {
    console.log('');
    console.log('📋 Migration Plan:');
    console.log('');
    console.log('🏗️ New Modular Structure:');
    console.log('  src/');
    console.log('  ├── core/                    # Business logic');
    console.log('  │   ├── dealDiscovery/       # Deal finding logic');
    console.log('  │   ├── cronJobs/            # Cron job management');
    console.log('  │   └── priceGap/            # Price gap analysis');
    console.log('  ├── data/');
    console.log('  │   ├── repositories/        # Database operations');
    console.log('  │   └── models/              # Data models');
    console.log('  ├── services/');
    console.log('  │   ├── api/                 # External API integrations');
    console.log('  │   ├── telegram/            # Telegram bot service');
    console.log('  │   └── cleanup/             # System maintenance');
    console.log('  ├── infrastructure/');
    console.log('  │   ├── database/            # Database connection');
    console.log('  │   ├── config/              # Configuration');
    console.log('  │   └── container/           # Dependency injection');
    console.log('  └── utils/                   # Shared utilities');
    console.log('');
    console.log('🔄 Legacy → Modular Mapping:');
    console.log('  telegramBot.js → src/services/telegram/TelegramService.js');
    console.log('  discountedItemsUtil.js → src/services/api/adapters/ExistingApiAdapter.js');
    console.log('  cronJobPersistence.js → src/data/repositories/CronJobRepository.js');
    console.log('  enhancedDealDiscovery.js → src/core/dealDiscovery/DealDiscoveryService.js');
    console.log('  findMaxSaverGaps.js → src/core/priceGap/PriceGapService.js');
    console.log('  runner.js → src/core/dealDiscovery/DealDiscoveryService.js');
    console.log('  logCleanup.js → src/services/cleanup/CleanupService.js');
  }

  async validateNewStructure() {
    console.log('');
    console.log('🔍 Validating new modular structure...');

    const requiredFiles = [
      'src/Application.js',
      'src/infrastructure/container/Container.js',
      'src/infrastructure/config/ConfigManager.js',
      'src/core/dealDiscovery/DealDiscoveryService.js',
      'src/services/api/DealApiService.js',
      'main.js'
    ];

    let allValid = true;

    for (const file of requiredFiles) {
      try {
        await fs.access(file);
        console.log(`  ✅ ${file}`);
      } catch (error) {
        console.log(`  ❌ Missing: ${file}`);
        allValid = false;
      }
    }

    if (!allValid) {
      throw new Error('Some required files are missing. Please ensure all modular files are created.');
    }

    console.log('✅ New structure validation passed');
  }

  async updatePackageJson() {
    console.log('');
    console.log('📝 Updating package.json...');

    try {
      const packagePath = './package.json';
      let packageJson;

      try {
        const packageData = await fs.readFile(packagePath, 'utf8');
        packageJson = JSON.parse(packageData);
      } catch (error) {
        packageJson = {
          name: "grocery-deal-finder",
          version: "2.0.0",
          description: "Modular grocery deal finder with Telegram notifications"
        };
      }

      // Update scripts
      packageJson.scripts = {
        ...packageJson.scripts,
        start: "node main.js",
        dev: "NODE_ENV=development node main.js",
        health: "node main.js --health",
        stats: "node main.js --stats",
        config: "node main.js --config",
        migrate: "node migrate-to-modular.js"
      };

      // Update main entry point
      packageJson.main = "main.js";
      packageJson.version = "2.0.0";

      await fs.writeFile(packagePath, JSON.stringify(packageJson, null, 2));
      console.log('✅ package.json updated');
    } catch (error) {
      console.log('⚠️ Could not update package.json:', error.message);
    }
  }

  async createMigrationGuide() {
    console.log('');
    console.log('📖 Creating migration guide...');

    const guide = `# Migration Guide: Legacy to Modular Architecture

## Overview

This guide helps you migrate from the legacy monolithic structure to the new modular architecture.

## What Changed

### Architecture Benefits
- **Single Responsibility**: Each module has one clear purpose
- **Dependency Injection**: Clean service dependencies
- **Testability**: Easy to unit test individual components
- **Maintainability**: Clear separation of concerns
- **Scalability**: Easy to add new features

### File Structure
\`\`\`
OLD STRUCTURE:
├── telegramBot.js (2000+ lines)
├── discountedItemsUtil.js
├── cronJobPersistence.js
├── enhancedDealDiscovery.js
└── runner.js

NEW STRUCTURE:
src/
├── core/                    # Business logic
├── data/repositories/       # Database operations
├── services/               # External integrations
├── infrastructure/         # System components
└── utils/                  # Shared utilities
\`\`\`

## Migration Steps

### 1. Backup Verification
Your legacy files have been backed up to \`./backup-legacy/\`

### 2. Configuration
The new system uses \`src/infrastructure/config/ConfigManager.js\` for configuration.
Environment variables are automatically loaded.

### 3. Database
All database operations are now in repository classes:
- \`CronJobRepository\` - Cron job operations
- \`DisplayedItemsRepository\` - Displayed items tracking
- \`NotificationRepository\` - Notification history

### 4. Services
- **DealDiscoveryService** - Core deal finding logic
- **TelegramService** - Telegram bot functionality
- **CleanupService** - System maintenance
- **ApiService** - External API communications

### 5. Starting the Application
\`\`\`bash
# Old way
node telegramBot.js

# New way
node main.js
\`\`\`

## Testing the Migration

### 1. Health Check
\`\`\`bash
node main.js --health
\`\`\`

### 2. Configuration Check
\`\`\`bash
node main.js --config
\`\`\`

### 3. Statistics
\`\`\`bash
node main.js --stats
\`\`\`

### 4. Full Start
\`\`\`bash
node main.js
\`\`\`

## Rollback Plan

If you need to rollback to the legacy system:

1. Stop the new application
2. Restore files from \`./backup-legacy/\`
3. Start the old system: \`node telegramBot.js\`

## Key Differences

### Environment Variables
The new system supports more environment variables:
- \`NODE_ENV\` - Environment (development/production/test)
- \`LOG_LEVEL\` - Logging level
- \`DATABASE_URL\` - Full database connection string
- All existing variables are still supported

### Logging
Enhanced logging with different levels and better formatting.

### Error Handling
Improved error handling with graceful shutdown and recovery.

### Performance
Better performance through:
- Connection pooling
- Efficient dependency injection
- Optimized database queries
- Smart caching

## Support

If you encounter issues:
1. Check the logs for detailed error messages
2. Verify your environment variables
3. Test database connectivity
4. Review the configuration

## Cleanup

Once you're confident the new system works:
\`\`\`bash
rm -rf backup-legacy/
\`\`\`

---

Generated by migration script on ${new Date().toISOString()}
`;

    await fs.writeFile('MIGRATION_GUIDE.md', guide);
    console.log('✅ Migration guide created: MIGRATION_GUIDE.md');
  }
}

// Run migration if called directly
if (require.main === module) {
  const migration = new MigrationScript();
  migration.run().catch(error => {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  });
}

module.exports = MigrationScript;
