#!/usr/bin/env node

/**
 * Runner script for the Discounted Items Utility
 * 
 * This script provides a command-line interface to fetch highly discounted items
 * from Swiggy Instamart based on category and filter.
 * 
 * Usage:
 *   node runner.js --category "Fresh Fruits" --filter "Seasonal Fruits" --threshold 20
 *   node runner.js --code "A1" --threshold 30 --no-cache
 *   node runner.js --category "Fresh Fruits" --threshold 25
 *   node runner.js --help
 */

const discountedItemsUtil = require('./discountedItemsUtil');
const enhancedDealDiscovery = require('./enhancedDealDiscovery');
const mappingUtil = require('./categoryFilterMappingUtil');
const DatabaseSingleton = require('./dbSingleton');
const fs = require('fs');
const path = require('path');

// Load configuration
const config = JSON.parse(fs.readFileSync(path.join(__dirname, 'config.json'), 'utf8'));

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  useCache: config.api.useCache,
  discountThreshold: config.api.defaultDiscountThreshold,
  apiType: 'existing',
  priceCalculation: 'offer',
  hideRepeatedItems: false
};

// Display help message
function showHelp() {
  console.log(`
Discounted Items Utility Runner

Usage:
  node runner.js [options]

Options:
  --category, -c    Category name (e.g., "Fresh Fruits") or comma-separated list
  --filter, -f      Filter name (e.g., "Seasonal Fruits") (optional)
  --code, -m        Mapping code (e.g., "A1" for specific filter, "A" for all filters) or comma-separated list
  --threshold, -t   Discount threshold percentage (default: ${config.api.defaultDiscountThreshold})
  --api-type        API type: 'existing' (default) or 'new' (with Max Saver support)
  --price-calc      Price calculation: 'offer' (default) or 'maxsaver' (requires --api-type new)
  --hide-repeated   Hide items that were already displayed (unless pricing changed)
  --cache           Enable using cached responses (default: ${config.api.useCache ? 'enabled' : 'disabled'})
  --list-categories List all available categories
  --list-filters    List all filters for a category (requires --category)
  --list-codes      List all available mapping codes
  --help, -h        Show this help message

Examples:
  node runner.js --category "Fresh Fruits" --filter "Seasonal Fruits" --threshold 20
  node runner.js --code "A1" --threshold 30 --cache
  node runner.js --code "A" --threshold 25 (all filters in category A)
  node runner.js --code "A,D,K" --threshold 25 (multiple categories)
  node runner.js --code "A1,A2,D15" --threshold 30 (multiple specific filters)
  node runner.js --category "Fresh Fruits" --threshold 25
  node runner.js --code "J" --api-type new --price-calc maxsaver --threshold 20
  node runner.js --code "A1" --api-type new --price-calc offer --threshold 30
  node runner.js --list-categories
  node runner.js --list-filters --category "Fresh Fruits"
  node runner.js --list-codes
  `);
}

// List all categories
function listCategories() {
  const categories = mappingUtil.getAllCategories();
  console.log(`\nAvailable Categories (${categories.length}):\n`);
  categories.forEach((category, index) => {
    console.log(`${index + 1}. ${category}`);
  });
}

// List all filters for a category
function listFilters(categoryName) {
  const filters = mappingUtil.getFiltersForCategory(categoryName);
  console.log(`\nFilters for "${categoryName}" (${filters.length}):\n`);
  filters.forEach((filter, index) => {
    const code = mappingUtil.namesToCode(categoryName, filter);
    console.log(`${index + 1}. ${filter} (Code: ${code})`);
  });
}

// List all mapping codes
function listCodes() {
  const codes = mappingUtil.getAllCodes();
  console.log(`\nAvailable Mapping Codes (${codes.length}):\n`);
  
  // Group codes by category
  const codesByCategory = {};
  codes.forEach(code => {
    const names = mappingUtil.codeToNames(code);
    if (!codesByCategory[names.category]) {
      codesByCategory[names.category] = [];
    }
    codesByCategory[names.category].push({
      code,
      filter: names.filter
    });
  });
  
  // Display codes grouped by category
  Object.keys(codesByCategory).sort().forEach(category => {
    console.log(`\n${category}:`);
    codesByCategory[category].forEach(item => {
      console.log(`  ${item.code}: ${item.filter}`);
    });
  });
}

// Parse arguments
for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  
  switch (arg) {
    case '--help':
    case '-h':
      showHelp();
      process.exit(0);
      break;
      
    case '--category':
    case '-c':
      options.categoryName = args[++i];
      break;
      
    case '--filter':
    case '-f':
      options.filterName = args[++i];
      break;
      
    case '--code':
    case '-m':
      options.code = args[++i];
      break;
      
    case '--threshold':
    case '-t':
      options.discountThreshold = parseInt(args[++i], 10);
      if (isNaN(options.discountThreshold)) {
        console.error('Error: Discount threshold must be a number');
        process.exit(1);
      }
      break;

    case '--api-type':
      const apiType = args[++i];
      if (apiType !== 'existing' && apiType !== 'new') {
        console.error('Error: API type must be "existing" or "new"');
        process.exit(1);
      }
      options.apiType = apiType;
      break;

    case '--price-calc':
      const priceCalc = args[++i];
      if (priceCalc !== 'offer' && priceCalc !== 'maxsaver') {
        console.error('Error: Price calculation must be "offer" or "maxsaver"');
        process.exit(1);
      }
      options.priceCalculation = priceCalc;
      break;

    case '--hide-repeated':
      options.hideRepeatedItems = true;
      break;

    case '--cache':
      options.useCache = true;
      break;
      
    case '--list-categories':
      listCategories();
      process.exit(0);
      break;
      
    case '--list-filters':
      if (!args.includes('--category') && !args.includes('-c')) {
        console.error('Error: --list-filters requires --category option');
        process.exit(1);
      }
      // Will be processed after all arguments are parsed
      break;
      
    case '--list-codes':
      listCodes();
      process.exit(0);
      break;
      
    default:
      if (arg.startsWith('--')) {
        console.error(`Error: Unknown option: ${arg}`);
        showHelp();
        process.exit(1);
      }
  }
}

// Process --list-filters after all arguments are parsed
if (args.includes('--list-filters')) {
  if (!options.categoryName) {
    console.error('Error: --list-filters requires --category option');
    process.exit(1);
  }
  listFilters(options.categoryName);
  process.exit(0);
}

// Validate options
if (!options.categoryName && !options.code) {
  console.error('Error: Either --category or --code is required');
  showHelp();
  process.exit(1);
}

// Validate API options
if (options.priceCalculation === 'maxsaver' && options.apiType !== 'new') {
  console.error('Error: --price-calc maxsaver requires --api-type new');
  process.exit(1);
}

// Format and print items
function printItems(items, apiType, priceCalculation) {
  if (items.length === 0) {
    console.log('No highly discounted items found.');
    return;
  }

  console.log(`\nFound ${items.length} highly discounted items using ${apiType} API${apiType === 'new' ? ` (${priceCalculation} pricing)` : ''}:\n`);
  
  // Sort items by discount percentage (highest first)
  items.sort((a, b) => parseFloat(b.discountPercentage) - parseFloat(a.discountPercentage));
  
  items.forEach((item, index) => {
    console.log(`${index + 1}. ${item.name} (${item.variation})`);
    console.log(`   Original Price: ₹${item.storePrice}`);
    console.log(`   Offer Price: ₹${item.offerPrice}`);

    // Show Max Saver price if available and different from offer price
    if (item.maxSaverPrice && item.maxSaverPrice !== item.offerPrice) {
      console.log(`   Max Saver Price: ₹${item.maxSaverPrice}`);
    }

    // Show which price was used for calculation
    if (item.priceCalculationMethod) {
      const calculationPrice = item.priceCalculationMethod === 'maxsaver' ? item.maxSaverPrice : item.offerPrice;
      console.log(`   Final Price (${item.priceCalculationMethod}): ₹${calculationPrice}`);
    }

    console.log(`   Discount: ${item.discountPercentage}% (Threshold: ${item.appliedThreshold}%)`);
    console.log(`   Quantity: ${item.quantity} ${item.unit}`);
    console.log(`   ID: ${item.id}`);
    console.log(`   URL: ${item.url}`);
    console.log('');
  });
}

// Main function
async function main() {
  const dbPersistence = await DatabaseSingleton.getInstance();

  try {
    console.log('Fetching highly discounted items...');
    console.log(`Options: ${JSON.stringify(options, null, 2)}`);

    const startTime = Date.now();
    let allItems = [];

    // Generate unique run ID
    const runId = `deal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    console.log(`📊 Run ID: ${runId}`);

    // Handle multiple codes if provided
    if (options.code && options.code.includes(',')) {
      const codes = options.code.split(',').map(code => code.trim());
      console.log(`Processing ${codes.length} codes: ${codes.join(', ')}`);

      for (let i = 0; i < codes.length; i++) {
        const code = codes[i];
        console.log(`\n--- Processing code ${i + 1}/${codes.length}: ${code} ---`);

        const codeOptions = {
          ...options,
          code: code,
          apiType: options.apiType,
          priceCalculation: options.priceCalculation
        };
        const items = await enhancedDealDiscovery.getHighlyDiscountedItems(codeOptions);
        allItems = allItems.concat(items);

        console.log(`Found ${items.length} items for code ${code}`);
      }

      // Remove duplicates based on item ID
      const uniqueItems = [];
      const seenIds = new Set();
      for (const item of allItems) {
        if (!seenIds.has(item.id)) {
          seenIds.add(item.id);
          uniqueItems.push(item);
        }
      }

      allItems = uniqueItems;
    } else {
      // Single code/category processing
      const enhancedOptions = {
        ...options,
        apiType: options.apiType,
        priceCalculation: options.priceCalculation
      };
      allItems = await enhancedDealDiscovery.getHighlyDiscountedItems(enhancedOptions);
    }

    const endTime = Date.now();
    const executionTimeMs = endTime - startTime;

    printItems(allItems, options.apiType, options.priceCalculation);

    // Save to database
    const codesString = options.code || (options.category ? `category:${options.category}` : 'unknown');
    const runData = {
      runId,
      codes: codesString,
      threshold: options.discountThreshold,
      apiType: options.apiType,
      priceCalculation: options.priceCalculation,
      totalItemsFound: allItems.length,
      executionTimeMs,
      status: 'completed',
      completedAt: new Date()
    };

    await dbPersistence.saveDealFinderRun(runData);

    // Save individual results to database
    if (allItems.length > 0) {
      const results = allItems.map(item => ({
        code: options.code || null,
        categoryName: item.category || options.category || null,
        filterName: item.filter || options.filter || null,
        name: item.name,
        variation: item.variation,
        id: item.id,
        storePrice: item.storePrice,
        offerPrice: item.offerPrice,
        discountPercentage: item.discountPercentage,
        quantity: item.quantity,
        unit: item.unit,
        url: item.url,
        appliedThreshold: item.appliedThreshold,
        inStock: item.inStock === true
      }));

      await dbPersistence.saveDealFinderResults(runId, results);
    }

    console.log(`\n📊 Total items found: ${allItems.length}`);
    console.log(`⏱️ Execution time: ${(executionTimeMs / 1000).toFixed(2)}s`);
    console.log(`🗄️ Saved to database with run ID: ${runId}`);

  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  } finally {
    await dbPersistence.close();
  }
}

// Run the main function
main();
