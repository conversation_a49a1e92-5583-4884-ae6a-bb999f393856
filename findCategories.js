/**
 * Finds all unique categories from the provided JSON data
 * @param {Object|string} jsonData - The JSON data or path to JSON file
 * @returns {string[]} - Array of unique categories
 */
function findCategories(jsonData) {
  // Parse JSON if it's a string
  const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
  
  // Set to store unique categories
  const categories = new Set();
  
  // Function to recursively search for category fields
  function searchForCategories(obj) {
    // Base case: if obj is null or not an object, return
    if (!obj || typeof obj !== 'object') return;
    
    // Check if current object has a category property
    if (obj.hasOwnProperty('category') && typeof obj.category === 'string') {
      categories.add(obj.category);
    }
    
    // Recursively search in all properties
    for (const key in obj) {
      // Skip if property is null or undefined
      if (obj[key] == null) continue;
      
      // If property is an array, search in each element
      if (Array.isArray(obj[key])) {
        obj[key].forEach(item => searchForCategories(item));
      } 
      // If property is an object, search in it
      else if (typeof obj[key] === 'object') {
        searchForCategories(obj[key]);
      }
    }
  }
  
  // Start the search
  searchForCategories(data);
  
  // Convert Set to Array and return
  return Array.from(categories);
}

/**
 * Extracts a structured representation of sections and categories from the provided JSON data
 * @param {Object|string} jsonData - The JSON data or path to JSON file
 * @returns {Object} - Object with sections as keys and arrays of category objects as values
 */
function extractCategoryStructure(jsonData) {
  // Parse JSON if it's a string
  const data = typeof jsonData === 'string' ? JSON.parse(jsonData) : jsonData;
  
  // Object to store the structured categories
  const structure = {};
  
  // Check if the data has the expected structure
  if (!data || !data.data || !data.data.cards || !Array.isArray(data.data.cards)) {
    console.warn('JSON data does not have the expected structure');
    return structure;
  }
  
  // Process each card (section)
  data.data.cards.forEach(cardObj => {
    // Skip if card doesn't have the expected structure
    if (!cardObj.card || !cardObj.card.card) return;
    
    const card = cardObj.card.card;
    
    // Get section name from header title
    const sectionName = card.header && card.header.title ? card.header.title : 'Unknown Section';
    
    // Skip "SHOP BY STORE" section as per requirements
    if (sectionName === "SHOP BY STORE") return;
    
    // Initialize array for this section if it doesn't exist
    if (!structure[sectionName]) {
      structure[sectionName] = [];
    }
    
    // Check if gridElements exists and has the expected structure
    if (card.gridElements && card.gridElements.infoWithStyle && 
        card.gridElements.infoWithStyle.info && Array.isArray(card.gridElements.infoWithStyle.info)) {
      
      // Process each category in the info array
      card.gridElements.infoWithStyle.info.forEach(item => {
        // Skip categories without descriptions
        if (!item.description) return;
        
        // Extract category information
        const categoryInfo = {
          id: item.id || '',
          description: item.description || '',
          link: (item.action && item.action.link) ? item.action.link : ''
        };
        
        // Add to the section's categories
        structure[sectionName].push(categoryInfo);
      });
    }
  });
  
  // Filter out sections with no categories
  const filteredStructure = {};
  Object.keys(structure).forEach(sectionName => {
    if (structure[sectionName].length > 0) {
      filteredStructure[sectionName] = structure[sectionName];
    }
  });
  
  return filteredStructure;
}

/**
 * Example usage with file system (Node.js environment):
 * 
 * const fs = require('fs');
 * const path = require('path');
 * 
 * // Path to the JSON file
 * const filePath = path.join(__dirname, 'data', 'category2.json');
 * 
 * // Read the file
 * const jsonData = fs.readFileSync(filePath, 'utf8');
 * 
 * // Find all categories
 * const categories = findCategories(jsonData);
 * 
 * // Display the results
 * console.log('Found', categories.length, 'unique categories:');
 * console.log(categories);
 * 
 * // Extract structured categories
 * const structure = extractCategoryStructure(jsonData);
 * console.log('Category structure:');
 * console.log(JSON.stringify(structure, null, 2));
 */

// For browser environment:
// This function can be used in a browser by passing a JSON object directly
// or by fetching the JSON file and passing the response text

/**
 * Example usage in browser:
 * 
 * fetch('data/category2.json')
 *   .then(response => response.json())
 *   .then(data => {
 *     const categories = findCategories(data);
 *     console.log('Found', categories.length, 'unique categories:');
 *     console.log(categories);
 *     
 *     const structure = extractCategoryStructure(data);
 *     console.log('Category structure:');
 *     console.log(structure);
 *   })
 *   .catch(error => console.error('Error loading JSON:', error));
 */

// Export the function if using modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { findCategories, extractCategoryStructure };
}