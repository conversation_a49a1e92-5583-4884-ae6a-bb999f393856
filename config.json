{"api": {"requestDelay": 3000, "useCache": false, "defaultDiscountThreshold": 75}, "filtering": {"blacklistKeywords": [], "customDiscountThresholds": {"desidiya": 90, "Kuber": 80, "bosco": 80}}, "urls": {"instamartItemBase": "https://www.swiggy.com/stores/instamart/item/", "shareParam": "?share=true", "storeId": "1403687"}, "cron": {"defaultInterval": 10, "maxActiveJobs": 20, "defaultThreshold": 60, "notificationLimit": 10}}