const fs = require('fs');
const path = require('path');
const https = require('https');
const querystring = require('querystring');

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function transformUrl(appLink) {
  if (!appLink) return null;
  
  let url;
  let params = {};
  
  if (appLink.includes('ageConsent?url=')) {
    const encodedUrl = appLink.split('ageConsent?url=')[1];
    const decodedUrl = decodeURIComponent(encodedUrl);
    url = decodedUrl.replace('swiggy://stores/instamart', 'https://www.swiggy.com/api/instamart');
    
    const queryString = url.split('?')[1];
    const searchParams = new URLSearchParams(queryString);
    
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
  } else {
    url = appLink.replace('swiggy://stores/instamart', 'https://www.swiggy.com/api/instamart');
    
    const queryString = url.split('?')[1];
    const searchParams = new URLSearchParams(queryString);
    
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
  }
  
  const storeId = params.storeId || '1403687';
  
  params.primaryStoreId = storeId;
  params.secondaryStoreId = '';
  
  delete params.showAgeConsent;
  
  const baseUrl = 'https://www.swiggy.com/api/instamart/category-listing';
  return `${baseUrl}?${querystring.stringify(params)}`;
}

function fetchData(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    }).on('error', (error) => {
      reject(new Error(`Failed to fetch data: ${error.message}`));
    });
  });
}

function extractFilters(data) {
  if (!data || !data.data || !data.data.filters || !Array.isArray(data.data.filters)) {
    return [];
  }
  
  return data.data.filters.map(filter => ({
    name: filter.name,
    id: filter.id,
    type: filter.type,
    productCount: filter.productCount
  }));
}

async function fetchAllCategories() {
  try {
    const structurePath = path.join(__dirname, 'category_structure.json');
    const structureData = JSON.parse(fs.readFileSync(structurePath, 'utf8'));
    
    const outputDir = path.join(__dirname, 'category_data');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir);
    }
    
    const filtersDir = path.join(__dirname, 'category_filters');
    if (!fs.existsSync(filtersDir)) {
      fs.mkdirSync(filtersDir);
    }
    
    const allCategories = [];
    Object.keys(structureData).forEach(section => {
      structureData[section].forEach(category => {
        allCategories.push(category);
      });
    });
    
    console.log(`Found ${allCategories.length} categories to process`);
    
    for (let i = 0; i < allCategories.length; i++) {
      const category = allCategories[i];
      const apiUrl = transformUrl(category.link);
      
      if (!apiUrl) {
        console.log(`Skipping category ${category.description} - could not transform URL`);
        continue;
      }
      
      console.log(`[${i+1}/${allCategories.length}] Fetching data for ${category.description}`);
      
      try {
        const data = await fetchData(apiUrl);
        
        const outputFile = path.join(outputDir, `${category.id}.json`);
        fs.writeFileSync(outputFile, JSON.stringify(data, null, 2), 'utf8');
        console.log(`Saved data to ${outputFile}`);
        
        const filters = extractFilters(data);
        const filtersFile = path.join(filtersDir, `${category.id}_filters.json`);
        fs.writeFileSync(filtersFile, JSON.stringify(filters, null, 2), 'utf8');
        console.log(`Saved ${filters.length} filters to ${filtersFile}`);
        
        if (i < allCategories.length - 1) {
          console.log(`Waiting 10 seconds before next request...`);
          await sleep(10000);
        }
      } catch (error) {
        console.error(`Error processing ${category.description}: ${error.message}`);
      }
    }
    
    console.log('All categories processed successfully');
  } catch (error) {
    console.error(`Failed to process categories: ${error.message}`);
  }
}

fetchAllCategories();