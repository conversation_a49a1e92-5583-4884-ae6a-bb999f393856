const fs = require('fs');
const path = require('path');
const querystring = require('querystring');

function transformUrl(appLink) {
  if (!appLink) return null;
  
  let url;
  let params = {};
  
  if (appLink.includes('ageConsent?url=')) {
    const encodedUrl = appLink.split('ageConsent?url=')[1];
    const decodedUrl = decodeURIComponent(encodedUrl);
    url = decodedUrl.replace('swiggy://stores/instamart', 'https://www.swiggy.com/api/instamart');
    
    const queryString = url.split('?')[1];
    const searchParams = new URLSearchParams(queryString);
    
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
  } else {
    url = appLink.replace('swiggy://stores/instamart', 'https://www.swiggy.com/api/instamart');
    
    const queryString = url.split('?')[1];
    const searchParams = new URLSearchParams(queryString);
    
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
  }
  
  const storeId = params.storeId || '1403687';
  
  params.primaryStoreId = storeId;
  params.secondaryStoreId = '';
  
  delete params.showAgeConsent;
  
  const baseUrl = 'https://www.swiggy.com/api/instamart/category-listing';
  return `${baseUrl}?${querystring.stringify(params)}`;
}

function extractFilters(data) {
  if (!data || !data.data || !data.data.filters || !Array.isArray(data.data.filters)) {
    return [];
  }
  
  return data.data.filters.map(filter => ({
    name: filter.name,
    id: filter.id,
    type: filter.type,
    productCount: filter.productCount
  }));
}

function generateFilterUrls(categoryId, filters) {
  const urls = [];
  
  filters.forEach(filter => {
    const url = `https://www.swiggy.com/api/instamart/category-listing/filter?filterId=${filter.id}&offset=0&storeId=1403687&primaryStoreId=1403687&secondaryStoreId=&type=${encodeURIComponent(filter.type)}&pageNo=0&limit=20&filterName=${encodeURIComponent(filter.name)}&categoryName=${encodeURIComponent(filter.name)}`;
    
    urls.push({
      filterId: filter.id,
      filterName: filter.name,
      url: url
    });
  });
  
  return urls;
}

function analyzeFilters() {
  try {
    // Read the fruits.json file
    const fruitsPath = path.join(__dirname, 'data', 'fruits.json');
    const fruitsData = JSON.parse(fs.readFileSync(fruitsPath, 'utf8'));
    
    // Extract filters
    const filters = extractFilters(fruitsData);
    
    console.log(`Found ${filters.length} filters in fruits.json:`);
    filters.forEach((filter, index) => {
      console.log(`${index + 1}. ${filter.name} (ID: ${filter.id}, Type: ${filter.type}, Products: ${filter.productCount})`);
    });
    
    // Generate filter URLs
    const categoryId = fruitsData.data.selectedCategoryId || '';
    const categoryName = fruitsData.data.selectedCategoryName || '';
    
    console.log(`\nCategory: ${categoryName} (ID: ${categoryId})`);
    
    const filterUrls = generateFilterUrls(categoryId, filters);
    
    console.log(`\nGenerated ${filterUrls.length} filter URLs:`);
    filterUrls.forEach((item, index) => {
      console.log(`\n${index + 1}. Filter: ${item.filterName} (ID: ${item.filterId})`);
      console.log(`   URL: ${item.url}`);
    });
    
    // Read category structure
    const structurePath = path.join(__dirname, 'category_structure.json');
    const structureData = JSON.parse(fs.readFileSync(structurePath, 'utf8'));
    
    // Generate API URLs for all categories
    console.log('\n\nAPI URLs for all categories:');
    
    const allCategories = [];
    Object.keys(structureData).forEach(section => {
      console.log(`\nSection: ${section}`);
      
      structureData[section].forEach(category => {
        const apiUrl = transformUrl(category.link);
        console.log(`- ${category.description} (ID: ${category.id})`);
        console.log(`  URL: ${apiUrl}`);
        
        allCategories.push({
          section: section,
          id: category.id,
          description: category.description,
          apiUrl: apiUrl
        });
      });
    });
    
    // Save the results
    const outputDir = path.join(__dirname, 'analysis');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir);
    }
    
    // Save filters
    const filtersFile = path.join(outputDir, 'filters.json');
    fs.writeFileSync(filtersFile, JSON.stringify(filters, null, 2), 'utf8');
    console.log(`\nSaved filters to ${filtersFile}`);
    
    // Save filter URLs
    const filterUrlsFile = path.join(outputDir, 'filter_urls.json');
    fs.writeFileSync(filterUrlsFile, JSON.stringify(filterUrls, null, 2), 'utf8');
    console.log(`Saved filter URLs to ${filterUrlsFile}`);
    
    // Save category URLs
    const categoryUrlsFile = path.join(outputDir, 'category_urls.json');
    fs.writeFileSync(categoryUrlsFile, JSON.stringify(allCategories, null, 2), 'utf8');
    console.log(`Saved category URLs to ${categoryUrlsFile}`);
    
    console.log('\nAnalysis completed successfully!');
  } catch (error) {
    console.error(`Analysis failed: ${error.message}`);
  }
}

analyzeFilters();